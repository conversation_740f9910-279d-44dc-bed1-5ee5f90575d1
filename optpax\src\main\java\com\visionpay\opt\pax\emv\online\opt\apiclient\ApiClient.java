package com.visionpay.opt.pax.emv.online.opt.apiclient;

import com.sankuai.waimai.router.annotation.RouterService;
import com.visionpay.opt.pax.BuildConfig;
import com.visionpay.opt.pax.emv.online.opt.IOPTApiService;

import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

@RouterService(interfaces = IApiClient.class, singleton = true)
public class ApiClient implements IApiClient {

    private IOPTApiService OPTApiService;

    public IOPTApiService getOPTApiService(){
        if(OPTApiService != null)
            return OPTApiService;

        OkHttpClient.Builder httpClient = new OkHttpClient.Builder();
        /*httpClient.addInterceptor(chain -> {
            Request original = chain.request();
            return chain.proceed(original);
        });*/

        OPTApiService = (new Retrofit
                .Builder())
                .baseUrl(BuildConfig.opt_api)
                .addConverterFactory(GsonConverterFactory.create())
                .client(httpClient.build())
                .build()
                .create(IOPTApiService.class);

        return OPTApiService;
    }
}
