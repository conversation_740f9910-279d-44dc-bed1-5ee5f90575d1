package com.pax.emvservice.emv.security;

import com.pax.commonlib.utils.ConvertUtils;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.security.IDukpt;
import com.sankuai.waimai.router.annotation.RouterService;

import java.util.Random;

@RouterService(interfaces = IDukpt.class,key = EmvServiceConstant.EMVSERVICE_DUKPT)
public class DukptVariant implements IDukpt {
    private BitSet _keyRegisterBitmask;
    private BitSet _dataVariantBitmask;

    private final byte[] _bdkTest = new byte[] { (byte)0xfe, (byte)0xef, (byte)0xdc, (byte)0xcd, (byte)0xba, (byte)0xab, (byte)0x98, (byte)0x89, 0x76, 0x67, 0x54, 0x45, 0x32, 0x23, 0x10, 0x01 };//getKey(keyFile);
    //private final byte[] _bdkProd = new byte[] { (byte)0x7C, (byte)0xA1, (byte)0x53, (byte)0x89, (byte)0x4F, (byte)0xEF, (byte)0x8D, (byte)0x2C, (byte)0x10, (byte)0x94, (byte)0x47, (byte)0x62, (byte)0x1B, (byte)0x0F, (byte)0xBD, (byte)0xAE };

    /**
     * <p>Creates a standard DUKPT variant object with the PIN variant bitmask.
     */
    public DukptVariant() {
        this(Dukpt.KEY_REGISTER_BITMASK, Dukpt.PIN_VARIANT_BITMASK);
    }

    /**
     * <p>Creates a DUKPT variant object with the provided key register bitmask (typically {@link Dukpt#KEY_REGISTER_BITMASK})
     * and data variant bitmask, depending on the type of key derivation desired.
     *
     * @param keyRegisterBitmaskHex
     * @param dataVariantBitmaskHex
     */
    public DukptVariant(final String keyRegisterBitmaskHex, final String dataVariantBitmaskHex) {
        this(Dukpt.toByteArray(keyRegisterBitmaskHex), Dukpt.toByteArray(dataVariantBitmaskHex));
    }

    /**
     * <p>Creates a DUKPT variant object with the provided key register bitmask (typically {@link Dukpt#KEY_REGISTER_BITMASK})
     * and data variant bitmask, depending on the type of key derivation desired.
     *
     * @param keyRegisterBitmask
     * @param dataVariantBitmask
     */
    public DukptVariant(final byte[] keyRegisterBitmask, final byte[] dataVariantBitmask) {
        this(Dukpt.toBitSet(keyRegisterBitmask), Dukpt.toBitSet(dataVariantBitmask));
    }

    /**
     * <p>Creates a DUKPT variant object with the provided key register bitmask (typically {@link Dukpt#KEY_REGISTER_BITMASK})
     * and data variant bitmask, depending on the type of key derivation desired.
     *
     * @param keyRegisterBitmask
     * @param dataVariantBitmask
     */
    public DukptVariant(final BitSet keyRegisterBitmask, final BitSet dataVariantBitmask) {
        this._keyRegisterBitmask = keyRegisterBitmask;
        this._dataVariantBitmask = dataVariantBitmask;
    }

    /**
     * <p>Computes a DUKPT (Derived Unique Key-Per-Transaction).
     *
     * @see Dukpt#computeKey(byte[], byte[])
     * @param baseDerivationKey
     * @param keySerialNumber
     * @return
     * @throws Exception
     */
    public byte[] computeKey(byte[] baseDerivationKey, byte[] keySerialNumber) throws Exception {
        return Dukpt.computeKey(baseDerivationKey, keySerialNumber, _keyRegisterBitmask, _dataVariantBitmask);
    }

    /**
     * <p>Computes the Initial PIN Encryption Key (Sometimes referred to as
     * the Initial PIN Entry Device Key).
     *
     * @see Dukpt#getIpek(BitSet, BitSet)
     * @param key
     * @param ksn
     * @return
     * @throws Exception
     */
    public BitSet getIpek(BitSet key, BitSet ksn) throws Exception {
        return Dukpt.getIpek(key, ksn, _keyRegisterBitmask);
    }

    /**
     * <p>Converts the provided derived key into a "data key".</p>
     *
     * @see Dukpt#toDataKey(byte[])
     * @param derivedKey
     * @return
     * @throws Exception
     */
    public byte[] toDataKey(byte[] derivedKey) throws Exception {
        return Dukpt.toDataKey(derivedKey);
    }

    public byte[] encryptPin(byte[] pin, byte[] ksn, String environment, boolean forceSoftEncrypt) throws Exception {
        //byte[] key = computeKey(_bdk, ksn);
        byte[] key;
        //if(environment.equals("Production"))
        //    key = computeKey(_bdkProd, ksn);
        //else
            key = computeKey(_bdkTest, ksn);

        Random r = new Random();
        String alphabet = "0123456789ABCDEF";
        String pinStruct = "1"  + pin.length + (new String(pin));
        for (int i = 0; i < (16 - pin.length - 2) ; i++) {
            pinStruct += alphabet.charAt(r.nextInt(alphabet.length()));
        }
        byte[] mountPin = ConvertUtils.strToBcd(pinStruct, ConvertUtils.EPaddingPosition.PADDING_LEFT);

        byte[] cipher = Dukpt.encryptTripleDes(key, mountPin);

        byte[] retPin = new byte[cipher.length + ksn.length];
        System.arraycopy(cipher, 0, retPin, 0, cipher.length);
        System.arraycopy(ksn, 0, retPin, cipher.length, ksn.length);
        return retPin;
    }
}
