<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.visionpay.opt.pax">

    <uses-permission android:name="android.permission.INTERNET" />
    <!--uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.WAKE_LOCK" /-->
    <uses-permission android:name="com.pax.permission.MAGCARD" />
    <uses-permission android:name="com.pax.permission.PED" />
    <uses-permission android:name="com.pax.permission.ICC" />
    <uses-permission android:name="com.pax.permission.PICC" />
    <uses-permission android:name="com.pax.permission.PRINTER" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application
        android:name="com.visionpay.opt.pax.app.App"
        android:allowBackup="true"
        android:icon="@mipmap/app_logo"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:theme="@style/AppTheme"
        tools:replace="android:label,android:allowBackup,android:theme">

        <!--App secret for PaxStore-->
        <!--meta-data android:name="PAXSTORE_APP_KEY" android:value="PSLTC7IXBIAYZPSM44XD" />
        <meta-data android:name="PAXSTORE_APP_SECRET" android:value="VZZJ04RLCSGMLNFER9T6C4LCMZCWXF1P3NJ43O3T" /-->
        <!--App secret for MaxStore-->
        <meta-data android:name="PAXSTORE_APP_KEY" android:value="QK3RVYBNWD13WS6ROGKA" />
        <meta-data android:name="PAXSTORE_APP_SECRET" android:value="EHNJ3QEEFW8GK2QFKYPQYVA6KDLT8F018J4HE99G" />

        <receiver
            android:name=".receiver.StartupOnBootUpReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <!--action android:name="android.intent.action.BOOT" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" /-->
            </intent-filter>
        </receiver>

        <service android:name=".service.DownloadParamService">
            <intent-filter>
                <action android:name="com.sdk.service.ACTION_TO_DOWNLOAD_PARAMS"/>
                <category android:name="${applicationId}"/>
            </intent-filter>
        </service>
        <service android:name=".service.MessageService" android:exported="true">
        </service>

        <activity
            android:name=".activity.ErrorActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OPTPAX.Fullscreen" />
        <activity
            android:name=".activity.SuccessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OPTPAX.Fullscreen" />
        <activity
            android:name=".activity.SuccessInternalActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OPTPAX.Fullscreen" />
        <activity
            android:name=".activity.MagReadCardActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OPTPAX.Fullscreen" />
        <activity
            android:name=".activity.ReceiptReadCardActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OPTPAX.Fullscreen" />
        <activity
            android:name=".activity.ReadCardActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OPTPAX.Fullscreen" />
        <activity
            android:name=".activity.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.OPTPAX.Fullscreen" />
        <activity
            android:name=".activity.TransActivity"
            android:exported="false" />
        <activity
            android:name=".activity.ConfigActivity"
            android:exported="false"
            android:launchMode="singleTask" />
        <activity
            android:name=".activity.SplashActivity"
            android:exported="true"
            android:theme="@style/TransBgTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.AdminActivity"
            android:exported="false" />
    </application>

</manifest>