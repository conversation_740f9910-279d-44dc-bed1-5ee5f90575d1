/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/13                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.annotation.Nullable;
import com.pax.bizentity.db.helper.PaywaveAidDbHelper;
import com.pax.bizentity.entity.clss.paywave.PaywaveAidBean;

/**
 * Contactless PayWave AID DataStore.
 */
public class PayWaveAidDataStore extends BaseEmvParamDataStore<PayWaveAidDataStore> {
    public static final String APP_NAME = "APP_NAME";
    public static final String TTQ = "TTQ";
    public static final String SEC_CAP = "SEC_CAP";

    @Nullable
    private final PaywaveAidBean aid;

    public PayWaveAidDataStore(@Nullable PaywaveAidBean aid) {
        this.aid = aid;
    }

    @Override
    public void putString(String key, @Nullable String value) {
        if (aid == null) {
            return;
        }
        switch (key) {
            case TTQ:
                aid.setTtq(value);
                break;
            case SEC_CAP:
                aid.setSecurityCapability(value);
                break;
            default: return;
        }
        PaywaveAidDbHelper.getInstance().update(aid);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Nullable
    @Override
    public String getString(String key, @Nullable String defValue) {
        if (aid != null) {
            switch (key) {
                case APP_NAME: return aid.getAppName();
                case TTQ: return aid.getTtq();
                case SEC_CAP: return aid.getSecurityCapability();
                default: return defValue;
            }
        }
        return defValue;
    }
}
