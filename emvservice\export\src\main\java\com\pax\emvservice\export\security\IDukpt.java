package com.pax.emvservice.export.security;

public interface IDukpt {

    /**
     * <p>Computes a DUKPT (Derived Unique Key-Per-Transaction).
     *
     * @param baseDerivationKey
     * @param keySerialNumber
     * @return
     * @throws Exception
     */
    byte[] computeKey(byte[] baseDerivationKey, byte[] keySerialNumber) throws Exception;

    /**
     * <p>Computes the Initial PIN Encryption Key (Sometimes referred to as
     * the Initial PIN Entry Device Key).
     *
     * @param key
     * @param ksn
     * @return
     * @throws Exception
     */
    //BitSet getIpek(BitSet key, BitSet ksn) throws Exception;

    /**
     * <p>Converts the provided derived key into a "data key".</p>
     *
     * @param derivedKey
     * @return
     * @throws Exception
     */
    byte[] toDataKey(byte[] derivedKey) throws Exception;

    /**
     * Performs Pin Encryption Interchange Pattern
     * @param pin
     * @param ksn
     * @return
     * @throws Exception
     */
    byte[] encryptPin(byte[] pin, byte[] ksn, String environment, boolean forceSoftEncrypt) throws Exception;
}
