/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.callback;

import android.widget.EditText;
import androidx.annotation.NonNull;

/**
 * A callback to be executed when {@code EditText} is bound. When the user clicks {@code
 * EditTextPreference} and a dialog with {@code EditText} pops up, this callback will be
 * executed, and this {@code EditText} instance will be passed in via the {@code onBind()}
 * method. You can use this {@code EditText} to perform some setting operations.
 * <br><br>
 * 绑定 {@code EditText} 时执行的回调。当用户点击 {@code EditTextPreference}，弹出带有 {@code EditText}
 * 的对话框时，这个回调将会被执行，并通过 {@code onBind()} 方法传入这个 {@code EditText} 实例。 你可以利用这个
 * {@code EditText} 来进行一些设置操作。
 */
public interface BindEditTextCallback {
    void onBind(@NonNull EditText editText);
}
