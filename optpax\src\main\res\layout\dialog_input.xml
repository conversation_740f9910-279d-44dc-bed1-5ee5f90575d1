<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/31                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/dialog_padding_horizontal"
    android:paddingTop="@dimen/dialog_padding_vertical"
    android:paddingBottom="@dimen/dialog_padding_vertical_small"
    android:background="@android:color/transparent">

    <TextView
        android:id="@+id/input_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textSize="@dimen/dialog_title_text"
        android:textStyle="bold"
        android:textColor="@color/main_text"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="Dialog Title"/>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/input_text_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dialog_content_spacing_vertical"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        app:boxCornerRadiusTopStart="@dimen/dialog_input_box_corner_radius"
        app:boxCornerRadiusTopEnd="@dimen/dialog_input_box_corner_radius"
        app:boxCornerRadiusBottomStart="@dimen/dialog_input_box_corner_radius"
        app:boxCornerRadiusBottomEnd="@dimen/dialog_input_box_corner_radius"
        app:layout_constraintTop_toBottomOf="@id/input_title">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/input_text_field"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:inputType="textMultiLine"
            tools:text="12345" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/input_positive_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/main_highlight_button"
        android:minWidth="@dimen/text_button_min_width"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        app:iconTint="@color/main_highlight_button"
        app:layout_constraintTop_toBottomOf="@id/input_text_layout"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="OK" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/input_negative_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="16dp"
        android:textColor="@color/main_highlight_button"
        android:minWidth="@dimen/text_button_min_width"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        app:iconTint="@color/main_highlight_button"
        app:layout_constraintTop_toBottomOf="@id/input_text_layout"
        app:layout_constraintEnd_toStartOf="@id/input_positive_button"
        tools:text="CANCEL" />

</androidx.constraintlayout.widget.ConstraintLayout>