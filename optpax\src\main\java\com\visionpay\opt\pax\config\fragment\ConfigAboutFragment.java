/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/11                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.app.App;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.IEmvVersionService;
import com.sankuai.waimai.router.Router;

import java.util.LinkedList;
import java.util.List;

/**
 * About page
 *
 * Settings -> About
 */
public class ConfigAboutFragment extends Fragment {
    private TextView appVer;
    private RecyclerView emvKernelVerList;
    private final IEmvVersionService emvVersionService = Router.getService(IEmvVersionService.class, EmvServiceConstant.EMVSERVICE_EMV_VERSION);

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_about, container, false);
        appVer = view.findViewById(R.id.about_app_version);
        emvKernelVerList = view.findViewById(R.id.about_kernel_version_list);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        appVer.setText(App.getVersion());

        List<VersionInfo> emvVersionList = new LinkedList<>();
        emvVersionList.add(new VersionInfo("AE Version", emvVersionService.getAmexVersion().trim()));
        emvVersionList.add(new VersionInfo("DPAS Version", emvVersionService.getDpasVersion().trim()));
        emvVersionList.add(new VersionInfo("EFT Version", emvVersionService.getEFTVersion().trim()));
        emvVersionList.add(new VersionInfo("EMV Version", emvVersionService.getEmvVersion().trim()));
        emvVersionList.add(new VersionInfo("ENTRY Version", emvVersionService.getEntryVersion().trim()));
        emvVersionList.add(new VersionInfo("JCB Version", emvVersionService.getJcbVersion().trim()));
        emvVersionList.add(new VersionInfo("MC Version", emvVersionService.getPayPassVersion().trim()));
        emvVersionList.add(new VersionInfo("MIR Version", emvVersionService.getMIRVersion().trim()));
        emvVersionList.add(new VersionInfo("PURE Version", emvVersionService.getPureVersion().trim()));
        emvVersionList.add(new VersionInfo("QPBOC Version", emvVersionService.getPbocVersion().trim()));
        emvVersionList.add(new VersionInfo("RuPay Version", emvVersionService.getRupayVersion().trim()));
        emvVersionList.add(new VersionInfo("WAVE Version", emvVersionService.getPayWaveVersion().trim()));

        emvKernelVerList.setAdapter(new Adapter(emvVersionList));
        emvKernelVerList.setLayoutManager(new LinearLayoutManager(getContext()));
    }

    private static class VersionInfo {
        private final String title;
        private final String content;

        public VersionInfo(String title, String content) {
            this.title = title;
            this.content = content;
        }

        public String getTitle() {
            return title;
        }

        public String getContent() {
            return content;
        }
    }

    public static class Adapter extends RecyclerView.Adapter<ViewHolder> {
        private List<VersionInfo> versionInfoList;

        public Adapter(List<VersionInfo> versionInfoList) {
            this.versionInfoList = versionInfoList;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_emv_version_content, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            VersionInfo info = versionInfoList.get(position);
            holder.titleTextView.setText(info.getTitle());
            holder.contentTextView.setText(info.getContent());
        }

        @Override
        public int getItemCount() {
            return versionInfoList.size();
        }
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        public TextView titleTextView;
        public TextView contentTextView;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            titleTextView = itemView.findViewById(R.id.emv_version_item_title);
            contentTextView = itemView.findViewById(R.id.emv_version_item_content);
        }
    }
}
