package com.pax.poslib.neptune;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import com.pax.commonlib.application.BaseApplication;
import com.pax.dal.IComm;
import com.pax.dal.entity.EUartPort;
import com.pax.dal.entity.UartParam;
import com.pax.dal.exceptions.CommException;

public class UartComm {

    private static final String TAG = UartComm.class.getName();

    private static UartComm commTester;

    private IComm uartComm;

    private UartCommListener listener;

    static ReceiveThread receiveThread;
    private Handler handler = new Handler(Looper.getMainLooper()) {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 0:
                    if(listener != null)
                        listener.OnCommMessageReceive(msg.obj.toString());
                    break;

                default:
                    break;
            }
        };
    };

    private UartComm() {
        UartParam uartParam = new UartParam();
        uartParam.setPort(EUartPort.USBDEV);
        uartParam.setAttr("9600,8,n,1");

        uartComm = Sdk.getInstance().getDal(BaseApplication.getAppContext()).getCommManager().getUartComm(uartParam);
        Log.i(TAG, "getUartComm" + uartParam.getPort().toString());
    }

    public static UartComm getInstance() {
        if (commTester == null) {
            commTester = new UartComm();
        }
        return commTester;
    }

    public void start(UartCommListener listener){
        this.listener = listener;
        if (receiveThread == null
                || (receiveThread != null
                && (receiveThread.getState() == Thread.State.TERMINATED
                || receiveThread.isInterrupted()))) {
            receiveThread = new ReceiveThread();
        }
        if(receiveThread.getState() == Thread.State.NEW){
            receiveThread.start();
        }
    }

    public void stop(){
        if (receiveThread != null) {
            receiveThread.interrupt();
        }
    }

    private void connect() {
        if (uartComm != null) {
            try {
                if (uartComm.getConnectStatus() == IComm.EConnectStatus.DISCONNECTED) {
                    uartComm.connect();
                    Log.i(TAG, "Connect");
                } else {
                    Log.i(TAG, "have connected");
                }
            } catch (CommException e) {
                Log.e(TAG, "Connect " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public void send(byte[] data) {
        if (uartComm != null) {
            try {
                connect();
                if (uartComm.getConnectStatus() == IComm.EConnectStatus.CONNECTED) {
                    uartComm.send(data);
                    Log.i(TAG, "Send");
                } else {
                    Log.e(TAG, "Send please connect first");
                }
            } catch (CommException e) {
                Log.e(TAG, "Send " + e.getMessage());
                e.printStackTrace();
            } finally {
                // disConnect();
            }
        }
    }

    public byte[] recv(int len) {
        if (uartComm != null) {
            try {
                connect();
                if (uartComm.getConnectStatus() == IComm.EConnectStatus.CONNECTED) {
                    byte[] result = uartComm.recv(len);
                    Log.i(TAG, "Recv");
                    return result;
                } else {
                    Log.e(TAG, "Send please connect first");
                    return null;
                }
            } catch (CommException e) {
                e.printStackTrace();
                Log.e(TAG, "Recv: " +  e.getMessage());
                return null;
            }
        }
        return null;
    }

    public byte[] recvNonBlocking() {
        if (uartComm != null) {
            try {
                connect();
                if (uartComm.getConnectStatus() == IComm.EConnectStatus.CONNECTED) {
                    byte[] result = uartComm.recvNonBlocking();
                    Log.i(TAG, "recvNonBlocking");
                    return result;
                } else {
                    Log.e(TAG, "recvNonBlocking please connect first");
                    return null;
                }
            } catch (CommException e) {
                e.printStackTrace();
                Log.e(TAG, "recvNonBlocking: " + e.getMessage());
                return null;
            }
        }
        return null;
    }

    public void disConnect() {
        if (uartComm != null) {
            try {
                if (uartComm.getConnectStatus() == IComm.EConnectStatus.CONNECTED)
                    uartComm.disconnect();
                Log.i(TAG, "DisConnect");
                // commTester = null;
                // uartComm = null;
            } catch (CommException e) {
                e.printStackTrace();
                Log.e(TAG, "DisConnect: " + e.getMessage());
            }
        }
    }

    public void setConnectTimeout(int timeout) {
        if (uartComm != null) {
            uartComm.setConnectTimeout(timeout);
            Log.i(TAG, "setConnectTimeout");
        }
    }

    public void cancelRecv() {
        if (uartComm != null) {
            uartComm.cancelRecv();
            Log.i(TAG, "cancelRecv");
            disConnect();
        }
    }

    public void reset() {
        if (uartComm != null) {
            uartComm.reset();
            Log.i(TAG, "reset");
        }
    }

    public void setSendTimeout(int timeout) {
        if (uartComm != null) {
            uartComm.setSendTimeout(timeout);
            Log.i(TAG, "setSendTimeout");
        }
    }

    public void setRecvTimeout(int timeout) {
        if (uartComm != null) {
            uartComm.setRecvTimeout(timeout);
            Log.i(TAG, "setRecvTimeout");
        }
    }

    class ReceiveThread extends Thread {
        @Override
        public void run() {
            super.run();
            String timeOut = "2000";//recvTimeoutEdit.getText().toString();
            while (!Thread.interrupted()) {
                byte[] data = UartComm.getInstance().recvNonBlocking();
                if (null != data) {
                    String res = new String(data);
                    if (res != null && !res.equals("")) {
                        Message message = Message.obtain();
                        message.what = 0;
                        message.obj = res;
                        handler.sendMessage(message);
                        Log.i("TAG", "Uart: " + res);
                    }

                } else {
                    Log.i("TAG", "Uart - No message");
                }
                try {
                    sleep(Integer.parseInt(timeOut));
                } catch (InterruptedException e) {
                    // Log.i("ss", "exit thread" + getId());
                    break;
                }
            }

        }
    }
}
