/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/07                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.utils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.pax.bizentity.db.helper.AmexAidDbHelper;
import com.pax.bizentity.db.helper.DpasAidDbHelper;
import com.pax.bizentity.db.helper.EFTAidDbHelper;
import com.pax.bizentity.db.helper.EmvAidDbHelper;
import com.pax.bizentity.db.helper.JcbAidDbHelper;
import com.pax.bizentity.db.helper.MirAidDbHelper;
import com.pax.bizentity.db.helper.PBOCAidDbHelper;
import com.pax.bizentity.db.helper.PaypassAidDbHelper;
import com.pax.bizentity.db.helper.PaywaveAidDbHelper;
import com.pax.bizentity.db.helper.PureAidDbHelper;
import com.pax.bizentity.db.helper.RupayAidDbHelper;
import com.pax.bizentity.entity.EmvAid;
import com.pax.bizentity.entity.clss.amex.AmexAidBean;
import com.pax.bizentity.entity.clss.dpas.DpasAidBean;
import com.pax.bizentity.entity.clss.eft.EFTAidBean;
import com.pax.bizentity.entity.clss.jcb.JcbAidBean;
import com.pax.bizentity.entity.clss.mir.MirAidBean;
import com.pax.bizentity.entity.clss.paypass.PayPassAidBean;
import com.pax.bizentity.entity.clss.paywave.PayWaveInterFloorLimitBean;
import com.pax.bizentity.entity.clss.paywave.PaywaveAidBean;
import com.pax.bizentity.entity.clss.pboc.PBOCAidBean;
import com.pax.bizentity.entity.clss.pure.PureAidBean;
import com.pax.bizentity.entity.clss.rupay.RupayAidBean;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.emvbase.constant.EmvConstant.KernType;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.entity.EmvAidInfo;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * Assembly AID Information
 */
public class EmvAidUtils {
    private EmvAidUtils() {
        // do nothing
    }

    /**
     * Get Contact AID Information.
     *
     * @param aid AID
     * @return Contact AID Information
     */
    @NonNull
    public static List<EmvAidInfo> getContactAidInfo(@NonNull String aid) {
        EmvAid emvAid = EmvAidDbHelper.getInstance().findAID(aid);
        if (emvAid != null) {
            return loadContactAidInfo(emvAid);
        }
        return Collections.emptyList();
    }

    @Nullable
    public static String getEditContactAidPath(String aid) {
        if (EmvAidDbHelper.getInstance().findAID(aid) != null) {
            return EmvRouterConst.CONFIG_PARAM_CONTACT_AID;
        }
        return null;
    }

    /**
     * Get Contactless AID Information.
     *
     * @param kernelType Contactless kernel type
     * @param aid AID
     * @param transType Transaction type
     * @return Contactless AID Information
     */
    @NonNull
    public static List<EmvAidInfo> getContactlessAidInfo(int kernelType, @NonNull String aid,
            int transType) {
        switch (kernelType) {
            case KernType.KERNTYPE_AE:
                AmexAidBean amexAidBean = AmexAidDbHelper.getInstance().findAID(aid);
                if (amexAidBean != null) {
                    return loadAmexAidInfo(amexAidBean);
                }
                break;
            case KernType.KERNTYPE_ZIP:
                DpasAidBean dpasAidBean = DpasAidDbHelper.getInstance().findAID(aid);
                if (dpasAidBean != null) {
                    return loadDpasAidInfo(dpasAidBean);
                }
                break;
            case KernType.KERNTYPE_EFT:
                EFTAidBean eftAidBean = EFTAidDbHelper.getInstance().findAID(aid);
                if (eftAidBean != null) {
                    return loadEftAidInfo(eftAidBean);
                }
                break;
            case KernType.KERNTYPE_JCB:
                JcbAidBean jcbAidBean = JcbAidDbHelper.getInstance().findAID(aid);
                if (jcbAidBean != null) {
                    return loadJcbAidInfo(jcbAidBean);
                }
                break;
            case KernType.KERNTYPE_MIR:
                MirAidBean mirAidBean = MirAidDbHelper.getInstance().findAID(aid);
                if (mirAidBean != null) {
                    return loadMirAidInfo(mirAidBean);
                }
                break;
            case KernType.KERNTYPE_MC:
                PayPassAidBean payPassAidBean = PaypassAidDbHelper.getInstance().findAID(aid);
                if (payPassAidBean != null) {
                    return loadMcAidInfo(payPassAidBean);
                }
                break;
            case KernType.KERNTYPE_VIS:
                PaywaveAidBean paywaveAidBean = PaywaveAidDbHelper.getInstance().findAID(aid);
                if (paywaveAidBean != null) {
                    return loadPayWaveAidInfo(paywaveAidBean, transType);
                }
                break;
            case KernType.KERNTYPE_PBOC:
                PBOCAidBean pbocAidBean = PBOCAidDbHelper.getInstance().findAID(aid);
                if (pbocAidBean != null) {
                    return loadPbocAidInfo(pbocAidBean);
                }
                break;
            case KernType.KERNTYPE_PURE:
                PureAidBean pureAidBean = PureAidDbHelper.getInstance().findAID(aid);
                if (pureAidBean != null) {
                    return loadPureAidInfo(pureAidBean);
                }
                break;
            case KernType.KERNTYPE_RUPAY:
                RupayAidBean rupayAidBean = RupayAidDbHelper.getInstance().findAID(aid);
                if (rupayAidBean != null) {
                    return loadRuPayAidInfo(rupayAidBean);
                }
                break;
        }
        return Collections.emptyList();
    }

    @Nullable
    public static String getEditContactlessAidPath(int kernelType, @NonNull String aid) {
        switch (kernelType) {
            case KernType.KERNTYPE_AE:
                AmexAidBean amexAidBean = AmexAidDbHelper.getInstance().findAID(aid);
                if (amexAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_AMEX_AID;
                }
                break;
            case KernType.KERNTYPE_ZIP:
                DpasAidBean dpasAidBean = DpasAidDbHelper.getInstance().findAID(aid);
                if (dpasAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_DPAS_AID;
                }
                break;
            case KernType.KERNTYPE_EFT:
                EFTAidBean eftAidBean = EFTAidDbHelper.getInstance().findAID(aid);
                if (eftAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_EFT_AID;
                }
                break;
            case KernType.KERNTYPE_JCB:
                JcbAidBean jcbAidBean = JcbAidDbHelper.getInstance().findAID(aid);
                if (jcbAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_JCB_AID;
                }
                break;
            case KernType.KERNTYPE_MIR:
                MirAidBean mirAidBean = MirAidDbHelper.getInstance().findAID(aid);
                if (mirAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_MIR_AID;
                }
                break;
            case KernType.KERNTYPE_MC:
                PayPassAidBean payPassAidBean = PaypassAidDbHelper.getInstance().findAID(aid);
                if (payPassAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_MC_AID;
                }
                break;
            case KernType.KERNTYPE_VIS:
                PaywaveAidBean paywaveAidBean = PaywaveAidDbHelper.getInstance().findAID(aid);
                if (paywaveAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE_AID;
                }
                break;
            case KernType.KERNTYPE_PBOC:
                PBOCAidBean pbocAidBean = PBOCAidDbHelper.getInstance().findAID(aid);
                if (pbocAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_PBOC_AID;
                }
                break;
            case KernType.KERNTYPE_PURE:
                PureAidBean pureAidBean = PureAidDbHelper.getInstance().findAID(aid);
                if (pureAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_PURE_AID;
                }
                break;
            case KernType.KERNTYPE_RUPAY:
                RupayAidBean rupayAidBean = RupayAidDbHelper.getInstance().findAID(aid);
                if (rupayAidBean != null) {
                    return EmvRouterConst.CONFIG_PARAM_CLSS_RUPAY_AID;
                }
                break;
        }
        return null;
    }

    private static List<EmvAidInfo> loadContactAidInfo(@NonNull EmvAid aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_cap_short), aid.getTerminalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_add_cap_short), aid.getTerminalAdditionalCapability()));
        return infoList;
    }

    private static List<EmvAidInfo> loadAmexAidInfo(@NonNull AmexAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_cap_short), aid.getTerminalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_add_cap_short), aid.getTerminalAdditionalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_trans_cap_short), aid.getTermTransCap()));
        return infoList;
    }

    private static List<EmvAidInfo> loadDpasAidInfo(@NonNull DpasAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_cap_short), aid.getTerminalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_ttq), aid.getTtq()));
        return infoList;
    }

    private static List<EmvAidInfo> loadEftAidInfo(@NonNull EFTAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_cap_short), aid.getTerminalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_add_cap_short), aid.getTerminalAddtionalCapability()));
        return infoList;
    }

    private static List<EmvAidInfo> loadJcbAidInfo(@NonNull JcbAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_interchange_short), aid.getTermInterchange()));
        return infoList;
    }

    private static List<EmvAidInfo> loadMirAidInfo(@NonNull MirAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        return infoList;
    }

    private static List<EmvAidInfo> loadMcAidInfo(@NonNull PayPassAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_kernel_config), aid.getKernelConfig()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_cap_cvm_short), aid.getCvmRequired()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_cap_no_cvm_short), aid.getNoCvmRequired()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_sec_cap_short), aid.getSecurityCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_mag_cvm_cap_cvm_short), aid.getMagCvm()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_mag_cvm_cap_no_cvm_short), aid.getMagNoCvm()));
        return infoList;
    }

    private static List<EmvAidInfo> loadPayWaveAidInfo(@NonNull PaywaveAidBean aid, int transType) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        List<PayWaveInterFloorLimitBean> floorLimitBeanList = aid.getInterWareFloorLimit();
        if (floorLimitBeanList != null && !floorLimitBeanList.isEmpty()) {
            for (PayWaveInterFloorLimitBean floorLimitBean : floorLimitBeanList) {
                if (transType == floorLimitBean.getTransType()) {
                    infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(floorLimitBean.getTransLimit())));
                    infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(floorLimitBean.getFloorLimit())));
                    infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(floorLimitBean.getCvmLimit())));
                    break;
                }
            }
        }
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_ttq), aid.getTtq()));
        return infoList;
    }

    private static List<EmvAidInfo> loadPbocAidInfo(@NonNull PBOCAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_qps_limit), CurrencyConverter.convert(aid.getQpsLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_cap_short), aid.getTerminalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_add_cap_short), aid.getTerminalAdditionalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_ttq), aid.getTtq()));
        return infoList;
    }

    private static List<EmvAidInfo> loadPureAidInfo(@NonNull PureAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_cap_short), aid.getTerminalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_add_cap_short), aid.getTerminalAdditionalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_clss_kernel_cap_short), aid.getClssKernelCapability()));
        return infoList;
    }

    private static List<EmvAidInfo> loadRuPayAidInfo(@NonNull RupayAidBean aid) {
        List<EmvAidInfo> infoList = new LinkedList<>();
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_aid), aid.getAid()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_app_name), aid.getAppName()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_trans_limit), CurrencyConverter.convert(aid.getTransLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_floor_limit), CurrencyConverter.convert(aid.getFloorLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_cvm_limit), CurrencyConverter.convert(aid.getCvmLimit())));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_default), aid.getTacDefault()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_denial), aid.getTacDenial()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_tac_online), aid.getTacOnline()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_cap_short), aid.getTerminalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_term_add_cap_short), aid.getTerminalAdditionalCapability()));
        infoList.add(new EmvAidInfo(ResourceUtil.getString(R.string.config_param_ttq), aid.getTtq()));
        return infoList;
    }
}
