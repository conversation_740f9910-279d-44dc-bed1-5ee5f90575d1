/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/02/22                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.activity;

import android.content.res.Configuration;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButtonToggleGroup;
import com.pax.bizentity.entity.ETransType;
import com.pax.commonlib.application.BaseActivity;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.commonui.keyboard.CustomKeyboardEditText;
import com.pax.commonui.text.EditorActionListener;
import com.pax.commonui.text.EnterAmountTextWatcher;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.poslib.utils.PosDeviceUtils;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.common.DefaultUriRequest;

public class AdminActivity extends BaseActivity {
    private static final String TAG = "MainActivity";
    private static final String SAVED_AMOUNT = "amount";
    private static final String SAVED_TRANS_TYPE = "trans_type";
    private static final String SAVED_IS_LEAVE_THIS_ACTIVITY = "is_leave_this_activity";

    private boolean isLeaveThisActivity = false;

    private CustomKeyboardEditText edtAmount;
    private MaterialButtonToggleGroup transTypeGroup;
    private EnterAmountTextWatcher amountWatcher;

    @Override
    protected int getLayoutId() {
        return R.layout.activity_admin;
    }

    @Override
    protected void initViews() {
        edtAmount = findViewById(R.id.main_amount_edit_text);

        edtAmount.requestFocus();
        edtAmount.setText("");
        edtAmount.setKeepKeyBoardOn(true);

        MaterialToolbar toolbar = findViewById(R.id.main_toolbar);
        setSupportActionBar(toolbar);

        transTypeGroup = findViewById(R.id.main_trans_type_group);

        View space = findViewById(R.id.main_space);
        if (isPortrait()) {
            space.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    (int) (ResourceUtil.getDisplayHeightPixels() * 0.5)));
        } else {
            space.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    (int) (ResourceUtil.getDisplayHeightPixels() * 0.375)));
        }
    }

    private boolean isPortrait() {
        return this.getResources().getConfiguration().orientation != Configuration.ORIENTATION_LANDSCAPE;
    }

    @Override
    protected void setListeners() {
        amountWatcher = new EnterAmountTextWatcher();
        edtAmount.addTextChangedListener(amountWatcher);
        edtAmount.setOnEditorActionListener(new EditorActionListener() {
            @Override
            protected void onKeyOk() {
                // do trans
                if (edtAmount.getText() != null
                        && edtAmount.getText().length() != 0) {
                    long amount = CurrencyConverter.parse(edtAmount.getText().toString());
                    if (amount > 0) {
                        doTrans(amount);
                    }
                }
            }

            @Override
            protected void onKeyCancel() {
                // clear
                edtAmount.setText("");
            }
        });
    }

    private void doTrans(long amount) {
        int typeId = transTypeGroup.getCheckedButtonId();
        ETransType transType = null;
        if (typeId == R.id.main_trans_type_sale) {
            transType = ETransType.SALE;
        } else if (typeId == R.id.main_trans_type_pre_auth) {
            transType = ETransType.PREAUTH;
        } else if (typeId == R.id.main_trans_type_refund) {
            transType = ETransType.REFUND;
        }
        if (transType != null) {
            Router.startUri(new DefaultUriRequest(this, EmvRouterConst.SEARCH_CARD)
                    .putExtra(BundleFieldConst.AMOUNT, amount)
                    .putExtra(BundleFieldConst.TRANS_TYPE, transType.name()));
            isLeaveThisActivity = true;
        }
    }

    @Override
    protected void loadParam() {
        // do nothing
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (isLeaveThisActivity) {
            clearAmount();
            isLeaveThisActivity = false;
        }
        PosDeviceUtils.enableHomeRecentKey(true);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    protected boolean onOptionsItemSelectedSub(MenuItem item) {
        if (R.id.settings == item.getItemId()) {
            // To settings page
            Router.startUri(this, EmvRouterConst.CONFIG_MAIN);
            isLeaveThisActivity = true;
        }
        return super.onOptionsItemSelectedSub(item);
    }

    private synchronized void clearAmount() {
        edtAmount.setText("");
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (edtAmount != null && edtAmount.getText() != null) {
            outState.putLong(SAVED_AMOUNT, CurrencyConverter.parse(edtAmount.getText().toString()));
        }
        if (transTypeGroup != null) {
            outState.putInt(SAVED_TRANS_TYPE, transTypeGroup.getCheckedButtonId());
        }
        outState.putBoolean(SAVED_IS_LEAVE_THIS_ACTIVITY, isLeaveThisActivity);
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        // Amount
        long amount = savedInstanceState.getLong(SAVED_AMOUNT, 0);
        LogUtils.d(TAG, "restore amount: " + amount);
        if (amount != 0 && amountWatcher != null) {
            amountWatcher.setAmount(0, amount);
        }

        // Trans Type
        int chipId = savedInstanceState.getInt(SAVED_TRANS_TYPE);
        if (chipId != 0 && transTypeGroup != null) {
            transTypeGroup.check(chipId);
        }

        isLeaveThisActivity = savedInstanceState.getBoolean(SAVED_IS_LEAVE_THIS_ACTIVITY, true);
    }
}