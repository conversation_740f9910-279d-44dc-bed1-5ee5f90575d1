/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/24                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.pref;

import androidx.fragment.app.DialogFragment;

/**
 * All custom dialog preference should implement this interface.
 */
public interface IDialogPreference<T extends DialogFragment> {

    /**
     * Create a dialog fragment. This method will be called when preference clicked.
     *
     * @return New dialog fragment instance
     */
    T createDialog(String key);
}
