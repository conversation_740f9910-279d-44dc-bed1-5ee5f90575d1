/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/02/23                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.annotation.Nullable;
import androidx.preference.PreferenceDataStore;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;

/**
 * Normal PreferenceDataStore
 * <br>
 * Replace Jetpack Preference's default use of SharedPreference to access data with configservice
 * module to access data
 */
public class PrefDataStore extends PreferenceDataStore {
    private final IConfigParamService service = Router.getService(IConfigParamService.class,
            ConfigServiceConstant.CONFIGSERVICE_CONFIG);

    @Override
    public void putString(String key, @Nullable String value) {
        service.putString(key, value);
    }

    @Override
    public void putInt(String key, int value) {
        service.putString(key, String.valueOf(value));
    }

    @Override
    public void putLong(String key, long value) {
        service.putString(key, String.valueOf(value));
    }

    @Override
    public void putFloat(String key, float value) {
        service.putString(key, String.valueOf(value));
    }

    @Override
    public void putBoolean(String key, boolean value) {
        service.putBoolean(key, value);
    }

    @Nullable
    @Override
    public String getString(String key, @Nullable String defValue) {
        return service.getString(key, String.valueOf(defValue));
    }

    @Override
    public int getInt(String key, int defValue) {
        return Integer.parseInt(service.getString(key, String.valueOf(defValue)));
    }

    @Override
    public long getLong(String key, long defValue) {
        return Long.parseLong(service.getString(key, String.valueOf(defValue)));
    }

    @Override
    public float getFloat(String key, float defValue) {
        return Float.parseFloat(service.getString(key, String.valueOf(defValue)));
    }

    @Override
    public boolean getBoolean(String key, boolean defValue) {
        return service.getBoolean(key, defValue);
    }
}
