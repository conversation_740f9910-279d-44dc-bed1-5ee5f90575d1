/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                Author	               Action
 * 20210507 	        xieYb                  Create
 * ===========================================================================================
 *
 */

package com.pax.configservice.export;

import com.pax.bizentity.entity.Acquirer;
import java.util.List;

public interface IConfigAcquirer {
    /**
     * insert acquirers
     * @param acquirerList acquirers
     * @return insert status
     */
    public boolean insertAcquirer(List<Acquirer> acquirerList);

    /**
     * find acquirer by name
     * @param acquirerName acquirer name
     * @return acquirer
     */
    public Acquirer findAcquirer(final String acquirerName);
    /**
     * find All Acquirers
     */
    public List<Acquirer> findAllAcquirers();

    /**
     * update acquirer
     * @param acquirer acquirer
     * @return update status
     */
    public boolean updateAcquirer(final Acquirer acquirer);

    /**
     * delete all acquirers
     */
    public void deleteAllAcquirer();
}
