/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/06                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.google.android.material.button.MaterialButton;
import com.visionpay.opt.pax.R;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/6
 */
public class TransErrorFragment extends Fragment {
    private String title;
    private String info;
    private String buttonText;
    private Integer buttonIconRes;
    private View.OnClickListener buttonClickListener;

    private TextView titleTextView;
    private TextView infoTextView;
    private View divider;
    private MaterialButton button;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_error_card, container, false);
        titleTextView = view.findViewById(R.id.trans_error_title);
        infoTextView = view.findViewById(R.id.trans_error_content);
        divider = view.findViewById(R.id.trans_error_divider);
        button = view.findViewById(R.id.trans_error_button);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Title
        if (title != null && !title.isEmpty()) {
            titleTextView.setVisibility(View.VISIBLE);
            titleTextView.setText(title);
        } else {
            titleTextView.setVisibility(View.GONE);
        }

        // Info
        if (info != null && !info.isEmpty()) {
            infoTextView.setVisibility(View.VISIBLE);
            infoTextView.setText(info);
        } else {
            infoTextView.setVisibility(View.GONE);
        }

        // Button
        if (buttonText != null && !buttonText.isEmpty()) {
            divider.setVisibility(View.VISIBLE);
            button.setVisibility(View.VISIBLE);
            button.setText(buttonText);
            if (buttonIconRes != null) {
                button.setIconResource(buttonIconRes);
            }
            if (buttonClickListener != null) {
                button.setOnClickListener(buttonClickListener);
            }
        } else {
            divider.setVisibility(View.GONE);
            button.setVisibility(View.GONE);
        }
    }

    public TransErrorFragment setTitle(String title) {
        this.title = title;
        return this;
    }

    public TransErrorFragment setInfo(String info) {
        this.info = info;
        return this;
    }

    public TransErrorFragment setButtonText(String buttonText) {
        this.buttonText = buttonText;
        return this;
    }

    public TransErrorFragment setButtonIconRes(Integer buttonIconRes) {
        this.buttonIconRes = buttonIconRes;
        return this;
    }

    public TransErrorFragment setButtonClickListener(View.OnClickListener buttonClickListener) {
        this.buttonClickListener = buttonClickListener;
        return this;
    }
}
