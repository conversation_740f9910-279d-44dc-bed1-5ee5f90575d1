/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                Author	               Action
 * 20210516 	        xieYb                  Create
 * ===========================================================================================
 *
 */

package com.pax.commonui.dialog.constant;

public class DialogConstant {
    /**
     * the period of showing dialog of successful transaction, unit: second
     */
    public static final int SUCCESS_DIALOG_SHOW_TIME = 2;
    /**
     * the period of showing dialog of failed transaction, unit: second
     */
    public static final int FAILED_DIALOG_SHOW_TIME = 3;
    public static final int FAILED_DIALOG_SHOW_TIME_LONG = 10;

    private DialogConstant() {
        // do nothing
    }
}
