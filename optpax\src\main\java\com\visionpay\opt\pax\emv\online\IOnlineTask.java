/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/01                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.online;

import androidx.annotation.NonNull;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.pax.emvservice.export.IEmvBase;

/**
 * Online task interface.
 */
public interface IOnlineTask<T extends IEmvBase> {
    /**
     * Start online and get result.
     *
     * @param emv IEmvBase
     * @return Online Result
     */
    OnlineResultWrapper start(@NonNull T emv, long amount, String terminalId, int detectResult, String reference);
}
