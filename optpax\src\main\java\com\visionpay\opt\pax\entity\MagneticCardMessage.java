package com.visionpay.opt.pax.entity;

import androidx.annotation.Keep;

import com.visionpay.opt.pax.utils.DateUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

@Keep
public class MagneticCardMessage {
    private Date createdUTCDateTime;
    private Date completedUTCDateTime;
    private String externalReference;
    private String im30Reference;
    private ReadState magneticState;
    private String trackData1;
    private String trackData2;
    private String trackData3;

    public MagneticCardMessage(POSTransaction post, IM30Transaction im30t){
        createdUTCDateTime = DateUtils.gettUtcTime(new Date());
        completedUTCDateTime = DateUtils.gettUtcTime(new Date());
        externalReference = post.getExternalReference();
        im30Reference = im30t.getIm30Reference();
        magneticState = ReadState.AwaitingCardRead;
        trackData1 = "";
        trackData2 = "";
        trackData3 = "";
    }

    public ReadState getMagneticState() {
        return magneticState;
    }

    public void setMagneticState(ReadState magneticState) {
        this.magneticState = magneticState;
        completedUTCDateTime = DateUtils.gettUtcTime(new Date());
    }

    public Date getCreatedUTCDateTime() {
        return createdUTCDateTime;
    }

    public void setCreatedUTCDateTime(Date createdUTCDateTime) {
        this.createdUTCDateTime = createdUTCDateTime;
    }

    public Date getCompletedUTCDateTime() {
        return completedUTCDateTime;
    }

    public void setCompletedUTCDateTime(Date completedUTCDateTime) {
        this.completedUTCDateTime = completedUTCDateTime;
    }

    public String getExternalReference() {
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public String getIm30Reference() {
        return im30Reference;
    }

    public void setIm30Reference(String im30Reference) {
        this.im30Reference = im30Reference;
    }

    public String getTrackData1() {
        return trackData1;
    }

    public void setTrackData1(String trackData1) {
        this.trackData1 = trackData1;
    }

    public String getTrackData2() {
        return trackData2;
    }

    public void setTrackData2(String trackData2) {
        this.trackData2 = trackData2;
    }

    public String getTrackData3() {
        return trackData3;
    }

    public void setTrackData3(String trackData3) {
        this.trackData3 = trackData3;
    }
}

