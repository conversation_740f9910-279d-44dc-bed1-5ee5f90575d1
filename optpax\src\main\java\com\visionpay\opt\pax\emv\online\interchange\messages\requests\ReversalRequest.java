package com.visionpay.opt.pax.emv.online.interchange.messages.requests;

import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageSubtype;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageType;
import com.visionpay.opt.pax.emv.online.interchange.messages.FinancialMessage;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class ReversalRequest extends FinancialMessage {

    private final long mInternalID;

    @Override
    protected byte[] internalGetMessage(){
        byte[] internalMessage = super.internalGetMessage();
        ByteArrayOutputStream message = new ByteArrayOutputStream();
        try {
            message.write(internalMessage);
        } catch (IOException e) {
            e.printStackTrace();
        }
        message.write((byte)((this.mInternalID >> 24) & 0xFF));
        message.write((byte)((this.mInternalID >> 16) & 0xFF));
        message.write((byte)((this.mInternalID >> 8) & 0xFF));
        message.write((byte)((this.mInternalID >> 0) & 0xFF));
        return message.toByteArray();
    }

    public ReversalRequest(String terminalID, long refNo, byte[] sessionID, long internalID){
        super(InterchangeMessageType.IMT_TRX, InterchangeMessageSubtype.SMT_REVERSE, refNo, terminalID, sessionID);
        this.mInternalID = internalID;
    }
}