package com.pax.emvlib.process.magnetic;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.emvbase.BuildConfig;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.param.EmvProcessParam;
import com.pax.emvbase.process.EmvBase;
import com.pax.emvbase.process.entity.IssuerRspData;
import com.pax.emvbase.process.entity.TransResult;
import com.pax.emvbase.process.enums.CvmResultEnum;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvbase.process.magnetic.IMagCallback;
import com.pax.emvbase.utils.EmvDebugger;
import com.pax.jemv.clcommon.ACType;
import com.pax.jemv.clcommon.RetCode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
public class MagProcess extends EmvBase {
    private static final String TAG = "MagProcess";

    private static final boolean enableDebugLog = BuildConfig.DEBUG;

    private final List<EmvTag> emvTagList = new ArrayList<>();

    private IMagCallback magStatusListener;

    private String trackData1;
    private String trackData2;
    private String trackData3;

    private MagProcess() {
    }

    private static class Holder {
        private static final MagProcess INSTANCE = new MagProcess();
    }

    public static MagProcess getInstance() {
        return Holder.INSTANCE;
    }

    public void registerMagProcessListener(IMagCallback magTransProcessListener) {
        magStatusListener = magTransProcessListener;
    }

    @Override
    public int preTransProcess(EmvProcessParam emvProcessParam) {
        return RetCode.EMV_OK;
    }

    public void setTrackData(String trackData1, String trackData2, String trackData3){
        this.trackData1 = trackData1;
        this.trackData2 = trackData2;
        this.trackData3 = trackData3;
    }

    @Override
    public TransResult startTransProcess() {
        LogUtils.i(TAG, "startTransProcess");
        //After detected card,start emv process
        //TODO set emg tags by tracks
        setTlv(TagsTable.TRACK2, ConvertUtils.strToBcd(trackData2, ConvertUtils.EPaddingPosition.PADDING_RIGHT));
        if(trackData1 != null && !trackData1.isEmpty()) {
            String[] infos = trackData1.split("\\^");
            setTlv(TagsTable.CARDHOLDER_NAME, infos.length > 1 ? infos[1].getBytes() : new byte[0]);
        }

        int ret = 0;
        if (magStatusListener != null){
            ret = magStatusListener.showConfirmCard();
            if (ret != RetCode.EMV_OK){//for example,timeout/data_error
                return new TransResult(ret, TransResultEnum.RESULT_OFFLINE_DENIED, CvmResultEnum.CVM_NO_CVM);
            }
        }

        TransResult transResult = new TransResult(RetCode.EMV_OK, TransResultEnum.RESULT_REQ_ONLINE, CvmResultEnum.CVM_NO_CVM);
        return transResult;
    }

    @Override
    public TransResult completeTransProcess(IssuerRspData issuerRspData) {
        TransResult completeTransResult = new TransResult(RetCode.EMV_OK, TransResultEnum.RESULT_ONLINE_CARD_DENIED, CvmResultEnum.CVM_NO_CVM);
        ACType acType = new ACType();
        if (issuerRspData.getAuthCode().length != 0) {
            LogUtils.i(TAG, "");
            setTlv(0x89, issuerRspData.getAuthCode());
            if (enableDebugLog) {
                LogUtils.i(TAG, "auth code(89):" + ConvertUtils.bcd2Str(issuerRspData.getAuthCode()));
            }
        }
        if (issuerRspData.getAuthData().length != 0) {
            setTlv(0x91, issuerRspData.getAuthData());
            if (enableDebugLog) {
                LogUtils.i(TAG, "auth data(91):" + ConvertUtils.bcd2Str(issuerRspData.getAuthData()));
            }
        }
        if (enableDebugLog) {
            LogUtils.i(TAG, "online result:" + issuerRspData.getOnlineResult());
            LogUtils.i(TAG, "issuer script:" + ConvertUtils.bcd2Str(issuerRspData.getScript()));
        }
        LogUtils.i(TAG, "completeTransProcess,acType:" + acType.type);
        if (acType.type == ACType.AC_TC) {
            completeTransResult.setTransResult(TransResultEnum.RESULT_ONLINE_APPROVED);
        } else if (acType.type == ACType.AC_AAC) {
            completeTransResult.setTransResult(TransResultEnum.RESULT_ONLINE_CARD_DENIED);
        }
        return completeTransResult;
    }

    @Override
    public byte[] getTlv(int tag) {
        byte[] value = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            Optional<EmvTag> emvTag = emvTagList.stream().filter(o -> o.getTag() == tag).findFirst();
            if (emvTag.isPresent()) {
                value = emvTag.get().getValue();
            }
        }

        EmvDebugger.d(TAG, "getTlv", tag, value);
        if (value == null) {
            return new byte[0];
        }
        return Arrays.copyOfRange(value, 0, value.length);
    }

    /**
     * Sets value on specific tag
     *
     * @param tag   emv tag
     * @param value tag value
     */
    @Override
    public void setTlv(int tag, byte[] value) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            Optional<EmvTag> emvTag = emvTagList.stream().filter(o -> o.getTag() == tag).findFirst();
            if (!emvTag.isPresent()) {
                emvTagList.add(new EmvTag(tag, value));
            }
            else {
                emvTag.get().setValue(value);
            }
        }else{
            emvTagList.add(new EmvTag(tag, value));
        }

        EmvDebugger.d(TAG, "setTlv", tag, value);
    }

    private class EmvTag {
        private int tag;
        private byte[] value;

        public EmvTag(int tag, byte[] value) {
            this.tag = tag;
            this.value = value;
        }

        public int getTag() {
            return tag;
        }

        public EmvTag setTag(int tag) {
            this.tag = tag;
            return this;
        }

        public byte[] getValue() {
            return value;
        }

        public EmvTag setValue(byte[] value) {
            this.value = value;
            return this;
        }
    }
}
