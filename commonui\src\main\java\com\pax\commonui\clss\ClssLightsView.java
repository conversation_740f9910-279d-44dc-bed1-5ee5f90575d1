/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.commonui.clss;

import android.content.Context;
import android.os.ConditionVariable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.widget.LinearLayout;
import androidx.annotation.IntRange;
import androidx.annotation.Nullable;
import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonui.R;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.pax.dal.exceptions.EPiccDevException;
import com.pax.dal.exceptions.PiccDevException;
import com.pax.poslib.utils.PosDeviceUtils;
import com.sankuai.waimai.router.Router;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * emv contactless light view list
 */
public class ClssLightsView extends LinearLayout {
    private static final String TAG = "ClssLightsView";

    private final ClssLight[] lights = new ClssLight[4];

    private AlphaAnimation blinking;
    private final ConditionVariable cv = new ConditionVariable();
    private final IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
    private final boolean isSupportPhysicalClsLight = configParamService.getBoolean(ConfigKeyConstant.EDC_PHYSICAL_CLS_LIGHT);
    private class LedThread extends Thread{
        /**
         * The Index.
         */
        private final AtomicInteger index = new AtomicInteger(-1);
        private final AtomicBoolean refreshNow = new AtomicBoolean(false);

        @Override
        public void run() {
            try {
                while (index.get() != -1) {
                    LogUtils.d(TAG,"physical lights loop");

                    // ON
                    PosDeviceUtils.setPiccLedWithException(index.get(), ClssLight.ON);
                    cv.block(300);
                    cv.close();
                    if (refreshNow.get()) {
                        refreshNow.set(false);
                        continue;
                    }

                    // OFF
                    PosDeviceUtils.setPiccLedWithException(index.get(), ClssLight.OFF);
                    cv.block(300);
                    cv.close();
                    refreshNow.set(false);
                }
                LogUtils.d(TAG,"physical lights end");
                PosDeviceUtils.setPiccLedWithException(-1, ClssLight.OFF);
            } catch (PiccDevException e) {
                LogUtils.e(TAG, "", e);
                if (e.getErrCode() == EPiccDevException.ERROR_DISABLED.getErrCodeFromBasement()) {
                    BaseApplication.getAppContext().runOnUiThread(() -> setLights(3, ClssLight.OFF));
                }
            }
        }
    }

    private LedThread ledThread = null;

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (isSupportPhysicalClsLight) {
            PosDeviceUtils.setPiccLed(-1, ClssLight.OFF);
        }
        if (ledThread != null && ledThread.isAlive()){
            ledThread.interrupt();
            ledThread = null;
        }
    }

    /**
     * Instantiates a new Clss lights view.
     *
     * @param context the context
     */
    public ClssLightsView(Context context) {
        this(context, null);
    }

    /**
     * Instantiates a new Clss lights view.
     *
     * @param context the context
     * @param attrs   the attrs
     */
    public ClssLightsView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    /**
     * Instantiates a new Clss lights view.
     *
     * @param context      the context
     * @param attrs        the attrs
     * @param defStyleAttr the def style attr
     */
    public ClssLightsView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        LayoutInflater mInflater = LayoutInflater.from(context);
        View myView = mInflater.inflate(R.layout.commonui_clss_light_layout, null);
        LayoutParams parentParams = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
        parentParams.setLayoutDirection(HORIZONTAL);
        addView(myView, parentParams);

        init();
    }

    private void init() {
        blinking = new AlphaAnimation(1, 0);
        blinking.setDuration(500);
        blinking.setRepeatCount(Animation.INFINITE);
        blinking.setRepeatMode(Animation.REVERSE);

        lights[0] = (ClssLight) findViewById(R.id.light1);
        lights[1] = (ClssLight) findViewById(R.id.light2);
        lights[2] = (ClssLight) findViewById(R.id.light3);
        lights[3] = (ClssLight) findViewById(R.id.light4);
    }

    /**
     * set Light status
     *
     * @param index  index
     * @param status status
     */
    public void setLights(final @IntRange(from = -1, to = 3) int index, @ClssLight.STATUS int status) {
        setLights(index, status, true);
    }

    /**
     * set Light status
     *
     * @param index  index
     * @param status status
     * @param resetOther reset other lights
     */
    public void setLights(final @IntRange(from = -1, to = 3) int index, @ClssLight.STATUS int status, boolean resetOther) {
        for (int i = 0; i < lights.length; ++i) {
            if (index == i) {
                lights[i].setStatus(status, blinking);
            } else if (resetOther) {
                lights[i].setStatus(ClssLight.OFF, null);
            }
        }
        if (!isSupportPhysicalClsLight){
            return;
        }
        if (ledThread == null) {
            ledThread = new LedThread();
            ledThread.index.set(index);
            ledThread.start();
        }else {
            ledThread.index.set(index);
            if (index == -1 || index == 1){
                ledThread.refreshNow.set(true);
                cv.open();
            }
        }
    }
}
