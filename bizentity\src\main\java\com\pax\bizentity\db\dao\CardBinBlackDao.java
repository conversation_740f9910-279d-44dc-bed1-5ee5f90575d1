package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.CardBinBlack;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "card_bin_black".
*/
public class CardBinBlackDao extends AbstractDao<CardBinBlack, Long> {

    public static final String TABLENAME = "card_bin_black";

    /**
     * Properties of entity CardBinBlack.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "id");
        public final static Property Bin = new Property(1, String.class, "bin", false, "card_bin");
        public final static Property CardNoLen = new Property(2, int.class, "cardNoLen", false, "CARD_NO_LEN");
    }


    public CardBinBlackDao(DaoConfig config) {
        super(config);
    }
    
    public CardBinBlackDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"card_bin_black\" (" + //
                "\"id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"card_bin\" TEXT UNIQUE ," + // 1: bin
                "\"CARD_NO_LEN\" INTEGER NOT NULL );"); // 2: cardNoLen
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"card_bin_black\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, CardBinBlack entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String bin = entity.getBin();
        if (bin != null) {
            stmt.bindString(2, bin);
        }
        stmt.bindLong(3, entity.getCardNoLen());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, CardBinBlack entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String bin = entity.getBin();
        if (bin != null) {
            stmt.bindString(2, bin);
        }
        stmt.bindLong(3, entity.getCardNoLen());
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public CardBinBlack readEntity(Cursor cursor, int offset) {
        CardBinBlack entity = new CardBinBlack( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // bin
            cursor.getInt(offset + 2) // cardNoLen
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, CardBinBlack entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setBin(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setCardNoLen(cursor.getInt(offset + 2));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(CardBinBlack entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(CardBinBlack entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(CardBinBlack entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
