package com.pax.bizentity.db.dao;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteDatabase.CursorFactory;
import android.util.Log;

import org.greenrobot.greendao.AbstractDaoMaster;
import org.greenrobot.greendao.database.StandardDatabase;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseOpenHelper;
import org.greenrobot.greendao.identityscope.IdentityScopeType;


// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/**
 * Master of DAO (schema version 8): knows all DAOs.
 */
public class DaoMaster extends AbstractDaoMaster {
    public static final int SCHEMA_VERSION = 8;

    /** Creates underlying database table using DAOs. */
    public static void createAllTables(Database db, boolean ifNotExists) {
        AcqIssuerRelationDao.createTable(db, ifNotExists);
        AcquirerDao.createTable(db, ifNotExists);
        CapkRevokeBeanDao.createTable(db, ifNotExists);
        CardBinDao.createTable(db, ifNotExists);
        CardBinBlackDao.createTable(db, ifNotExists);
        CardRangeDao.createTable(db, ifNotExists);
        ClssTornLogDao.createTable(db, ifNotExists);
        EmvAidDao.createTable(db, ifNotExists);
        EmvCapkDao.createTable(db, ifNotExists);
        IssuerDao.createTable(db, ifNotExists);
        TransDataDao.createTable(db, ifNotExists);
        TransTotalDao.createTable(db, ifNotExists);
        AmexAidBeanDao.createTable(db, ifNotExists);
        AmexDrlBeanDao.createTable(db, ifNotExists);
        DpasAidBeanDao.createTable(db, ifNotExists);
        EFTAidBeanDao.createTable(db, ifNotExists);
        JcbAidBeanDao.createTable(db, ifNotExists);
        MirAidBeanDao.createTable(db, ifNotExists);
        PayPassAidBeanDao.createTable(db, ifNotExists);
        PayWaveInterFloorLimitBeanDao.createTable(db, ifNotExists);
        PaywaveAidBeanDao.createTable(db, ifNotExists);
        PaywaveDrlBeanDao.createTable(db, ifNotExists);
        PBOCAidBeanDao.createTable(db, ifNotExists);
        PureAidBeanDao.createTable(db, ifNotExists);
        RupayAidBeanDao.createTable(db, ifNotExists);
    }

    /** Drops underlying database table using DAOs. */
    public static void dropAllTables(Database db, boolean ifExists) {
        AcqIssuerRelationDao.dropTable(db, ifExists);
        AcquirerDao.dropTable(db, ifExists);
        CapkRevokeBeanDao.dropTable(db, ifExists);
        CardBinDao.dropTable(db, ifExists);
        CardBinBlackDao.dropTable(db, ifExists);
        CardRangeDao.dropTable(db, ifExists);
        ClssTornLogDao.dropTable(db, ifExists);
        EmvAidDao.dropTable(db, ifExists);
        EmvCapkDao.dropTable(db, ifExists);
        IssuerDao.dropTable(db, ifExists);
        TransDataDao.dropTable(db, ifExists);
        TransTotalDao.dropTable(db, ifExists);
        AmexAidBeanDao.dropTable(db, ifExists);
        AmexDrlBeanDao.dropTable(db, ifExists);
        DpasAidBeanDao.dropTable(db, ifExists);
        EFTAidBeanDao.dropTable(db, ifExists);
        JcbAidBeanDao.dropTable(db, ifExists);
        MirAidBeanDao.dropTable(db, ifExists);
        PayPassAidBeanDao.dropTable(db, ifExists);
        PayWaveInterFloorLimitBeanDao.dropTable(db, ifExists);
        PaywaveAidBeanDao.dropTable(db, ifExists);
        PaywaveDrlBeanDao.dropTable(db, ifExists);
        PBOCAidBeanDao.dropTable(db, ifExists);
        PureAidBeanDao.dropTable(db, ifExists);
        RupayAidBeanDao.dropTable(db, ifExists);
    }

    /**
     * WARNING: Drops all table on Upgrade! Use only during development.
     * Convenience method using a {@link DevOpenHelper}.
     */
    public static DaoSession newDevSession(Context context, String name) {
        Database db = new DevOpenHelper(context, name).getWritableDb();
        DaoMaster daoMaster = new DaoMaster(db);
        return daoMaster.newSession();
    }

    public DaoMaster(SQLiteDatabase db) {
        this(new StandardDatabase(db));
    }

    public DaoMaster(Database db) {
        super(db, SCHEMA_VERSION);
        registerDaoClass(AcqIssuerRelationDao.class);
        registerDaoClass(AcquirerDao.class);
        registerDaoClass(CapkRevokeBeanDao.class);
        registerDaoClass(CardBinDao.class);
        registerDaoClass(CardBinBlackDao.class);
        registerDaoClass(CardRangeDao.class);
        registerDaoClass(ClssTornLogDao.class);
        registerDaoClass(EmvAidDao.class);
        registerDaoClass(EmvCapkDao.class);
        registerDaoClass(IssuerDao.class);
        registerDaoClass(TransDataDao.class);
        registerDaoClass(TransTotalDao.class);
        registerDaoClass(AmexAidBeanDao.class);
        registerDaoClass(AmexDrlBeanDao.class);
        registerDaoClass(DpasAidBeanDao.class);
        registerDaoClass(EFTAidBeanDao.class);
        registerDaoClass(JcbAidBeanDao.class);
        registerDaoClass(MirAidBeanDao.class);
        registerDaoClass(PayPassAidBeanDao.class);
        registerDaoClass(PayWaveInterFloorLimitBeanDao.class);
        registerDaoClass(PaywaveAidBeanDao.class);
        registerDaoClass(PaywaveDrlBeanDao.class);
        registerDaoClass(PBOCAidBeanDao.class);
        registerDaoClass(PureAidBeanDao.class);
        registerDaoClass(RupayAidBeanDao.class);
    }

    public DaoSession newSession() {
        return new DaoSession(db, IdentityScopeType.Session, daoConfigMap);
    }

    public DaoSession newSession(IdentityScopeType type) {
        return new DaoSession(db, type, daoConfigMap);
    }

    /**
     * Calls {@link #createAllTables(Database, boolean)} in {@link #onCreate(Database)} -
     */
    public static abstract class OpenHelper extends DatabaseOpenHelper {
        public OpenHelper(Context context, String name) {
            super(context, name, SCHEMA_VERSION);
        }

        public OpenHelper(Context context, String name, CursorFactory factory) {
            super(context, name, factory, SCHEMA_VERSION);
        }

        @Override
        public void onCreate(Database db) {
            Log.i("greenDAO", "Creating tables for schema version " + SCHEMA_VERSION);
            createAllTables(db, false);
        }
    }

    /** WARNING: Drops all table on Upgrade! Use only during development. */
    public static class DevOpenHelper extends OpenHelper {
        public DevOpenHelper(Context context, String name) {
            super(context, name);
        }

        public DevOpenHelper(Context context, String name, CursorFactory factory) {
            super(context, name, factory);
        }

        @Override
        public void onUpgrade(Database db, int oldVersion, int newVersion) {
            Log.i("greenDAO", "Upgrading schema from version " + oldVersion + " to " + newVersion + " by dropping all tables");
            dropAllTables(db, true);
            onCreate(db);
        }
    }

}
