/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/27                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import com.pax.commonlib.utils.LogUtils;
import com.pax.preflib.R;
import com.pax.preflib.builder.callback.BindEditTextCallback;
import com.pax.preflib.builder.callback.InputValidateCallback;
import com.pax.preflib.builder.callback.PrefChangedCallback;
import com.pax.preflib.builder.factory.PrefFactory;
import com.pax.preflib.builder.pref.BaseAmountDialogPreference;
import java.util.LinkedList;
import java.util.List;

/**
 * Create AmountEditTextDialogPreference builder.
 */
public class AmountEditTextPrefBuilder extends BasePrefBuilder<BaseAmountDialogPreference, AmountEditTextPrefBuilder> {
    protected List<InputValidateCallback<Long>> inputValidateCallbackList = new LinkedList<>();
    protected List<BindEditTextCallback> bindEditTextCallbackList = new LinkedList<>();
    private final List<PrefChangedCallback<BaseAmountDialogPreference, Long>> prefChangedCallbackList = new LinkedList<>();
    protected boolean showSummary = true;

    protected AmountEditTextPrefBuilder(@NonNull Context context, @NonNull String key,
            @NonNull String title) {
        super(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param titleId Title string resource id
     * @return New instance
     */
    public static AmountEditTextPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @StringRes int titleId) {
        return new AmountEditTextPrefBuilder(context, key, context.getString(titleId));
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param title Title
     * @return New instance
     */
    public static AmountEditTextPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @NonNull String title) {
        return new AmountEditTextPrefBuilder(context, key, title);
    }

    /**
     * Add a callback to check whether the new value is legal before storing.
     * <br>
     * In fact, this callback is not much different from the {@code OnPreferenceChangeListener}
     * provided by the Preference library. But this callback passed the type conversion during
     * execution, so the value passed in this callback is a certain {@code Long} type instead
     * of {@code Object}. Using this callback, you don't need to perform type judgment and
     * conversion yourself.
     * <br>
     * Please do not execute the {@code setOnPreferenceChangeListener()} method after executing
     * this method. The {@code setOnPreferenceChangeListener()} method overrides the inspection
     * rules set by this method.
     * <br><br>
     * 添加用于在存储前检查新的值是否合法的回调。
     * <br>
     * 实际上，这个回调和 Preference 库提供的 {@code OnPreferenceChangeListener} 没有太大的区别。但是这个
     * 回调在执行时通过了类型转换，因此，这个回调传入的值是确定的 {@code Long} 类型，而不是 {@code Object}。
     * 使用这个回调，你就不需要自己再进行类型判断和转换了。
     * <br>
     * 请您不要在执行了该方法以后再执行 {@code setOnPreferenceChangeListener()} 方法。{@code
     * setOnPreferenceChangeListener()} 方法会覆盖该方法设定的检查规则。
     *
     * @param callback A callback to check whether the new value is legal before storing. The
     * callback will put in a Long value, which is the value the user wants to save. If this
     * value is legal, then please return {@code true} so that this value will be stored. If it
     * is not legal, please return {@code false} and this value will be discarded.
     * <br>
     * 用于在存储前检查新的值是否合法的回调。该回调会传入一个 Long 值，这个值就是用户想要保存的值。如果这个值是合法
     * 的，那么请返回 {@code true}，这样就会将这个值存储。如果不合法，请返回 {@code false}，这个值就会被丢弃。
     * @return This builder
     */
    public AmountEditTextPrefBuilder addPrefChangedCallback(@NonNull PrefChangedCallback<BaseAmountDialogPreference, Long> callback) {
        prefChangedCallbackList.add(callback);
        return this;
    }

    /**
     * Add a callback to verify that the input content is legal. This callback will pass in the
     * new amount entered by the user via the {@code onChange()} method. You can verify whether
     * the new amount entered is legal by checking this value. If it is not legal, return false,
     * so that the user can be prevented from entering this content, and the text in the input
     * box will not change. If it returns true, the user's current input is allowed.
     * <br>
     * Please note, do not regard this callback as {@code OnPreferenceChangeListener} or {@code
     * PrefChangedCallback}, the purpose of the two is completely different. This callback is to
     * check whether the content you input is legal, and {@code OnPreferenceChangeListener} or
     * {@code PrefChangedCallback} is to check whether the content you want to store is legal.
     * This callback will be executed every time the user enters or deletes a number, and
     * {@code OnPreferenceChangeListener} or {@code PrefChangedCallback} will only execute once
     * before storing the data.
     * <br>
     * Since this method is to add a callback instead of set callback, so you can add multiple
     * callbacks to an {@code AmountEditTextPrefBuilder}.
     * <br><br>
     * 添加一个验证输入内容是否合法的回调。这个回调会将用户输入的新金额通过 {@code onChange()} 方法传入。
     * 你可以通过检查这个数值来验证输入的新金额是否合法。如果不合法，则返回 {@code false}，这样就会阻止用户输入
     * 这个新内容，而输入框仍然保持原来的内容。如果返回 {@code true}，则用户的输入将被批准。
     * <br>
     * 请注意，不要把这个回调和 {@code OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 搞
     * 混了，两者是完全不同的。{@code TextValidateCallback} 这个回调是用来检查用户正在输入的内容是否合法，而 {@code
     * OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 是用来检查用户想要保存的内容是否合法。
     * {@code TextValidateCallback} 这个回调会在每次用户输入或者删除一个数字的时候就被执行，而 {@code
     * OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 只有在保存数据前才会执行一次。
     * <br>
     * 由于这个方法是添加一个 callback 而非设置一个 callback，所以你可以对一个 {@code AmountEditTextPrefBuilder}
     * 执行多次 {@code addTextValidateCallback} 方法。
     *
     * @param callback A callback to verify that the input amount is legal. <br>验证正在输入的金额是否合法的回调
     * @return This builder
     */
    public AmountEditTextPrefBuilder addInputValidateCallback(@NonNull InputValidateCallback<Long> callback) {
        inputValidateCallbackList.add(callback);
        return this;
    }

    /**
     * Add a callback to be executed when {@code EditText} is bound. When the user clicks {@code
     * EditTextPreference} and a dialog with {@code EditText} pops up, this callback will be
     * executed, and this {@code EditText} instance will be passed in via the {@code onBind()}
     * method. You can use this {@code EditText} to perform some setting operations.
     * <br><br>
     * 添加一个绑定 {@code EditText} 时执行的回调。当用户点击 {@code EditTextPreference}，弹出带有 {@code
     * EditText} 的对话框时，这个回调将会被执行，并通过 {@code onBind()} 方法传入这个 {@code EditText} 实例。
     * 你可以利用这个 {@code EditText} 来进行一些设置操作。
     *
     * @param callback A callback to be executed when {@code EditText} is bound
     * @return This builder
     */
    public AmountEditTextPrefBuilder addBindEditTextCallback(@NonNull BindEditTextCallback callback) {
        bindEditTextCallbackList.add(callback);
        return this;
    }

    /**
     * Whether to display the content summary. The default is {@code true}. If it is set to
     * {@code false}, the user can see the saved content of this option on {@code EditText} only
     * after clicking {@code EditTextPreference}, and a summary of "(HIDE)" will be displayed for
     * this option.
     * <br>
     * Please note that this option does not hide the content displayed after the user clicks
     * {@code EditTextPreference}. If you need to completely hide the content of this option,
     * please do so by customizing the DataStore.
     * <br><br>
     * 是否显示内容摘要。默认为 {@code true}。如果设置 {@code false}，那么用户只有点击 {@code
     * EditTextPreference} 以后才能在 {@code EditText} 上看到这个选项保存的内容，并且会给这个选项显示一个
     * "(HIDE)" 的摘要。
     * <br>
     * 请注意，这个选项并不会隐藏用户点击 {@code EditTextPreference} 以后显示的内容。如果你需要彻底隐藏该选项
     * 的内容，请通过自定义 DataStore 来实现。
     *
     * @param showSummary Whether to display the content summary.
     * @return This builder
     */
    public AmountEditTextPrefBuilder setShowSummary(boolean showSummary) {
        this.showSummary = showSummary;
        return this;
    }

    private boolean verifyNewValue(BaseAmountDialogPreference preference, Long newValue) {
        for (PrefChangedCallback<BaseAmountDialogPreference, Long> callback : prefChangedCallbackList) {
            if (!callback.onChanged(preference, newValue)) {
                return false;
            }
        }
        return true;
    }

    @NonNull
    @Override
    public BaseAmountDialogPreference build() {
        BaseAmountDialogPreference preference = PrefFactory.getInstance()
                .createAmountEditTextDialogPreference(context);
        preference.setKey(key);
        preference.setTitle(title);
        preference.setDialogTitle(title);
        preference.setIconSpaceReserved(false);
        if (!bindEditTextCallbackList.isEmpty()) {
            preference.setOnBindEditTextListener(editText -> {
                for (BindEditTextCallback callback : bindEditTextCallbackList) {
                    callback.onBind(editText);
                }
            });
        }
        if (!inputValidateCallbackList.isEmpty()) {
            preference.setInputValidateCallback(content -> {
                for (InputValidateCallback<Long> callback : inputValidateCallbackList) {
                    if (!callback.onChanged(content)) {
                        return false;
                    }
                }
                return true;
            });
        }
        if (!prefChangedCallbackList.isEmpty()) {
            preference.setOnPreferenceChangeListener((pref, newValue) -> {
                if (newValue instanceof Number) {
                    return verifyNewValue(preference, (long) newValue);
                } else if (newValue != null) {
                    String s = newValue.toString();
                    if (TextUtils.isEmpty(s)) {
                        return false;
                    }
                    try {
                        long value = Long.parseLong(s);
                        return verifyNewValue(preference, value);
                    } catch (Exception e) {
                        LogUtils.e(e);
                    }
                    return false;
                } else {
                    return false;
                }
            });
        }
        if (showSummary) {
            preference.setSummaryProvider(BaseAmountDialogPreference.AmountSummaryProvider.getInstance());
        } else {
            preference.setSummaryProvider(null);
            preference.setSummary(R.string.configui_hide_value);
        }
        if (onPreferenceClickListener != null) {
            preference.setOnPreferenceClickListener(onPreferenceClickListener);
        }
        return preference;
    }
}
