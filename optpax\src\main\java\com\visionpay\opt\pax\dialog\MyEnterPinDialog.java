/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.dialog;

import android.content.Context;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.callback.OnBindEditTextCallback;

/**
 * Enter PIN Dialog.
 */
public class MyEnterPinDialog extends BottomSheetDialog {//implements View.OnClickListener {

    //private String pin = "";
    //private Context context;

    private OnBindEditTextCallback onBindEditTextCallback;

    public MyEnterPinDialog(@NonNull Context context) {
        super(context, R.style.BottomSheetDialogTheme);
        //this.context = context;
        getBehavior().setPeekHeight(1880);
        setCancelable(false);
        setCanceledOnTouchOutside(false);

        setContentView(R.layout.dialog_my_enter_pin);


        /*findViewById(R.id.buttonEnter).setOnClickListener(this);
        findViewById(R.id.buttonClear).setOnClickListener(this);
        findViewById(R.id.button0).setOnClickListener(this);
        findViewById(R.id.button1).setOnClickListener(this);
        findViewById(R.id.button2).setOnClickListener(this);
        findViewById(R.id.button3).setOnClickListener(this);
        findViewById(R.id.button4).setOnClickListener(this);
        findViewById(R.id.button5).setOnClickListener(this);
        findViewById(R.id.button6).setOnClickListener(this);
        findViewById(R.id.button7).setOnClickListener(this);
        findViewById(R.id.button8).setOnClickListener(this);
        findViewById(R.id.button9).setOnClickListener(this);*/

        //TODO implemetns min and max pin length
    }

    public MyEnterPinDialog setTitle(String title) {
        ((TextView)findViewById(R.id.enter_pin_title)).setText(title);
        return this;
    }


    public MyEnterPinDialog setOnBindEditTextCallback(@Nullable OnBindEditTextCallback onBindEditTextCallback) {
        this.onBindEditTextCallback = onBindEditTextCallback;
        TextView editText = findViewById(R.id.txvPin);
        if (onBindEditTextCallback != null) {
            onBindEditTextCallback.onBind(editText);
        }
        return this;
    }

    /*@Override
    public void onClick(View view) {
        switch (view.getId()){
            case R.id.buttonEnter:
                listener.onComplete(generatePinBlock(pin, panBlock));
                dismiss();
                if(context instanceof BaseActivity)
                    ((BaseActivity)context).hide();
                break;
            case R.id.buttonClear:
                changeMe("");
                break;
            default:
                changeMe(pin + ((Button)view).getText().toString());
                break;
        }
    }

    public void changeMe(String word) {
        pin = word + "";
        TextView txvPin = findViewById(R.id.txvPin);
        if(pin.length() > 0)
            txvPin.setText(String.format("%1$" + pin.length() + "s", "").replace(' ', '*'));
        else
            txvPin.setText("");
    }

    public void setOnCompletePinListener(OnCompletePinListener listener){
        this.listener = listener;
    }

    public interface OnCompletePinListener{
        void onComplete(byte[] pinBlock);
    }

    private int fromHex(char c) {
        if (c >= '0' && c <= '9') {
            return c - '0';
        }
        if (c >= 'A' && c <= 'F') {
            return c - 'A' + 10;
        }
        if (c >= 'a' && c <= 'f') {
            return c - 'a' + 10;
        }
        throw new IllegalArgumentException();
    }

    private char toHex(int nybble) {
        if (nybble < 0 || nybble > 15) {
            throw new IllegalArgumentException();
        }
        return "0123456789ABCDEF".charAt(nybble);
    }


    private byte[] xorHex(String a, String b) {
        char[] chars = new char[a.length()];
        for (int i = 0; i < chars.length; i++) {
            chars[i] = toHex(fromHex(a.charAt(i)) ^ fromHex(b.charAt(i)));
        }
        return new String(chars).toUpperCase().getBytes();
    }


    public byte[] generatePinBlock(String pin, String panBlock) {
        if(pin.length() < 4 || pin.length() > 12) { // Throw exception here
            return null;
        }
        //  as an alternative to below pinBlock formation, you can execute: String pinBlock = StringUtils.rightPad("0" + pin, 16, 'F');
        String pinBlock = String.format("%s%d%s", "0", pin.length(), pin);
        while(pinBlock.length() != 16) {
            pinBlock += "F";
        }

        return xorHex(pinBlock,panBlock);
    }*/
}
