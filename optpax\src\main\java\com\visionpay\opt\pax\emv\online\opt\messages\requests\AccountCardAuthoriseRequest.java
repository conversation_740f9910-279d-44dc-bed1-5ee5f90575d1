package com.visionpay.opt.pax.emv.online.opt.messages.requests;

import androidx.annotation.Keep;

@Keep
public class AccountCardAuthoriseRequest {
    private String terminalID;
    private long transactionReferece;
    private String pan;
    private String expiryDate;
    private String track2;
    private String pin;
    private long amount;

    public String getTerminalID() {
        return terminalID;
    }

    public void setTerminalID(String terminalID) {
        this.terminalID = terminalID;
    }

    public long getTransactionReferece() {
        return transactionReferece;
    }

    public void setTransactionReferece(long transactionReferece) {
        this.transactionReferece = transactionReferece;
    }

    public String getPan() {
        return pan;
    }

    public void setPan(String pan) {
        this.pan = pan;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getTrack2() {
        return track2;
    }

    public void setTrack2(String track2) {
        this.track2 = track2;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public long getAmount() {
        return amount;
    }

    public void setAmount(long amount) {
        this.amount = amount;
    }
}
