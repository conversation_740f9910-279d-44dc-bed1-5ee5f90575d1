/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/22                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import com.pax.commonui.clss.ClssLightsView;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.viewmodel.TransPromptViewModel;

/**
 * Transaction Search Card Prompt Fragment.
 */
public class TransPromptFragment extends BaseTransFragment {
    private static final String TAG = "TransPromptFragment";

    private TransPromptViewModel viewModel;

    private TextView promptTextView;
    private ClssLightsView clssLightsView;
    private ImageView insertCardIcon;
    private TextView insertCardTitle;
    private TextView insertCardInfo;
    private ImageView tapCardIcon;
    private TextView tapCardTitle;
    private TextView tapCardInfo;

    @NonNull
    @Override
    public String getFragmentTag() {
        return TransFragmentManager.SEARCH_CARD_PROMPT;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(this).get(TransPromptViewModel.class);
        viewModelPrepared(viewModel);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_search_card, container, false);
        promptTextView = view.findViewById(R.id.trans_prompt_text);
        clssLightsView = view.findViewById(R.id.trans_clss_light);
        insertCardIcon = view.findViewById(R.id.trans_prompt_insert_icon);
        insertCardTitle = view.findViewById(R.id.trans_prompt_insert_title);
        insertCardInfo = view.findViewById(R.id.trans_prompt_insert_info);
        tapCardIcon = view.findViewById(R.id.trans_prompt_tap_icon);
        tapCardTitle = view.findViewById(R.id.trans_prompt_tap_title);
        tapCardInfo = view.findViewById(R.id.trans_prompt_tap_info);
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        if (viewModel != null) {
            // Prompt Text
            viewModel.listenPromptTextDisplay().observe(getViewLifecycleOwner(), isDisplay ->
                    promptTextView.setVisibility(isDisplay ? View.VISIBLE : View.GONE));
            viewModel.listenPromptText().observe(getViewLifecycleOwner(), prompt ->
                    promptTextView.setText(prompt));

            // Clss Light
            viewModel.listenLight1Status().observe(getViewLifecycleOwner(), status -> {
                clssLightsView.setLights(0, status, false);
            });
            viewModel.listenLight2Status().observe(getViewLifecycleOwner(), status -> {
                clssLightsView.setLights(1, status, false);
            });
            viewModel.listenLight3Status().observe(getViewLifecycleOwner(), status -> {
                clssLightsView.setLights(2, status, false);
            });
            viewModel.listenLight4Status().observe(getViewLifecycleOwner(), status -> {
                clssLightsView.setLights(3, status, false);
            });

            // Icc Prompt
            viewModel.listenInsertDisplay().observe(getViewLifecycleOwner(), isDisplay -> {
                insertCardIcon.setVisibility(isDisplay ? View.VISIBLE : View.GONE);
                insertCardTitle.setVisibility(isDisplay ? View.VISIBLE : View.GONE);
                insertCardInfo.setVisibility(isDisplay ? View.VISIBLE : View.GONE);
            });
            viewModel.listenInsertIconEnabled().observe(getViewLifecycleOwner(), isEnable ->
                    insertCardIcon.setEnabled(isEnable));
            viewModel.listenInsertInfo().observe(getViewLifecycleOwner(), info ->
                    insertCardInfo.setText(info));

            // Picc Prompt
            viewModel.listenTapDisplay().observe(getViewLifecycleOwner(), isDisplay -> {
                tapCardIcon.setVisibility(isDisplay ? View.VISIBLE : View.GONE);
                tapCardTitle.setVisibility(isDisplay ? View.VISIBLE : View.GONE);
                tapCardInfo.setVisibility(isDisplay ? View.VISIBLE : View.GONE);
            });
            viewModel.listenTapIconEnabled().observe(getViewLifecycleOwner(), isEnable ->
                    tapCardIcon.setEnabled(isEnable));
            viewModel.listenTapInfo().observe(getViewLifecycleOwner(), info ->
                    tapCardInfo.setText(info));
        }
    }
}
