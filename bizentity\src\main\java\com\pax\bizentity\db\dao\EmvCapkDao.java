package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.EmvCapk;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "capk".
*/
public class EmvCapkDao extends AbstractDao<EmvCapk, Long> {

    public static final String TABLENAME = "capk";

    /**
     * Properties of entity EmvCapk.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "id");
        public final static Property RID = new Property(1, String.class, "rID", false, "rid");
        public final static Property KeyID = new Property(2, int.class, "keyID", false, "KEY_ID");
        public final static Property HashInd = new Property(3, int.class, "hashInd", false, "HASH_IND");
        public final static Property ArithInd = new Property(4, int.class, "arithInd", false, "ARITH_IND");
        public final static Property Module = new Property(5, String.class, "module", false, "MODULE");
        public final static Property Exponent = new Property(6, String.class, "exponent", false, "EXPONENT");
        public final static Property ExpDate = new Property(7, String.class, "expDate", false, "EXP_DATE");
        public final static Property CheckSum = new Property(8, String.class, "checkSum", false, "CHECK_SUM");
    }


    public EmvCapkDao(DaoConfig config) {
        super(config);
    }
    
    public EmvCapkDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"capk\" (" + //
                "\"id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"rid\" TEXT NOT NULL ," + // 1: rID
                "\"KEY_ID\" INTEGER NOT NULL ," + // 2: keyID
                "\"HASH_IND\" INTEGER NOT NULL ," + // 3: hashInd
                "\"ARITH_IND\" INTEGER NOT NULL ," + // 4: arithInd
                "\"MODULE\" TEXT," + // 5: module
                "\"EXPONENT\" TEXT," + // 6: exponent
                "\"EXP_DATE\" TEXT," + // 7: expDate
                "\"CHECK_SUM\" TEXT);"); // 8: checkSum
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"capk\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, EmvCapk entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindString(2, entity.getRID());
        stmt.bindLong(3, entity.getKeyID());
        stmt.bindLong(4, entity.getHashInd());
        stmt.bindLong(5, entity.getArithInd());
 
        String module = entity.getModule();
        if (module != null) {
            stmt.bindString(6, module);
        }
 
        String exponent = entity.getExponent();
        if (exponent != null) {
            stmt.bindString(7, exponent);
        }
 
        String expDate = entity.getExpDate();
        if (expDate != null) {
            stmt.bindString(8, expDate);
        }
 
        String checkSum = entity.getCheckSum();
        if (checkSum != null) {
            stmt.bindString(9, checkSum);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, EmvCapk entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindString(2, entity.getRID());
        stmt.bindLong(3, entity.getKeyID());
        stmt.bindLong(4, entity.getHashInd());
        stmt.bindLong(5, entity.getArithInd());
 
        String module = entity.getModule();
        if (module != null) {
            stmt.bindString(6, module);
        }
 
        String exponent = entity.getExponent();
        if (exponent != null) {
            stmt.bindString(7, exponent);
        }
 
        String expDate = entity.getExpDate();
        if (expDate != null) {
            stmt.bindString(8, expDate);
        }
 
        String checkSum = entity.getCheckSum();
        if (checkSum != null) {
            stmt.bindString(9, checkSum);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public EmvCapk readEntity(Cursor cursor, int offset) {
        EmvCapk entity = new EmvCapk( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.getString(offset + 1), // rID
            cursor.getInt(offset + 2), // keyID
            cursor.getInt(offset + 3), // hashInd
            cursor.getInt(offset + 4), // arithInd
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // module
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // exponent
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // expDate
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8) // checkSum
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, EmvCapk entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setRID(cursor.getString(offset + 1));
        entity.setKeyID(cursor.getInt(offset + 2));
        entity.setHashInd(cursor.getInt(offset + 3));
        entity.setArithInd(cursor.getInt(offset + 4));
        entity.setModule(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setExponent(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setExpDate(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setCheckSum(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(EmvCapk entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(EmvCapk entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(EmvCapk entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
