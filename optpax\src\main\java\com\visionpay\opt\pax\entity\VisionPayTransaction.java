package com.visionpay.opt.pax.entity;

import androidx.annotation.Keep;

import com.pax.bizentity.entity.Issuer;
import com.pax.bizentity.entity.TransData;
import com.pax.bizlib.trans.AcqManager;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.pax.poslib.model.ModelInfo;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.utils.DateUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

@Keep
public class VisionPayTransaction {
    private Date createdUTCDateTime;
    private Date completedUTCDateTime;
    private String externalReference;
    private String im30Reference;
    private IM30State im30State;
    private long transactionAmount;
    private TransactionStatus transactionStatus;
    private TransactionType transactionType;
    private int transactionCurrency;
    private String cardType;
    private String cardSignature;
    private String cardSequenceNumber;
    private CardVerificationMethod cardVerificationMethod;
    private String cardExpiryDate;
    private String applicationId;
    private String applicationLabel;
    private int applicationTransactionCounter;
    private String authorizationId;
    private String merchantId;
    private String im30TerminalId;
    private String stan;
    private String gatewayResponse;
    private String gatewayResponseCode;
    private AuthorizationType authorizationType;

    public VisionPayTransaction(POSTransaction post, IM30Transaction im30t, TransactionType transactionType){
        createdUTCDateTime = DateUtils.gettUtcTime(new Date());
        completedUTCDateTime = DateUtils.gettUtcTime(new Date());
        externalReference = post.getExternalReference();
        im30Reference = "";//im30t.getIm30Reference();
        im30State = IM30State.AwaitingCardRead;
        transactionAmount = post.getTransactionAmount();
        transactionStatus = TransactionStatus.None;
        this.transactionType = transactionType;
        transactionCurrency = post.getTransactionCurrency();
        cardType = "";
        cardSignature = "";
        cardSequenceNumber = "";
        cardVerificationMethod = CardVerificationMethod.None;
        cardExpiryDate = "";
        applicationId = "";
        applicationLabel = "";
        applicationTransactionCounter = 0;
        authorizationId = "";
        merchantId = "";
        stan = "";
        gatewayResponse = "";
        gatewayResponseCode = "";
        authorizationType = AuthorizationType.Unknown;

        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        im30TerminalId = configParamService.getString(ConfigKeyConstant.EDC_CATID);
    }

    public void loadTransData(TransData transData){
        im30Reference = transData.getId() + "";
        transactionAmount = Long.parseLong(transData.getAmount());
        //cardType = transData.getEmvAppLabel();
        setCardSignature(transData.getPan());
        //cardSequenceNumber = "";
        cardVerificationMethod = transData.getHasPin() ? CardVerificationMethod.PIN : CardVerificationMethod.None;
        merchantId = transData.getIssuerCode();
        cardExpiryDate = transData.getExpDate();
        applicationId = transData.getAid();
        applicationLabel = transData.getEmvAppLabel();
        applicationTransactionCounter = Integer.parseInt(transData.getAtc());
        authorizationId = transData.getAuthCode();
        cardSequenceNumber = transData.getCardSerialNo();

        Issuer matchedIssuer = AcqManager.getInstance().findIssuerByPan(transData.getPan());
        if (matchedIssuer != null) {
            authorizationType = matchedIssuer.isBankCard() ? AuthorizationType.BankCard : AuthorizationType.WhiteCard;
            String name = matchedIssuer.getName();
            EIssuer info = EIssuer.parse(name);
            if (info != null) {
                cardType = info.getDisplayName();
            }
            else {
                cardType = name;
            }
        }
        else {
            cardType = "Unknown";
        }
    }
    public Date getCreatedUTCDateTime() {
        return createdUTCDateTime;
    }

    public void setCreatedUTCDateTime(Date createdUTCDateTime) {
        this.createdUTCDateTime = createdUTCDateTime;
    }

    public Date getCompletedUTCDateTime() {
        return completedUTCDateTime;
    }

    public void setCompletedUTCDateTime(Date completedUTCDateTime) {
        this.completedUTCDateTime = DateUtils.gettUtcTime(completedUTCDateTime);
    }

    public String getExternalReference() {
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public String getIm30Reference() {
        return im30Reference;
    }

    public void setIm30Reference(String im30Reference) {
        this.im30Reference = im30Reference;
    }

    public IM30State getIm30State() {
        return im30State;
    }

    public void setIm30State(IM30State im30State) {
        this.im30State = im30State;
        completedUTCDateTime = DateUtils.gettUtcTime(new Date());
    }

    public long getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(long transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public TransactionStatus getTransactionStatus() {
        return transactionStatus;
    }

    public void setTransactionStatus(TransactionStatus transactionStatus) {
        this.transactionStatus = transactionStatus;
    }

    public TransactionType getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(TransactionType transactionType) {
        this.transactionType = transactionType;
    }

    public int getTransactionCurrency() {
        return transactionCurrency;
    }

    public void setTransactionCurrency(int transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardSignature() {
        return cardSignature;
    }

    public void setCardSignature(String cardSignature) {
        this.cardSignature = cardSignature.length() > 6 ? cardSignature.substring(0, 6) + "**********" + cardSignature.substring(cardSignature.length() - 4) : "";
    }

    public String getCardSequenceNumber() {
        return cardSequenceNumber;
    }

    public void setCardSequenceNumber(String cardSequenceNumber) {
        this.cardSequenceNumber = cardSequenceNumber;
    }

    public CardVerificationMethod getCardVerificationMethod() {
        return cardVerificationMethod;
    }

    public void setCardVerificationMethod(CardVerificationMethod cardVerificationMethod) {
        this.cardVerificationMethod = cardVerificationMethod;
    }

    public String getCardExpiryDate() {
        return cardExpiryDate;
    }

    public void setCardExpiryDate(String cardExpiryDate) {
        this.cardExpiryDate = cardExpiryDate;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getApplicationLabel() {
        return applicationLabel;
    }

    public void setApplicationLabel(String applicationLabel) {
        this.applicationLabel = applicationLabel;
    }

    public int getApplicationTransactionCounter() {
        return applicationTransactionCounter;
    }

    public void setApplicationTransactionCounter(int applicationTransactionCounter) {
        this.applicationTransactionCounter = applicationTransactionCounter;
    }

    public String getAuthorizationId() {
        return authorizationId;
    }

    public void setAuthorizationId(String authorizationId) {
        this.authorizationId = authorizationId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getIm30TerminalId() {
        return im30TerminalId;
    }

    public void setIm30TerminalId(String im30TerminalId) {
        this.im30TerminalId = im30TerminalId;
    }

    public String getStan() {
        return stan;
    }

    public void setStan(String stan) {
        this.stan = stan;
    }

    public String getGatewayResponse() {
        return gatewayResponse;
    }

    public void setGatewayResponse(String gatewayResponse) {
        this.gatewayResponse = gatewayResponse;
    }

    public String getGatewayResponseCode() {
        return gatewayResponseCode;
    }

    public void setGatewayResponseCode(String gatewayResponseCode) {
        this.gatewayResponseCode = gatewayResponseCode;
    }

    public AuthorizationType getAuthorizationType() {
        return authorizationType;
    }

    public void setAuthorizationType(AuthorizationType authorizationType) {
        this.authorizationType = authorizationType;
    }
}

