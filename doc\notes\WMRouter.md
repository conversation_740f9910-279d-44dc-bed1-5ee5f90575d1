# WMRouter

WMRouter is an Android open-source routing library, based on componentized design ideas, with flexible functions and good usability.

WMRoute provides two functions: URI distribution and ServiceLoader. The former is responsible for starting the Activity or Fragment page, and the latter is responsible for creating an implementation instance of the interface.

You can check the official documentation [here](https://github.com/meituan/WMRouter/blob/master/docs/user-manual.md).

> Since the library is developed by a Chinese group, only Chinese documentation is provided. You can translate official documents using translation tools. We also provide a brief manual to illustrate the usage of the library.

## 1. Quick Start

### 1.1 Download

Add the following snippet to the *build.gradle* file in the project root directory:

```groovy
buildscript {
    // ...
    dependencies {
        // ...
        classpath 'io.github.meituan-dianping:plugin:1.2.1'
    }
    // ...
}
```

Then add the following snippet to the *build.gradle* file in application module (such as app module):

```groovy
apply plugin: 'WMRouter'

// ...
```

And if you want to use <PERSON><PERSON>out<PERSON> in one specific module (include application module and library module), you need to add the following snippet to the *build.gradle* file of the specific module.

For Java module:

```groovy
// ...

dependencies {
    // If you want to use Router.getService() or Router.startUri()
    // and other method, please add this:
    implementation 'io.github.meituan-dianping:router:1.2.1'
    // If you want to use @RouterService annotation, please add this:
    annotationProcessor 'io.github.meituan-dianping:compiler:1.2.1'

    // ...
}
```

For Kotlin module:

```groovy
apply plugin: 'kotlin-kapt'

// ...

dependencies {
    // If you want to use Router.getService() or Router.startUri()
    // and other method, please add this:
    implementation 'io.github.meituan-dianping:router:1.2.1'
    // If you want to use @RouterService annotation, please add this:
    kapt 'io.github.meituan-dianping:compiler:1.2.1'

    // ...
}
```

Then add the following proguard configurations to *proguard-rules.pro* file of *app* module:

```
# Retain the ServiceLoaderInit class, which needs to be called by reflection
-keep class com.sankuai.waimai.router.generated.ServiceLoaderInit { *; }

# To avoid annotations being removed in the shrink phase, the annotations in the
# obfuscate phase are invalid and the implementation class is still confused
-keep @interface com.sankuai.waimai.router.annotation.RouterService

# For implementation classes annotated with RouterService, it is necessary to
# avoid Proguard from shrinking or obfuscating members such as constructors
# and methods, resulting in the inability to reflect calls. The class name of the
# implementing class can be obfuscated.
-keepclassmembers @com.sankuai.waimai.router.annotation.RouterService class * { *; }
```

### 1.2 Initialization

Add the following snippet to the Application class:

```java
public class App extends Application {
    @Override
    protected void onCreate() {
        // ...

        DefaultRootUriHandler rootHandler = new DefaultRootUriHandler(this);
        Router.init(rootHandler);
    }
}
```

### 1.3 Jump to Activity

Firstly, you need to add an annotation to the Activity:

```java
// Use the RouterUri annotation, which passes in a path. Just make sure
// it's unique.
@RouterUri(path = "/test")
public class TestActivity extends AppCompatActivity {
    // ...
}
```

Next, you need to jump to this path:

```java
// The first parameter is Context, it is recommended to pass in Activity
// The second parameter is the path corresponding to the Activity
Router.startUri(context, "/test");

// or

Router.startUri(new UriRequest(context, "/test"));

// or

new DefaultUriRequest(context, "/test")
        .start();
```

In this way, your application can successfully jump to `TestActivity`.

It is not difficult to find that routing between activities in this way does not need to reference the specific class of the target activity. Therefore, it is very suitable for activities in different modules to jump to each other, because it can reduce the direct reference to the classes in the other module.

## 2. URI distribution

### 2.1 Send data to Activity

WMRouter uses Intent to implement Activity routing internally, so it can carry data when jumping like `Intent`, and the way of destination Activity receives data does not need to be changed.

```java
public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // ...

        // Jump to TestActivity and send data
        new DefaultUriRequest(context, "/test")
                // add data to Intent
                .putExtra("test-int", 1)  // optional
                .putExtra("test-string", "str")   // optional
                .start();
    }
}
```

```java
@RouterUri(path = "/test")
public class TestActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // ...

        // Receive data (Same as original method)
        Bundle extras = getIntent().getExtras();
        if (extras != null) {
            int testInt = extras.getInt("test-int");    // 1
            String testStr = extras.getString("test-string");   // str
        }
    }
}
```

### 2.2 Receive data returned by Activity

As mentioned in the previous section, WMRouter uses the Intent to implement Activity routing internally, so after Activity jumps to other Activities, the way to receive data returned from other Activities is the same as the standard API.

```java
public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // ...

        // Jump to TestActivity and receive data
        new DefaultUriRequest(context, "/test")
                // set request code
                .activityRequestCode(100)
                .start();
    }

    @Override
    protected void onActivityResult(int requestCode, 
                                    int resultCode, 
                                    Intent data) {
        // ...
        if (requestCode == 100) {
            // receive data
            if (data != null) {
                String str = data.getStringExtra("data");   // ABCDEFG
                // ...
            }
        }
    }
}
```

```java
@RouterUri(path = "/test")
public class TestActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // ...

        // Set result and finish
        Intent result = new Intent();
        intent.putExtra("data", "ABCDEFG");
        setResult(RESULT_OK, intent);
        finish();
    }
}
```

### 2.3 Interceptor

WMRouter supports executing interceptors when routing. If you want to create an interceptor, you need to create a class that implements the `UriInterceptor` interface.

```java
public class PermissionCheckInterceptor implements UriInterceptor {
    @Override
    public void intercept(@NonNull UriRequest request, 
                          @NonNull final UriCallback callback) {
        if (UserManager.getInstance()
            	.getCurrentUser()
            	.hasPermission(request.getUri().getPath())) {
            // Execute next interceptor or jump to Activity
            callback.onNext();
        } else {
            // Intercept the request and set the error code
            // The custom error code is preferably a negative number
            callback.onComplete(CustomUriResult.NO_PERMISSION);
        }
    }
}
```

Then add this interceptor to `@RouterUri` annotation of corresponding destination Activity:

```java
@RouterUri(path = "/test", interceptors = PermissionCheckInterceptor.class)
public class TestActivity extends AppCompatActivity {
    // ...
}
```

### 2.4 Get whether the jump is successful

Sometimes you may want to know whether the jump was successful after sending a request. You can know whether the jump is successful through the `onComplete()` method of `DefaultUriRequest`.

```java
public class MainActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // ...

        // Jump to TestActivity and check jump status
        new DefaultUriRequest(context, "/test")
            	.onComplete(new OnCompleteListener() {
                    @Override
                    public void onSuccess(@NonNull UriRequest request) {
                        // Jump success
                    }
                    
                    @Override
                    public void onError(@NonNull UriRequest request, 
                                        int resultCode) {
                        // Jump failed
                    }
                })
                .start();
    }
}
```

## 3. ServiceLoader

The ServiceLoader function of WMRouter can create an instance of the implementation class only using the interface class and the key of the implementation class.

For example, there is such an interface:

```java
public interface IUser {
    void login(String username, String password);
}
```

There are also interface implementation classes:

```java
@RouterService(interfaces = IUser.class, key = "OKHTTP")
public class OkhttpUserImpl implements IUser {
    @Override
    public void login(String username, String password) {
        // ...
    }
}
```

```java
@RouterService(interfaces = IUser.class, key = "RETROFIT")
public class RetrofitUserImpl implements IUser {
    @Override
    public void login(String username, String password) {
        // ...
    }
}
```

Then you can create an instance of the implementation class with code like this:

```java
// Get an instance of OkhttpUserImpl class
IUser okhttpUser = Router.getService(IUser.class, "OKHTTP");

// Get an instance of RetrofitUserImpl class
IUser retrofitUser = Router.getService(IUser.class, "RETROFIT");
```

It's easy to notice that you can create an instance of a class without referencing the class itself. The advantage of this create instance method is that we can put the interface and key in a separate module, and the classes that implement the interface are placed in another module. In this way, the specific implementation is hidden, and the circular dependency between modules is solved.

### 3.1 Get implementation class `Class`

```java
// Get OkhttpUserImpl Class
Class<IUser> clazz = Router.getServiceClass(IUser.class, "OKHTTP");

// Get all implementation classes of IUser interface
List<Class<IUser>> classes = Router.getAllServiceClasses(IUser.class);
```

### 3.2 Get implementation class instance

```java
// Get an instance of OkhttpUserImpl class
IUser okhttpUser = Router.getService(IUser.class, "OKHTTP");

// Get all instances of IUser interface implementation class
List<IUser> users = Router.getAllServices(IUser.class);
```

Sometimes the constructor of the implementation class needs to pass in parameters. If the implementation class only needs to pass in Context:

```java
// Get an instance
IUser okhttpUser = Router.getService(IUser.class, "OTHER", context);

// Get all instances of IUser interface implementation class
List<IUser> users = Router.getAllServices(IUser.class, context);
```

But if you need to pass in other types of parameters, you need to create an instance by reflection.

```java
IFactory factory = new IFactory() {
	public Object create(Class clazz) {
		String arg1 = "ARG 1";
		String arg2 = "ARG 2";
		
		return clazz.getConstructor().newInstance(arg1, arg2);
	}
}

// Get an instance
IUser okhttpUser = Router.getService(IUser.class, "OTHER", factory);

// Get all instances of IUser interface implementation class
List<IUser> users = Router.getAllServices(IUser.class, factory);
```

Or sometimes you provide a **static** method of creating an instance without parameters in the implementation class (such as the `getInstance()` method), then you can use the `@RouterProvider` annotation to declare to WMRouter to use this method to get the instance.

```java
@RouterService(interfaces = IUser.class, key = "ROUTER_PROVIDER_TEST")
public class OtherUserImpl implements IUser {
    private OtherUserImpl() {
        // do nothing
    }
    
    private static class Holder {
        private static final OtherUserImpl INSTANCE = new OtherUserImpl();
    }
    
    @RouterProvider
    public static OtherUserImpl getInstance() {
        return Holder.INSTANCE;
    }
    
    // ...
}
```

```java
// Get an instance of OtherUserImpl class
IUser otherUser = Router.getService(IUser.class, "ROUTER_PROVIDER_TEST");
```

Or if you want a simpler way to create a singleton, then add the `singleton` parameter in the `@RouterService` annotation and set it to `true`. WMRouter will automatically manage the created instance to ensure that the instance will not be constructed repeatedly and is thread-safe.

```java
@RouterService(interfaces = IUser.class, key = "OKHTTP", singleton = true)
public class OkhttpUserImpl implements IUser {
    @Override
    public void login(String username, String password) {
        // ...
    }
}
```

### 3.3 Call method

WMRouter provides a series of interfaces, which interface name starts with `Func` and ends with numbers, which indicates that the method in the interface needs to pass in several parameters, and the generic types are used to specify the type of these parameters and returned value.

For example:

```java
@RouterService(interfaces = Func2.class, key = "/add", singleton = true)
public class AddMethod implements Func2<Integer, Integer, Integer> {
    @Override
    public Integer call(Integer a, Integer b) {
        return a + b;
    }
}
```

As illustrated above, the `Func2` interface requires three generic types. The first two generic types represent the parameter types passed in by the `call` method, and the last generic type represents the type of the returned value of the `call` method.

After implementing the `Func2` interface, you can call methods like this:

```java
Func2<Integer, Integer, Integer> addMethod = Router.getService(Func2.class,
                                                               "/add");
Integer result = addMethod.call(1, 2);

// or

Integer result = Router.callMethod("/add", 1, 2);	// better
```

