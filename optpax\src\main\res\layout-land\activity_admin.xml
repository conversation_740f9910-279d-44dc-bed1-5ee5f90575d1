<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/04/08                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/main_background"
    tools:context="com.visionpay.opt.pax.activity.AdminActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:orientation="vertical">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/main_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/app_name"
            app:titleTextColor="@color/main_toolbar_content"
            app:titleCentered="true"
            app:contentInsetStart="@dimen/toolbar_content_to_screen_edge"
            app:layout_constraintTop_toTopOf="parent"/>

        <com.pax.commonui.keyboard.CustomKeyboardEditText
            android:id="@+id/main_amount_edit_text"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="4dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="8dp"
            android:focusable="true"
            android:gravity="center"
            android:inputType="text"
            android:textColor="@color/main_text"
            android:textSize="56sp"
            android:textStyle="bold"
            android:fontFamily="monospace"
            app:xml="@xml/amount_keyboard_land"
            app:autoSize="true"
            app:layout_constraintTop_toBottomOf="@id/main_toolbar"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/main_trans_type_group"
            tools:text="$123.45" />

        <com.google.android.material.button.MaterialButtonToggleGroup
            android:id="@+id/main_trans_type_group"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/page_content_to_screen_edge"
            android:layout_marginEnd="@dimen/page_content_to_screen_edge"
            android:layout_marginBottom="4dp"
            android:orientation="vertical"
            app:singleSelection="true"
            app:selectionRequired="true"
            app:checkedButton="@id/main_trans_type_sale"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/main_toolbar"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main_trans_type_sale"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:paddingHorizontal="@dimen/toggle_button_padding_horizontal"
                android:paddingVertical="@dimen/toggle_button_padding_vertical"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:text="@string/trans_sale"
                android:textSize="@dimen/text_normal_size"
                app:strokeWidth="@dimen/toggle_button_stroke_width"
                app:cornerRadius="@dimen/toggle_button_corner_radius" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main_trans_type_pre_auth"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:paddingHorizontal="@dimen/toggle_button_padding_horizontal"
                android:paddingVertical="@dimen/toggle_button_padding_vertical"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:text="@string/trans_preAuth"
                android:textSize="@dimen/text_normal_size"
                app:strokeWidth="@dimen/toggle_button_stroke_width"
                app:cornerRadius="@dimen/toggle_button_corner_radius" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/main_trans_type_refund"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:paddingHorizontal="@dimen/toggle_button_padding_horizontal"
                android:paddingVertical="@dimen/toggle_button_padding_vertical"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:text="@string/trans_refund"
                android:textSize="@dimen/text_normal_size"
                app:strokeWidth="@dimen/toggle_button_stroke_width"
                app:cornerRadius="@dimen/toggle_button_corner_radius" />

        </com.google.android.material.button.MaterialButtonToggleGroup>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/main_space"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"/>

</LinearLayout>