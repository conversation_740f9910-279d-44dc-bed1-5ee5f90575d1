/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/05/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.fragment;

import android.content.Intent;
import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.fragment.app.DialogFragment;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.PreferenceScreen;
import com.pax.commonlib.result.StartActivityLauncher;
import com.pax.commonlib.utils.LogUtils;
import com.pax.preflib.builder.pref.IDialogPreference;
import com.pax.preflib.utils.PrefBuilderManager;

/**
 * PreferenceFragmentCompat Base Class
 */
public abstract class ConfigBaseFragment extends PreferenceFragmentCompat {
    protected final String TAG = this.getClass().getSimpleName();
    private final StartActivityLauncher launcher = new StartActivityLauncher(this);

    @NonNull
    public abstract String getFragmentTitle();

    @CallSuper
    @Override
    public void onDisplayPreferenceDialog(@NonNull Preference preference) {
        if (preference instanceof IDialogPreference) {
            // Show custom dialog
            DialogFragment fragment = ((IDialogPreference<?>) preference).createDialog(preference.getKey());
            if (fragment != null) {
                fragment.setTargetFragment(this, 0);
                fragment.show(getParentFragmentManager(), "androidx.preference.PreferenceFragment.DIALOG");
            }
        } else {
            super.onDisplayPreferenceDialog(preference);
        }
    }

    @CallSuper
    @Override
    public void setPreferenceScreen(PreferenceScreen preferenceScreen) {
        PrefBuilderManager.executeBeforeScreen(preferenceScreen, this);

        super.setPreferenceScreen(preferenceScreen);

        PrefBuilderManager.executeDependency((prefKey, dependencyKey) -> {
            try {
                Preference preference = findPreference(prefKey);
                if (preference != null) {
                    preference.setDependency(dependencyKey);
                }
            } catch (Exception e) {
                LogUtils.e(e);
            }
        });

        PrefBuilderManager.executeAfterScreen(preferenceScreen, this);
    }

    protected void startActivityWaitResult(@NonNull Intent intent, @NonNull ActivityResultCallback<ActivityResult> callback) {
        launcher.launch(intent, (resultCode, data) -> callback.onActivityResult(new ActivityResult(resultCode, data)));
    }
}
