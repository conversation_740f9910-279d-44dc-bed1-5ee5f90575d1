package com.visionpay.opt.pax.emv.online;

import androidx.annotation.NonNull;

import com.pax.bizentity.entity.Issuer;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvservice.export.IEmvBase;

public class OnlineTaskFactory {

    public static IOnlineTask<IEmvBase> GetTask(@NonNull IEmvBase emv, ISendingOnlineCallback callback){
        Issuer matchedIssuer = emv.getMatchedIssuerByPan();
        if(matchedIssuer != null && matchedIssuer.isBankCard())
            return new EmvOnlineTask().setSendindOnlineCallback(callback);
        else
            return new OPTOnlineTask().setSendindOnlineCallback(callback);
    }

}
