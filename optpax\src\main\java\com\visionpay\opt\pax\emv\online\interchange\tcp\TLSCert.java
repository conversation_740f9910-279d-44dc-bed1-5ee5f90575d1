package com.visionpay.opt.pax.emv.online.interchange.tcp;

import com.pax.commonlib.apppara.AppParaLoader;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Base64;

public class TLSCert {

    public static boolean tls_initialized;
    private static X509Certificate cacert;

    public static X509Certificate getCacert(){
        if(cacert == null) {
            try {
                initialize();
            } catch (CertificateException | IOException e) {
                return null;
            }
        }
        return cacert;
    }

    public static void initialize() throws CertificateException, IOException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        String cacertFile = configParamService.getString(ConfigKeyConstant.CACERT, "");
        String trustedRootCertificates = AppParaLoader.getString(cacertFile);
        byte[] decoded;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            Base64.Decoder decoder = Base64.getDecoder();
            decoded = decoder.decode(
                    trustedRootCertificates
            );
        }
        else {
            decoded = android.util.Base64.decode(trustedRootCertificates, android.util.Base64.DEFAULT);
        }
        cacert = (X509Certificate)cf.generateCertificate(new ByteArrayInputStream(
                decoded
        ));

        tls_initialized = true;
    }
}

