package com.visionpay.opt.pax.viewmodel;

import androidx.annotation.Nullable;

import androidx.lifecycle.ViewModel;

import com.pax.bizentity.entity.SearchMode;
import com.pax.bizlib.card.CardReaderHelper;
import com.pax.commonlib.utils.LazyInit;
import com.pax.commonlib.utils.LogUtils;

public class MagReadCardViewModel extends ViewModel {
    private static final String TAG = "MagReadCardViewModel";
    private static final byte defaultSearchCardType = SearchMode.SWIPE;

    private final LazyInit<CardReaderHelper> cardReaderHelper =
            LazyInit.by(() -> new CardReaderHelper(new CardReaderCallback()));

    private MessageCallback messageCallback;
    private CardDetectCallback cardDetectCallback;

    private byte getSearchCardType() {
        return defaultSearchCardType;
    }

    public void startSearchCard() {
        LogUtils.d(TAG, "Start Search Card");
        cardReaderHelper.get().stopPolling();
        cardReaderHelper.get().polling(getSearchCardType());
    }

    public void stopSearchCard() {
        LogUtils.d(TAG, "Stop Search Card");
        cardReaderHelper.get().stopPolling();
    }

    public void setMessageCallback(@Nullable MessageCallback messageCallback) {
        this.messageCallback = messageCallback;
    }

    public void setCardDetectCallback(@Nullable CardDetectCallback cardDetectCallback) {
        this.cardDetectCallback = cardDetectCallback;
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        stopSearchCard();
        messageCallback = null;
        cardDetectCallback = null;
    }

    private class CardReaderCallback implements CardReaderHelper.Callback {

        @Override
        public void onMagDetected(String trackData1, String trackData2, String trackData3) {
            messageCallback.onSend("Card detected: Magnetic\nReading card...");
            cardDetectCallback.onDetect(trackData1, trackData2, trackData3);
        }

        @Override
        public void onError(String message) {
            if (messageCallback != null) {
                messageCallback.onSend(message);
            }
        }
    }

    public interface MessageCallback {
        void onSend(String message);
    }

    public interface CardDetectCallback {
        void onDetect(String trackData1, String trackData2, String trackData3);
    }
}
