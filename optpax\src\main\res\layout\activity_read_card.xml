<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/fullscreenBackgroundColor"
    android:theme="@style/ThemeOverlay.OPTPAX.FullscreenContainer"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/main"
        android:paddingTop="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="40px"
            android:text="YOUR TOTAL"
            android:textColor="@android:color/white"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center"
            android:baselineAligned="false">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="40dp"
                android:text="$"
                android:textSize="20dp"
                android:textStyle="bold"
                android:layout_marginTop="13dp"
                android:layout_marginRight="5dp"
                android:textColor="@android:color/white"/>
            <TextView
                android:id="@+id/text_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="50dp"
                android:text="10.00"
                android:textStyle="bold"
                android:textColor="@android:color/white"/>
        </LinearLayout>

        <TextView
            android:id="@+id/text_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="30px"
            android:layout_marginBottom="10dp"
            android:text="Waiting Card..."
            android:textColor="@android:color/white"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="20dp"
        android:orientation="vertical"
        android:background="#201E5392">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_card"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:gravity="center"
            android:layout_marginBottom="10dp"
            android:id="@+id/cardTap">
            <ImageView
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.1"
                android:src="@drawable/ccless"
                android:backgroundTint="@color/main"
                app:tint="@color/main" />
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.2"
                android:textAlignment="textStart"
                android:textSize="50px"
                android:text="TAP"
                android:textStyle="bold"
                android:layout_marginLeft="30dp"
                android:textColor="@color/main"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_card"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:gravity="center"
            android:layout_marginBottom="10dp"
            android:id="@+id/cardSwipe">
            <ImageView
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.1"
                android:src="@drawable/mag"
                android:backgroundTint="@color/main"
                app:tint="@color/main"/>
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.2"
                android:textAlignment="textStart"
                android:textSize="50px"
                android:text="SWIPE"
                android:textStyle="bold"
                android:layout_marginLeft="30dp"
                android:textColor="@color/main"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_card"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:gravity="center"
            android:layout_marginBottom="10dp"
            android:id="@+id/cardInsert">
            <ImageView
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.1"
                android:src="@drawable/cc"
                android:backgroundTint="@color/main"
                app:tint="@color/main"/>
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.2"
                android:textAlignment="textStart"
                android:textSize="50px"
                android:text="INSERT"
                android:textStyle="bold"
                android:layout_marginLeft="30dp"
                android:textColor="@color/main"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_card"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:gravity="center"
            android:layout_marginBottom="10dp"
            android:id="@+id/cardInfo"
            android:visibility="gone">
            <ImageView
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.1"
                android:id="@+id/imgCard"/>
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.2"
                android:textAlignment="textStart"
                android:textSize="50px"
                android:text=""
                android:textStyle="bold"
                android:layout_marginLeft="30dp"
                android:textColor="@color/main"
                android:id="@+id/txtCard"/>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>