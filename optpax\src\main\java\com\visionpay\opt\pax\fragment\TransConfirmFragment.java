/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.list.AidInfoAdapter;
import com.visionpay.opt.pax.viewmodel.TransConfirmViewModel;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.common.DefaultUriRequest;

/**
 * Transaction Card PAN & AID Confirm Fragment.
 */
public class TransConfirmFragment extends BaseTransFragment {
    private static final String TAG = "TransConfirmFragment";

    private TransConfirmViewModel viewModel;

    private ImageView issuerIcon;
    private TextView panText;
    private TextView infoText;
    private LinearLayout cardholderLayout;
    private TextView cardholderName;
    private View aidInfoDivider;
    private RecyclerView aidInfoList;
    private View editAidDivider;
    private MaterialButton editAidButton;

    @NonNull
    @Override
    public String getFragmentTag() {
        return TransFragmentManager.CONFIRM_AID;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(this).get(TransConfirmViewModel.class);
        viewModelPrepared(viewModel);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_confirm_card, container, false);
        issuerIcon = view.findViewById(R.id.trans_confirm_issuer_icon);
        panText = view.findViewById(R.id.trans_confirm_pan);
        infoText = view.findViewById(R.id.trans_confirm_card_info);
        cardholderLayout = view.findViewById(R.id.trans_confirm_cardholder);
        cardholderName = view.findViewById(R.id.trans_confirm_cardholder_name);
        aidInfoDivider = view.findViewById(R.id.trans_confirm_aid_divider);
        aidInfoList = view.findViewById(R.id.trans_confirm_aid_list);
        editAidDivider = view.findViewById(R.id.trans_confirm_edit_aid_divider);
        editAidButton = view.findViewById(R.id.trans_confirm_edit_aid_button);
        return view;
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        if (viewModel != null) {
            // Icon
            viewModel.listenIssuerIcon().observe(getViewLifecycleOwner(), id -> issuerIcon.setImageResource(id));

            // Pan
            viewModel.listenPan().observe(getViewLifecycleOwner(), pan -> panText.setText(pan));

            // Info
            viewModel.listenInfo().observe(getViewLifecycleOwner(), info -> infoText.setText(info));

            // Cardholder Name
            viewModel.listenCardholderName().observe(getViewLifecycleOwner(), name -> {
                if (name == null || name.isEmpty() || name.trim().isEmpty()) {
                    cardholderLayout.setVisibility(View.GONE);
                } else {
                    cardholderLayout.setVisibility(View.VISIBLE);
                    cardholderName.setText(name.trim());
                }
            });

            // Aid
            AidInfoAdapter adapter = new AidInfoAdapter();
            aidInfoList.setAdapter(adapter);
            aidInfoList.setLayoutManager(new LinearLayoutManager(getContext()));
            viewModel.listenAidInfo().observe(getViewLifecycleOwner(), list -> {
                if (list == null || list.isEmpty()) {
                    aidInfoDivider.setVisibility(View.GONE);
                    aidInfoList.setVisibility(View.GONE);
                } else {
                    aidInfoDivider.setVisibility(View.VISIBLE);
                    aidInfoList.setVisibility(View.VISIBLE);
                    adapter.setInfoList(list).notifyDataSetChanged();
                }
            });

            // Edit Aid
            viewModel.getStatusLiveData().observe(getViewLifecycleOwner(), status -> {
                if (status == null
                        || status.getEditAidPath() == null
                        || status.getEditAidPath().isEmpty()
                        || status.getEditAidParam() == null
                        || status.getEditAidParam().isEmpty()) {
                    editAidDivider.setVisibility(View.GONE);
                    editAidButton.setVisibility(View.GONE);
                } else {
                    editAidDivider.setVisibility(View.VISIBLE);
                    editAidButton.setVisibility(View.VISIBLE);
                    editAidButton.setOnClickListener(v -> Router.startUri(
                            new DefaultUriRequest(v.getContext(), status.getEditAidPath())
                                    .putExtra(BundleFieldConst.EMV_PARAM, status.getEditAidParam())));
                }
            });
        }
    }
}
