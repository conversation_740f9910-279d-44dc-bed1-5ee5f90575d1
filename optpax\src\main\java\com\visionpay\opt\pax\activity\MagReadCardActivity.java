package com.visionpay.opt.pax.activity;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.pax.commonlib.application.ActivityStack;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.sankuai.waimai.router.common.DefaultUriRequest;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.entity.IM30Transaction;
import com.visionpay.opt.pax.entity.MagneticCardMessage;
import com.visionpay.opt.pax.entity.POSTransaction;
import com.visionpay.opt.pax.entity.ProcessType;
import com.visionpay.opt.pax.entity.ReadState;
import com.visionpay.opt.pax.message.BroadcastMesseger;
import com.visionpay.opt.pax.message.BroadcastMessegerListener;
import com.visionpay.opt.pax.thread.OnlineThread;
import com.visionpay.opt.pax.utils.ProcessTypeDeserializer;
import com.visionpay.opt.pax.viewmodel.MagReadCardViewModel;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RouterUri(path = EmvRouterConst.MAG_READ_CARD)
public class MagReadCardActivity extends BaseActivity implements BroadcastMessegerListener {

    private TextView mTextData;
    private ImageView imgMag;

    private View viewCards;

    private POSTransaction POSTransaction = null;
    private IM30Transaction im30t = null;
    MagneticCardMessage magneticCardMessage = null;

    private MagReadCardViewModel viewModel;
    private boolean isRestore = false;

    private int FINISH_DELAY = 60000;
    private final Handler mFinishHandler = new Handler(Looper.myLooper());
    private final Runnable mAutoFinishRunnable = () -> finishActivity(ReadState.Timeout);

    ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        isRestore = (savedInstanceState != null);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_mag_read_card);

        viewModel = new ViewModelProvider(this).get(MagReadCardViewModel.class);

        mTextData = findViewById(R.id.text_data);
        imgMag = findViewById(R.id.mag);
        viewCards = findViewById(R.id.viewCards);

        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        String client = configParamService.getString(ConfigKeyConstant.EDC_CLIENT, "");
        if(client.toLowerCase(Locale.ROOT).equals("pitstop")) {
            imgMag.setVisibility(View.VISIBLE);
            viewCards.setVisibility(View.GONE);
        }

        if (!isRestore) {
            Intent intent = getIntent();
            if (intent != null) {
                POSTransaction = (POSTransaction) getIntent().getSerializableExtra(BundleFieldConst.POSTRANSACTION);

                im30t = new IM30Transaction(POSTransaction);
                magneticCardMessage = new MagneticCardMessage(POSTransaction, im30t);

                BroadcastMesseger.getInstance().send(im30t);
                BroadcastMesseger.getInstance().send(magneticCardMessage);

                setListeners();
            }

            BroadcastMesseger.getInstance().start(this);
        }

        FINISH_DELAY = configParamService.getInt(ConfigKeyConstant.AUTO_CANCEL_MAG_DELAY, 60000);

        mFinishHandler.postDelayed(mAutoFinishRunnable, FINISH_DELAY);
    }

    @Override
    public void onBackPressed() {
        mFinishHandler.removeCallbacks(mAutoFinishRunnable);
        super.onBackPressed();
    }

    public void finishActivity(ReadState magneticState){
        magneticCardMessage.setMagneticState(magneticState);
        BroadcastMesseger.getInstance().send(magneticCardMessage);
        BroadcastMesseger.getInstance().stop();
        if(!BroadcastMesseger.getInstance().isInternalMessage()) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK); // equal to Intent.FLAG_ACTIVITY_CLEAR_TASK which is only available from API level 11
            this.startActivity(intent);
        } else {
            ActivityStack.getInstance().popAll();
        }
    }

    @Override
    protected void onDestroy() {
        mFinishHandler.removeCallbacks(mAutoFinishRunnable);
        super.onDestroy();
    }

    protected void setListeners() {
        viewModel.setMessageCallback(message -> runOnUiThread(() -> mTextData.setText(message)));

        viewModel.setCardDetectCallback((track1, track2, track3) -> {
            runOnUiThread(() -> {
                magneticCardMessage.setMagneticState(ReadState.CardReadSuccess);
                magneticCardMessage.setTrackData1(track1);
                magneticCardMessage.setTrackData2(track2);
                magneticCardMessage.setTrackData3(track3);
                BroadcastMesseger.getInstance().send(magneticCardMessage);

                mFinishHandler.removeCallbacks(mAutoFinishRunnable);
                BroadcastMesseger.getInstance().stop();
                //imgMag.setVisibility(View.GONE);
                if (!BroadcastMesseger.getInstance().isInternalMessage()) {
                    Router.startUri(new DefaultUriRequest(this, EmvRouterConst.SUCCESS_CARD)
                            .putExtra(BundleFieldConst.TITLESUCESS, "Success"));
                } else {
                    Router.startUri(new DefaultUriRequest(this, EmvRouterConst.SUCCESS_INTERNAL_CARD)
                            .putExtra(BundleFieldConst.TITLESUCESS, "Success"));
                }
            });
        });

        if (!isRestore) {
            viewModel.startSearchCard();
        }

        isRestore = false;  // Reset flag
    }

    @Override
    public void OnMessageReceive(String message) {

        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                .registerTypeAdapter(ProcessType.class, new ProcessTypeDeserializer()).create();

        Type listType = new TypeToken<List<POSTransaction>>(){}.getType();
        try {
            message = message.replaceAll("\\p{Cntrl}", "").replace("}{", "},{");
            message =  "[" + message + "]";

            List<POSTransaction> ptList = gson.fromJson(message, listType);
            for (POSTransaction pt : ptList) {
                if (pt.checkValues()) {
                    if (pt.getProcessType() == ProcessType.Capture || pt.getProcessType() == ProcessType.Reversal) {
                        executorService.execute(new OnlineThread(pt));
                    }
                    if (pt.getExternalDeviceToken().equals(POSTransaction.getExternalDeviceToken())
                            && pt.getExternalReference().equals(POSTransaction.getExternalReference())
                            && pt.getProcessType() == ProcessType.Cancel) {
                        finishActivity(ReadState.Canceled);
                    }
                } else {
                    IM30Transaction im30t = new IM30Transaction(pt, "Incorrect values.");
                    BroadcastMesseger.getInstance().send(gson.toJson(im30t));
                }
            }
        } catch (Exception ex) {
            IM30Transaction im30t = new IM30Transaction(POSTransaction, ex.getMessage());
            BroadcastMesseger.getInstance().send(gson.toJson(im30t));
        }
    }
}