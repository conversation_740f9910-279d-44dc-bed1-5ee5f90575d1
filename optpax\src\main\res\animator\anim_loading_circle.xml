<?xml version="1.0" encoding="utf-8"?><!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/04/13                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<set xmlns:android="http://schemas.android.com/apk/res/android">
    <objectAnimator
        android:duration="1000"
        android:interpolator="@android:anim/linear_interpolator"
        android:propertyName="rotation"
        android:repeatMode="restart"
        android:repeatCount="-1"
        android:valueType="floatType"
        android:valueFrom="0"
        android:valueTo="359" />
</set>