/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.consts;

/**
 * Bundle Field Constants
 */
public class BundleFieldConst {
    public static final String AMOUNT = "amount";
    public static final String TRANS_TYPE = "transType";
    public static final String POSTRANSACTION = "postransaction";
    public static final String TITLESUCESS = "titlesucess";

    public static final String ATTEMPTS_READ_CARD = "attempts_read_card";

    public static final String EMV_PARAM = "EMV_PARAM";
}
