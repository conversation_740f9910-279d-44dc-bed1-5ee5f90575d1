/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/02/22                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.app;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.RemoteException;
import android.util.Log;
import android.widget.Toast;

import com.pax.bizentity.db.helper.DaoManager;
import com.pax.commonlib.application.AppActivityLifecycleCallbacks;
import com.pax.commonlib.application.AppInfo;
import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.apppara.AppParaLoader;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ThreadPoolManager;
import com.pax.commonui.keyboard.KeyboardViewFactory;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.dal.IDAL;
import com.pax.market.android.app.sdk.BaseApiService;
import com.pax.market.android.app.sdk.StoreSdk;
import com.pax.poslib.gl.IGL;
import com.pax.poslib.gl.IPacker;
import com.pax.poslib.gl.convert.ConvertHelper;
import com.pax.poslib.gl.convert.IConvert;
import com.pax.poslib.gl.impl.GL;
import com.pax.poslib.model.ModelInfo;
import com.pax.poslib.neptune.Sdk;
import com.sankuai.waimai.router.service.ServiceLoader;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.receiver.StartupOnBootUpReceiver;

import java.util.Locale;

/**
 * OPT PAX Application.
 */
public class App extends BaseApplication {
    private static final String TAG = "App";

    private static App mApp;

    // Neptune interface
    private static IDAL dal;
    private static IGL gl;
    private static IConvert convert;
    private static IPacker packer;

    // app version
    private static String version;

    public PendingIntent pendingIntent;

    private boolean isReadyToUpdate = true;

    @Override
    public void onCreate() {
        super.onCreate();

        mApp = this;

        DaoManager.getInstance().init();
        ServiceLoader.lazyInit();

        ThreadPoolManager.getInstance().execute(() -> {
            ConvertHelper.init(true);
            registerActivityLifecycleCallbacks(new AppActivityLifecycleCallbacks());
            init();
            version = updateVersion();
        });

        customUIInit();
        initStoreSdk();
    }

    private void init() {
        dal = Sdk.getInstance().getDal(this);
        GL.init(this);
        gl = GL.getGL();
        convert = getGl().getConvert();
        packer = getGl().getPacker();

        ModelInfo.getInstance().buildCache();

        defaultUEH = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(_unCaughtExceptionHandler);
    }

    private void customUIInit() {
        // Custom Amount Keyboard
        KeyboardViewFactory.getInstance().setLayoutId(R.layout.custom_keyboard_view)
                .setOpKeyCallback(codes -> codes == -4);

        // Custom Preference Style
        //PrefFactory.getInstance().setFactory(new EmvPrefFactory());
    }

    private void initStoreSdk() {
        //todo Init AppKey，AppSecret, make sure the appKey and appSecret is corret.
        String key = AppInfo.getInstance().getAppKey();
        String secret = AppInfo.getInstance().getAppSecret();
        StoreSdk.getInstance().init(getApplicationContext(), key, secret,
                new BaseApiService.Callback() {
                    @Override
                    public void initSuccess() {
                        Log.i(TAG, "initSuccess.");
                        initInquirer();
                        //Intent intent = new Intent(getAppContext(), DownloadParamService.class);
                        //startService(intent);
                    }

                    @Override
                    public void initFailed(RemoteException e) {
                        Log.i(TAG, "initFailed: "+e.getMessage());
                        Toast.makeText(getApplicationContext(), "Cannot get API URL from STORE client," +
                                " Please install STORE client first.", Toast.LENGTH_LONG).show();
                    }
                });
    }


    private void initInquirer() {

        //todo 2. Init checking of whether app can be updated
        StoreSdk.getInstance().initInquirer(new StoreSdk.Inquirer() {
            @Override
            public boolean isReadyUpdate() {
                Log.i(TAG, "call business function....isReadyUpdate = " + isReadyToUpdate);
                //todo call your business function here while is ready to update or not
                return isReadyToUpdate;
            }
        });
    }

    /**
     * Gets version.
     *
     * @return the version
     */
    public static String getVersion() {
        return version;
    }

    /**
     * get app version
     */
    private String updateVersion() {
        try {
            PackageManager manager = getPackageManager();
            PackageInfo info = manager.getPackageInfo(getPackageName(), 0);
            return info.versionName;
        } catch (Exception e) {
            LogUtils.w(TAG, e);
            return null;
        }
    }

    private Thread.UncaughtExceptionHandler defaultUEH;

    // handler listener
    private Thread.UncaughtExceptionHandler _unCaughtExceptionHandler = new Thread.UncaughtExceptionHandler() {
        @Override
        public void uncaughtException(Thread thread, Throwable ex) {
            ex.printStackTrace();

            try {
                String configfile = AppParaLoader.getString("config.p");
                int index = configfile.indexOf(ConfigKeyConstant.EDC_TYPE);
                String type = "M";
                if (index > -1)
                    type = configfile.substring(index + 9, index + 10);
                Log.d(StartupOnBootUpReceiver.class.getSimpleName(), "Type: " + type);
                if (type.toUpperCase(Locale.ROOT).equals("M")) { //Main
                    AlarmManager mgr = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
                    mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 500, pendingIntent);
                    System.exit(2);
                }
            }catch(Exception e) {}
        }
    };

    public static App getApp() {
        return mApp;
    }

    /**
     * Gets dal.
     *
     * @return the dal
     */
    public static IDAL getDal() {
        return dal;
    }

    /**
     * Gets gl.
     *
     * @return the gl
     */
    public static IGL getGl() {
        return gl;
    }

    /**
     * Gets convert.
     *
     * @return the convert
     */
    public static IConvert getConvert() {
        return convert;
    }

    /**
     * Gets packer.
     *
     * @return the packer
     */
    public static IPacker getPacker() {
        return packer;
    }
}
