/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/28                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.entity;

import android.view.View;
import androidx.annotation.DrawableRes;

/**
 * Error Event Entity.
 */
public class ErrorEvent {
    private String title;
    private String info;
    private Integer buttonIconId;
    private String buttonContent;
    private View.OnClickListener buttonClickListener;

    public String getTitle() {
        return title;
    }

    public ErrorEvent setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getInfo() {
        return info;
    }

    public ErrorEvent setInfo(String info) {
        this.info = info;
        return this;
    }

    public Integer getButtonIconId() {
        return buttonIconId;
    }

    public ErrorEvent setButtonIconId(@DrawableRes int buttonIconId) {
        this.buttonIconId = buttonIconId;
        return this;
    }

    public String getButtonContent() {
        return buttonContent;
    }

    public ErrorEvent setButtonContent(String buttonContent) {
        this.buttonContent = buttonContent;
        return this;
    }

    public View.OnClickListener getButtonClickListener() {
        return buttonClickListener;
    }

    public ErrorEvent setButtonClickListener(View.OnClickListener buttonClickListener) {
        this.buttonClickListener = buttonClickListener;
        return this;
    }
}
