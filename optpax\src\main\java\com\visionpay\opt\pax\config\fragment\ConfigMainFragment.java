/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/02/23                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;

import com.visionpay.opt.pax.config.datastore.PrefDataStore;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.preflib.builder.RouterPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

/**
 * Settings main page
 */
@RouterService(interfaces = EmvConfigBaseFragment.class, key = EmvRouterConst.CONFIG_MAIN)
public class ConfigMainFragment extends EmvConfigBaseFragment {
    @NonNull
    @Override
    public String getFragmentTitle() {
        return "Settings";
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new PrefDataStore());
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // Common
            screen.addPreference(RouterPrefBuilder
                    .newInstance(context, EmvRouterConst.CONFIG_COMMON, "Common")
                    .setPath(EmvRouterConst.CONFIG_COMMON)
                    .build());

            // Param
            screen.addPreference(RouterPrefBuilder
                    .newInstance(context, EmvRouterConst.CONFIG_PARAM, "Param")
                    .setPath(EmvRouterConst.CONFIG_PARAM)
                    .build());

            // About SmartFuel PAX
            screen.addPreference(RouterPrefBuilder
                    .newInstance(context, EmvRouterConst.CONFIG_ABOUT, "About")
                    .setPath(EmvRouterConst.CONFIG_ABOUT)
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
