<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/fullscreenBackgroundColor"
    android:theme="@style/ThemeOverlay.OPTPAX.FullscreenContainer"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/main"
        android:padding="30dp">

        <TextView
            android:id="@+id/text_data"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="30px"
            android:text="Waiting Card..."
            android:textColor="@android:color/white"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="20dp"
        android:orientation="vertical"
        android:background="#201E5392"
        android:id="@+id/viewCards">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_card"
            android:orientation="horizontal"
            android:paddingLeft="30dp"
            android:gravity="center"
            android:layout_marginBottom="10dp">
            <ImageView
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="0.1"
                android:src="@drawable/mag"
                android:backgroundTint="@color/main"
                app:tint="@color/main"/>
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.2"
                android:textAlignment="textStart"
                android:textSize="50px"
                android:text="SWIPE"
                android:textStyle="bold"
                android:layout_marginLeft="30dp"
                android:textColor="@color/main"/>
        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/im_pos_magswipe"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:id="@+id/mag"
        android:visibility="gone"/>

</LinearLayout>