/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.magnetic;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvbase.utils.EmvResultUtils;
import com.pax.emvservice.export.IEmvMagService;
import com.pax.emvservice.export.magnetic.IMagResultListener;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvTag;
import com.visionpay.opt.pax.utils.EmvTagUtils;

import java.util.List;

public class MagResultListener implements IMagResultListener {
    private static final String TAG = "MagResultListener";

    @NonNull
    private final IEmvMagService emv;
    @NonNull
    private final List<CvmType> cvmTypeList;
    @NonNull
    private final List<EmvTag> emvTagList;
    @Nullable
    private final IEmvService.ErrorCallback errorCallback;
    @Nullable
    private final IEmvService.CompleteCallback completeCallback;

    public MagResultListener(@NonNull IEmvMagService emv,
                             @NonNull List<CvmType> cvmTypeList,
                             @NonNull List<EmvTag> emvTagList,
                             @Nullable IEmvService.ErrorCallback errorCallback,
                             @Nullable IEmvService.CompleteCallback completeCallback) {
        this.emv = emv;
        this.cvmTypeList = cvmTypeList;
        this.emvTagList = emvTagList;
        this.errorCallback = errorCallback;
        this.completeCallback = completeCallback;
    }

    private void saveEmvTag() {
        EmvTagUtils.getCommonTags(emv);
    }

    /**
     * Fallback, 一般是因为没有匹配到 AID 或者在 Select APP 阶段发生了一些错误导致的。
     *
     * 由于 SmartFuel PAX 不支持刷卡，因此这里只是通过 ErrorCallback 去在界面上显示错误信息。在实际的应用中，应该降级
     * 到刷卡交易。
     */
    @Override
    public void fallback() {
        LogUtils.d(TAG, "fallback");
        if (errorCallback != null) {
            errorCallback.onError("Contact Fallback",
                    "Cannot match AID or icc command error, need fallback.");
        }
    }

    /**
     * 联机拒绝交易。一般是服务器返回了非 00 的 Response Code 导致的。
     *
     * SmartFuel PAX 中并不具备实际的联机功能，Online Denied 是用户自己选择的，Response Code 也是用户自己输入的，因
     * 此并不会显示错误信息，而是去显示了 CVM Type 和 EMV Tag。在实际的应用中，应该提示错误并终止交易。
     */
    @Override
    public void onlineDenied() {
        LogUtils.d(TAG, "onlineDenied");
        if (completeCallback != null) {
            saveEmvTag();
            //GreendaoHelper.getTransDataHelper().insert(transData);
            completeCallback.onComplete(true, TransResultEnum.RESULT_ONLINE_DENIED, cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * 离线拒绝交易。这很可能是在联机之前就发生了某些错误，导致 EMV 流程被异常终止。因此对于这种情况，SmartFuel PAX 会
     * 显示错误信息。在实际的应用中也应该提示错误信息。
     *
     * @param resultCode 错误原因代码
     */
    @Override
    public void offlineDenied(int resultCode) {
        LogUtils.d(TAG, "offlineDenied");
        emv.setTlv(0x8A, "Z1".getBytes());
        if (errorCallback != null) {
            errorCallback.onError("Offline Denied",
                    "Result Code: " + resultCode + ", " + EmvResultUtils.getMessage(resultCode));
        }
    }

    /**
     * 联机批准，但是卡片拒绝交易。这种情况一般是第二次 GAC 失败导致的。这有可能联机返回的数据有问题。在 SmartFuel PAX 中
     * 并没有提示错误，而是显示了 CVM Type 和 EMV Tag。在实际的应用中，应该要提示错误信息并终止交易。
     *
     * @param resultCode 错误原因代码
     */
    @Override
    public void onlineCardDenied(int resultCode) {
        LogUtils.d(TAG, "onlineCardDenied");
        saveEmvTag();
        if (completeCallback != null) {
            completeCallback.onComplete(true, TransResultEnum.RESULT_ONLINE_CARD_DENIED,
                    cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * 离线批准交易。
     *
     * 注意，这种情况有两种可能：
     * 1. 这笔交易在第一次 GAC 时的结果为 TC，即不需要联机，直接离线批准交易；
     * 2. 这笔交易在第一次 GAC 时的结果为 ARQC，即要求联机，但是联机失败（比如说没有网络），然后第二次 GAC 的结果为
     * TC，这种也是离线批准交易。
     *
     * 第二种情况非常少见，但是也的确存在（只有 JCB 的部分 case 有这种要求），因此依然需要对这种情况进行适配。
     *
     * 理论上来说，对于第二种情况，也可以搞个单独的回调来进行处理，但是由于这两种情况都属于离线批准交易，大部分的处理
     * 逻辑应该都是一样的，所以我们没有提供一个额外的回调，而是共同使用了 offlineApproved 回调，只是通过
     * needSetARC 这个参数进行区分。
     *
     * 所谓 ARC，其实就是 8A 这个 Tag。对于上面讲的第一种情况来说，由于没有走到联机步骤，所以必须在这里设置 Tag 8A；
     * 而对于第二种情况来说，8A 是 EMV 内核自己设置的（Y3 或者 Z3），所以不需要再在这里进行设置了。因此，你可以通过
     * needSetARC 来区分走到 offlineApproved 这里时究竟是第一种情况还是第二种情况。
     *
     * 还是要再次强调一下，对于联机失败的情况，不要自己去手动设置 Tag 8A，而应该让 EMV 内核自己去处理。否者很有可能
     * 导致 Test case 不通过。
     *
     * @param needSignature 是否需要签名。这里仅表示 EMV 内核的 CVM Type 是否要求用户签名。
     * @param needSetARC 是否需要设置 Tag 8A 的值
     */
    @Override
    public void offlineApproved(boolean needSignature, boolean needSetARC) {
        LogUtils.d(TAG, "offlineApproved");
        if (needSignature) {
            cvmTypeList.add(new CvmType(R.drawable.ic_signature, ResourceUtil.getString(R.string.cvm_signature)));
        }
        if (needSetARC) {
            emv.setTlv(0x8A, "Y1".getBytes());
        }
        saveEmvTag();
        if (completeCallback != null) {
            if (needSetARC) {
                completeCallback.onComplete(true, TransResultEnum.RESULT_OFFLINE_APPROVED,
                        cvmTypeList,
                        emvTagList);
            } else {
                completeCallback.onComplete(true,
                        TransResultEnum.RESULT_ONLINE_FAILED_CARD_APPROVED, cvmTypeList,
                        emvTagList);
            }
        }
    }

    /**
     * 联机批准。一般是第一次 GAC 结果为 ARQC，然后联机成功，Response Code 为 00，最后第二次 GAC 结果为 TC。
     *
     * @param needSignature 是否需要签名。这里仅表示 EMV 内核的 CVM Type 是否要求用户签名。
     */
    @Override
    public void onlineApproved(boolean needSignature) {
        LogUtils.d(TAG, "onlineApproved");
        if (needSignature) {
            cvmTypeList.add(new CvmType(R.drawable.ic_signature, ResourceUtil.getString(R.string.cvm_signature)));
        }
        saveEmvTag();
        if (completeCallback != null) {
            completeCallback.onComplete(true, TransResultEnum.RESULT_ONLINE_APPROVED, cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * 联机失败，交易被拒绝。一般是第一次 GAC 结果为 ARQC，然后联机失败（比如说没有网络），最后第二次 GAC 结果为
     * AAC。
     *
     * 请务必注意，不是联机失败就一定会被拒绝交易的。对于部分场景来说，即便是联机失败，也依然有可能被卡片批准交易。此
     * 回调仅代表联机失败后被卡片拒绝交易的场景。
     *
     * 另外，同样需要注意的是，联机失败了以后，不要自己去设置 Tag 8A，而应该交由 EMV 内核处理。否则很有可能导致
     * Test case 不通过。
     */
    @Override
    public void onlineFailed() {
        LogUtils.d(TAG, "onlineFailed");
        saveEmvTag();
        if (completeCallback != null) {
            completeCallback.onComplete(true, TransResultEnum.RESULT_ONLINE_FAILED, cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * Simple Flow 结束。
     *
     * Simple Flow 是在流程走到 Add CAPK 了以后就终止，不执行完整的 EMV 交易流程。在实际的应用中可能有些交易类型
     * 需要这样处理，不过 SmartFuel PAX 不支持，所以直接显示了错误信息。
     *
     * 在预处理阶段可以设置当前 EMV 交易是否为 Simple Flow。
     */
    @Override
    public void simpleFlowEnd() {
        LogUtils.d(TAG, "simpleFlowEnd");
        if (errorCallback != null) {
            errorCallback.onError("Simple Flow End",
                    "SmartFuel PAX not support this result");
        }
    }
}
