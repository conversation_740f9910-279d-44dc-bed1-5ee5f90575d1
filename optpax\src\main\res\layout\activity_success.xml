<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?attr/fullscreenBackgroundColor"
    android:theme="@style/ThemeOverlay.OPTPAX.FullscreenContainer"
    android:padding="20dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/payment_successful"
        android:textSize="25dp"
        android:textStyle="bold"
        android:id="@+id/txtTitle"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@android:color/darker_gray"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="30dp"/>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/successiful"
        android:layout_gravity="center"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/your_payment_was_successful"
        android:textSize="18dp"
        android:gravity="center"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="30dp"
        android:id="@+id/txtText"/>

    <android.widget.Button
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="done"
        style="@style/PinPadEnter"
        android:id="@+id/btnDone"/>

</LinearLayout>