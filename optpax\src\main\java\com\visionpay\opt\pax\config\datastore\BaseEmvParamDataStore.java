/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.preference.PreferenceDataStore;

/**
 * Base EMV Param DataStore.
 */
public abstract class BaseEmvParamDataStore<T extends BaseEmvParamDataStore<T>> extends PreferenceDataStore {
    protected RefreshCachedCallback refreshCachedCallback;

    /**
     * Set refresh EMV param cache callback.
     *
     * Due to the existence of the EMV parameter cache, after editing the value in AID or Program
     * ID, the cache needs to be refreshed to make the new value effective.
     *
     * By setting this callback, you can know whether the user has edited the value in the AID or
     * Program ID. After the callback is executed, refresh the cache to make the new value take
     * effect.
     *
     * 由于EMV参数缓存的存在，所以在编辑了 AID 或者 Program ID 中的值以后，还需要刷新缓存才能使得新的值生效。
     *
     * 通过设置这个回调，就可以得知用户是否编辑了 AID 或者 Program ID 中的值，当该回调执行以后再去刷新缓存，即可
     * 使得新的值生效。
     *
     * @param callback Refresh param cache callback.
     * @return This data store.
     */
    public T setRefreshCachedParamCallback(RefreshCachedCallback callback) {
        refreshCachedCallback = callback;
        return (T) this;
    }

    public interface RefreshCachedCallback {
        void needRefresh();
    }
}
