/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/11                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.annotation.Nullable;
import com.pax.bizentity.db.helper.AmexDrlDbHelper;
import com.pax.bizentity.entity.clss.amex.AmexDrlBean;

/**
 * Contactless AMEX Program ID DataStore.
 */
public class AmexDrlDataStore extends BaseEmvParamDataStore<AmexDrlDataStore> {
    public static final String TRANS_LIMIT = "TRANS_LIMIT";
    public static final String FLOOR_LIMIT = "FLOOR_LIMIT";
    public static final String CVM_LIMIT = "CVM_LIMIT";

    @Nullable
    private final AmexDrlBean drl;

    public AmexDrlDataStore(@Nullable AmexDrlBean drl) {
        this.drl = drl;
    }

    @Override
    public void putLong(String key, long value) {
        if (drl == null) {
            return;
        }
        switch (key) {
            case TRANS_LIMIT:
                drl.setTransLimit(value);
                break;
            case FLOOR_LIMIT:
                drl.setFloorLimit(value);
                break;
            case CVM_LIMIT:
                drl.setCvmLimit(value);
                break;
            default: return;
        }
        AmexDrlDbHelper.getInstance().update(drl);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Override
    public long getLong(String key, long defValue) {
        if (drl == null) {
            return defValue;
        }
        switch (key) {
            case TRANS_LIMIT: return drl.getTransLimit();
            case FLOOR_LIMIT: return drl.getFloorLimit();
            case CVM_LIMIT: return drl.getCvmLimit();
            default: return defValue;
        }
    }
}
