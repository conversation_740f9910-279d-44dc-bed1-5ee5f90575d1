<?xml version="1.0" encoding="utf-8"?><!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/04/02                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:paddingVertical="@dimen/dialog_padding_vertical_large"
    android:paddingHorizontal="@dimen/dialog_padding_horizontal"
    android:background="@android:color/transparent">

    <ImageView
        android:id="@+id/dialog_prompt_icon"
        android:layout_width="@dimen/dialog_icon_size"
        android:layout_height="@dimen/dialog_icon_size"
        android:src="@drawable/transition_loading"
        android:contentDescription="@string/content_progress_icon" />

    <TextView
        android:id="@+id/dialog_prompt_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dialog_icon_title_spacing_vertical"
        android:textSize="@dimen/dialog_title_text"
        android:textColor="@color/main_text"
        android:textStyle="bold"
        android:gravity="center_horizontal"
        tools:text="Processing..." />

    <TextView
        android:id="@+id/dialog_prompt_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/title_content_spacing_vertical_small"
        android:textSize="@dimen/dialog_content_text"
        android:textColor="@color/main_text"
        android:gravity="center_horizontal"
        tools:text="EMV service running, please wait" />

</LinearLayout>