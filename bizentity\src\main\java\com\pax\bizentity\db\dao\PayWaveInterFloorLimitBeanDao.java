package com.pax.bizentity.db.dao;

import java.util.List;
import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;
import org.greenrobot.greendao.query.Query;
import org.greenrobot.greendao.query.QueryBuilder;

import com.pax.bizentity.entity.clss.paywave.PayWaveInterFloorLimitBean;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "paywave_floor_limit".
*/
public class PayWaveInterFloorLimitBeanDao extends AbstractDao<PayWaveInterFloorLimitBean, Long> {

    public static final String TABLENAME = "paywave_floor_limit";

    /**
     * Properties of entity PayWaveInterFloorLimitBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "paywave_floor_limit_id");
        public final static Property PaywaveAidId = new Property(1, Long.class, "paywaveAidId", false, "paywaveAidId");
        public final static Property TransType = new Property(2, byte.class, "transType", false, "TRANS_TYPE");
        public final static Property FloorLimit = new Property(3, long.class, "floorLimit", false, "FLOOR_LIMIT");
        public final static Property TransLimit = new Property(4, long.class, "transLimit", false, "TRANS_LIMIT");
        public final static Property CvmLimit = new Property(5, long.class, "cvmLimit", false, "CVM_LIMIT");
        public final static Property TransLimitFlag = new Property(6, byte.class, "transLimitFlag", false, "TRANS_LIMIT_FLAG");
        public final static Property CvmLimitFlag = new Property(7, byte.class, "cvmLimitFlag", false, "CVM_LIMIT_FLAG");
        public final static Property FloorLimitFlag = new Property(8, byte.class, "floorLimitFlag", false, "FLOOR_LIMIT_FLAG");
    }

    private Query<PayWaveInterFloorLimitBean> paywaveAidBean_InterWareFloorLimitQuery;

    public PayWaveInterFloorLimitBeanDao(DaoConfig config) {
        super(config);
    }
    
    public PayWaveInterFloorLimitBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"paywave_floor_limit\" (" + //
                "\"paywave_floor_limit_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"paywaveAidId\" INTEGER," + // 1: paywaveAidId
                "\"TRANS_TYPE\" INTEGER NOT NULL ," + // 2: transType
                "\"FLOOR_LIMIT\" INTEGER NOT NULL ," + // 3: floorLimit
                "\"TRANS_LIMIT\" INTEGER NOT NULL ," + // 4: transLimit
                "\"CVM_LIMIT\" INTEGER NOT NULL ," + // 5: cvmLimit
                "\"TRANS_LIMIT_FLAG\" INTEGER NOT NULL ," + // 6: transLimitFlag
                "\"CVM_LIMIT_FLAG\" INTEGER NOT NULL ," + // 7: cvmLimitFlag
                "\"FLOOR_LIMIT_FLAG\" INTEGER NOT NULL );"); // 8: floorLimitFlag
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"paywave_floor_limit\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, PayWaveInterFloorLimitBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        Long paywaveAidId = entity.getPaywaveAidId();
        if (paywaveAidId != null) {
            stmt.bindLong(2, paywaveAidId);
        }
        stmt.bindLong(3, entity.getTransType());
        stmt.bindLong(4, entity.getFloorLimit());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getCvmLimit());
        stmt.bindLong(7, entity.getTransLimitFlag());
        stmt.bindLong(8, entity.getCvmLimitFlag());
        stmt.bindLong(9, entity.getFloorLimitFlag());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, PayWaveInterFloorLimitBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        Long paywaveAidId = entity.getPaywaveAidId();
        if (paywaveAidId != null) {
            stmt.bindLong(2, paywaveAidId);
        }
        stmt.bindLong(3, entity.getTransType());
        stmt.bindLong(4, entity.getFloorLimit());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getCvmLimit());
        stmt.bindLong(7, entity.getTransLimitFlag());
        stmt.bindLong(8, entity.getCvmLimitFlag());
        stmt.bindLong(9, entity.getFloorLimitFlag());
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public PayWaveInterFloorLimitBean readEntity(Cursor cursor, int offset) {
        PayWaveInterFloorLimitBean entity = new PayWaveInterFloorLimitBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getLong(offset + 1), // paywaveAidId
            (byte) cursor.getShort(offset + 2), // transType
            cursor.getLong(offset + 3), // floorLimit
            cursor.getLong(offset + 4), // transLimit
            cursor.getLong(offset + 5), // cvmLimit
            (byte) cursor.getShort(offset + 6), // transLimitFlag
            (byte) cursor.getShort(offset + 7), // cvmLimitFlag
            (byte) cursor.getShort(offset + 8) // floorLimitFlag
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, PayWaveInterFloorLimitBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setPaywaveAidId(cursor.isNull(offset + 1) ? null : cursor.getLong(offset + 1));
        entity.setTransType((byte) cursor.getShort(offset + 2));
        entity.setFloorLimit(cursor.getLong(offset + 3));
        entity.setTransLimit(cursor.getLong(offset + 4));
        entity.setCvmLimit(cursor.getLong(offset + 5));
        entity.setTransLimitFlag((byte) cursor.getShort(offset + 6));
        entity.setCvmLimitFlag((byte) cursor.getShort(offset + 7));
        entity.setFloorLimitFlag((byte) cursor.getShort(offset + 8));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(PayWaveInterFloorLimitBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(PayWaveInterFloorLimitBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(PayWaveInterFloorLimitBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "interWareFloorLimit" to-many relationship of PaywaveAidBean. */
    public List<PayWaveInterFloorLimitBean> _queryPaywaveAidBean_InterWareFloorLimit(Long paywaveAidId) {
        synchronized (this) {
            if (paywaveAidBean_InterWareFloorLimitQuery == null) {
                QueryBuilder<PayWaveInterFloorLimitBean> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.PaywaveAidId.eq(null));
                paywaveAidBean_InterWareFloorLimitQuery = queryBuilder.build();
            }
        }
        Query<PayWaveInterFloorLimitBean> query = paywaveAidBean_InterWareFloorLimitQuery.forCurrentThread();
        query.setParameter(0, paywaveAidId);
        return query.list();
    }

}
