/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/07                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.annotation.Nullable;
import com.pax.bizentity.db.helper.EmvAidDbHelper;
import com.pax.bizentity.entity.EmvAid;

/**
 * Contact AID DataStore.
 */
public class EmvAidDataStore extends BaseEmvParamDataStore<EmvAidDataStore> {
    public static final String APP_NAME = "APP_NAME";
    public static final String FLOOR_LIMIT = "FLOOR_LIMIT";
    public static final String TAC_DENIAL = "TAC_DENIAL";
    public static final String TAC_ONLINE = "TAC_ONLINE";
    public static final String TAC_DEFAULT = "TAC_DEFAULT";
    public static final String DDOL = "DDOL";
    public static final String TDOL = "TDOL";
    public static final String TERM_CAP = "TERM_CAP";
    public static final String ADD_TERM_CAP = "ADD_TERM_CAP";
    public static final String RISK_MANAGE = "RISK_MANAGE";

    @Nullable
    private final EmvAid aid;

    public EmvAidDataStore(@Nullable EmvAid aid) {
        this.aid = aid;
    }

    @Override
    public void putString(String key, @Nullable String value) {
        if (aid == null) {
            return;
        }
        switch (key) {
            case TAC_DEFAULT:
                aid.setTacDefault(value);
                break;
            case TAC_DENIAL:
                aid.setTacDenial(value);
                break;
            case TAC_ONLINE:
                aid.setTacOnline(value);
                break;
            case DDOL:
                aid.setDDOL(value);
                break;
            case TDOL:
                aid.setTDOL(value);
                break;
            case TERM_CAP:
                aid.setTerminalCapability(value);
                break;
            case ADD_TERM_CAP:
                aid.setTerminalAdditionalCapability(value);
                break;
            case RISK_MANAGE:
                aid.setRiskManageData(value);
                break;
            default: return;
        }
        EmvAidDbHelper.getInstance().update(aid);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Override
    public void putLong(String key, long value) {
        if (aid == null) {
            return;
        }
        if (FLOOR_LIMIT.equals(key)) {
            aid.setFloorLimit(value);
            EmvAidDbHelper.getInstance().update(aid);
            if (refreshCachedCallback != null) {
                refreshCachedCallback.needRefresh();
            }
        }
    }

    @Nullable
    @Override
    public String getString(String key, @Nullable String defValue) {
        if (aid == null) {
            return defValue;
        }
        switch (key) {
            case APP_NAME: return aid.getAppName();
            case TAC_DEFAULT: return aid.getTacDefault();
            case TAC_DENIAL: return aid.getTacDenial();
            case TAC_ONLINE: return aid.getTacOnline();
            case DDOL: return aid.getDDOL();
            case TDOL: return aid.getTDOL();
            case TERM_CAP: return aid.getTerminalCapability();
            case ADD_TERM_CAP: return aid.getTerminalAdditionalCapability();
            case RISK_MANAGE: return aid.getRiskManageData();
            default: return defValue;
        }
    }

    @Override
    public long getLong(String key, long defValue) {
        if (aid == null) {
            return defValue;
        }
        if (FLOOR_LIMIT.equals(key)) {
            return aid.getFloorLimit();
        }
        return defValue;
    }
}
