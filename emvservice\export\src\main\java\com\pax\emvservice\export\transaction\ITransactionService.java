package com.pax.emvservice.export.transaction;

import com.pax.bizentity.entity.TransData;

public interface ITransactionService {
    TransData transInit(String reference, String amount, String pan, boolean hasPin, String track2, String aid, String appName, String expDate, String atc, String cardSerialNo, TransData.EnterMode enterMode);

    TransData transUpdate(long id, String authCode, String emvResult, TransData.ReversalStatus reversalStatus, String responseCode, String issuerCode, long stan);

    TransData getByReference(String reference);

    boolean remove(long id);
}
