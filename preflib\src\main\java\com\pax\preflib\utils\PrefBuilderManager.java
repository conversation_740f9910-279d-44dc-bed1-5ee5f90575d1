/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/01/18                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.utils;

import androidx.annotation.NonNull;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.PreferenceScreen;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/1/18
 */
public class PrefBuilderManager {
    private static final List<ExecuteCallback> beforeScreen = new LinkedList<>();
    private static final List<ExecuteCallback> afterScreen = new LinkedList<>();
    private static final Map<String, String> dependencyInfo = new LinkedHashMap<>();

    private PrefBuilderManager() {
        // do nothing
    }

    public static void registerBeforeScreen(@NonNull ExecuteCallback callback) {
        beforeScreen.add(callback);
    }

    public static void executeBeforeScreen(@NonNull PreferenceScreen screen,
            @NonNull PreferenceFragmentCompat fragment) {
        if (!beforeScreen.isEmpty()) {
            for (ExecuteCallback callback : beforeScreen) {
                callback.execute(screen, fragment);
            }
            beforeScreen.clear();
        }
    }

    public static void registerAfterScreen(@NonNull ExecuteCallback callback) {
        afterScreen.add(callback);
    }

    public static void executeAfterScreen(@NonNull PreferenceScreen screen,
            @NonNull PreferenceFragmentCompat fragment) {
        if (!afterScreen.isEmpty()) {
            for (ExecuteCallback callback : afterScreen) {
                callback.execute(screen, fragment);
            }
            afterScreen.clear();
        }
    }

    public static void registerDependency(@NonNull String key, @NonNull String dependOn) {
        dependencyInfo.put(key, dependOn);
    }

    public static void executeDependency(@NonNull DependencyCallback callback) {
        if (!dependencyInfo.isEmpty()) {
            for (Map.Entry<String, String> entry : dependencyInfo.entrySet()) {
                String prefKey = entry.getKey();
                String dependencyKey = entry.getValue();
                if (prefKey != null && dependencyKey != null) {
                    callback.execute(prefKey, dependencyKey);
                }
            }
            dependencyInfo.clear();
        }
    }

    public interface ExecuteCallback {
        void execute(@NonNull PreferenceScreen screen, @NonNull PreferenceFragmentCompat fragment);
    }

    public interface DependencyCallback {
        void execute(@NonNull String prefKey, @NonNull String dependencyKey);
    }
}
