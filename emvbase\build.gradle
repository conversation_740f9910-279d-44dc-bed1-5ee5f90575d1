apply plugin: 'com.android.library'
android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        buildConfigField('String', 'DATABASE_PWD', DATABASE_PWD)
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        stagingRelease {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        autoTest{
            buildConfigField('boolean','RELEASE','false')
        }
        debug{
            buildConfig<PERSON>ield('boolean','RELEASE','false')
        }
    }

}
dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])
    api project(":commonlib")
    testImplementation "junit:junit:$rootProject.junit"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.androidxExtJunit"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
}