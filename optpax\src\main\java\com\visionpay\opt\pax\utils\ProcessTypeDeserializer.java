package com.visionpay.opt.pax.utils;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.visionpay.opt.pax.entity.ProcessType;

import java.lang.reflect.Type;

public class ProcessTypeDeserializer implements JsonDeserializer<ProcessType> {

    @Override
    public ProcessType deserialize(JsonElement element, Type arg1, JsonDeserializationContext arg2) throws JsonParseException {
        int key = element.getAsInt();
        return ProcessType.fromValue(key);
    }

}
