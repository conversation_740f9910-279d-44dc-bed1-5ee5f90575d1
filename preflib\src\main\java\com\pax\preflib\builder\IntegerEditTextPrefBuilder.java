/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import android.text.InputType;
import android.text.TextUtils;
import android.text.method.DigitsKeyListener;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.preference.EditTextPreference;
import com.pax.commonlib.utils.LogUtils;
import com.pax.preflib.builder.callback.PrefChangedCallback;
import java.util.LinkedList;
import java.util.List;

/**
 * Create EditTextPreference that can only input integers.
 * <br>
 * 创建只能输入整数的 EditTextPreference
 */
public class IntegerEditTextPrefBuilder extends EditTextPrefBuilder<IntegerEditTextPrefBuilder> {
    private static final String TAG = "IntegerEditTextPrefBuilder";
    private final List<PrefChangedCallback<EditTextPreference, String>> prefChangedCallbackList =
            new LinkedList<>();

    protected IntegerEditTextPrefBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        super(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param titleId Title string resource id
     * @return New instance
     */
    public static IntegerEditTextPrefBuilder newInstance(@NonNull Context context,
            @NonNull String key, @StringRes int titleId) {
        return new IntegerEditTextPrefBuilder(context, key, context.getString(titleId));
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param title Title
     * @return New instance
     */
    public static IntegerEditTextPrefBuilder newInstance(@NonNull Context context,
            @NonNull String key, @NonNull String title) {
        return new IntegerEditTextPrefBuilder(context, key, title);
    }

    @Override
    protected boolean handlePrefChangedCallback(@NonNull EditTextPreference preference,
            @Nullable Object newValue) {
        if (prefChangedCallbackList.isEmpty()) {
            LogUtils.d(TAG, "Not set pref changed callback");
            return !(newValue instanceof String && ((String) newValue).isEmpty()) && newValue != null;
        }
        if (newValue instanceof Number) {
            LogUtils.d(TAG, "New value is number: " + newValue);
            return verifyNewValue(preference, newValue.toString());
        } else if (newValue != null) {
            try {
                String content = newValue.toString();
                if (TextUtils.isEmpty(content)) {
                    LogUtils.d(TAG, "New value is empty");
                    return false;
                }
                return verifyNewValue(preference, content);
            } catch (Exception e) {
                LogUtils.e(TAG, "New value parse failed", e);
                return false;
            }
        } else {
            // Cannot be null
            LogUtils.d(TAG, "New value is null");
            return false;
        }
    }

    /**
     * Add a callback to check whether the new value is legal before storing.
     * <br>
     * In fact, this callback is not much different from the {@code OnPreferenceChangeListener}
     * provided by the Preference library. But this callback passed the type conversion during
     * execution, so the value passed in this callback is a certain {@code String} type instead
     * of {@code Object}. Using this callback, you don't need to perform type judgment and
     * conversion yourself. And for {@code IntegerEditTextPrefBuilder}, it also has a special
     * function, which is to check whether the input content is empty. If you do not implement
     * this method to set your own rules, then users will be prevented from setting this option
     * to empty content by default. If you set your own inspection rules through this method, the
     * content of the incoming callback must not be empty. In other words, if the content entered
     * by the user is empty, the callback you set will not be executed and will be directly
     * refused to save.
     * <br>
     * Please do not execute the {@code setOnPreferenceChangeListener()} method after executing
     * this method. The {@code setOnPreferenceChangeListener()} method overrides the inspection
     * rules set by this method.
     * <br><br>
     * 添加用于在存储前检查新的值是否合法的回调。
     * <br>
     * 实际上，这个回调和 Preference 库提供的 {@code OnPreferenceChangeListener} 没有太大的区别。但是这个
     * 回调在执行时通过了类型转换，因此，这个回调传入的值是确定的 {@code String} 类型，而不是 {@code Object}。
     * 使用这个回调，你就不需要自己再进行类型判断和转换了。并且对于 {@code IntegerEditTextPrefBuilder}
     * 来说，它还有个特殊的作用，就是会检查输入的内容是否为空。如果你不执行该方法设置自己的规则，
     * 那么默认会阻止用户将该选项设置为空内容。如果你通过该方法设置了自己的检查规则，那么传入回调的内容一定是不为空的。
     * 换句话说，如果用户输入的内容为空，那么你设置的回调就不会被执行，
     * 会被直接拒绝保存。
     * <br>
     * 请您不要在执行了该方法以后再执行 {@code setOnPreferenceChangeListener()} 方法。{@code
     * setOnPreferenceChangeListener()} 方法会覆盖该方法设定的检查规则。
     *
     * @param callback A callback to check whether the new value is legal before storing. The
     * callback will put in a String value, which is the value the user wants to save. If this
     * value is legal, then please return {@code true} so that this value will be stored. If it
     * is not legal, please return {@code false} and this value will be discarded.
     * <br>
     * 用于在存储前检查新的值是否合法的回调。该回调会传入一个String值，这个值就是用户想要保存的值。如果这个值是合法
     * 的，那么请返回 {@code true}，这样就会将这个值存储。如果不合法，请返回 {@code false}，这个值就会被丢弃。
     * @return This builder
     */
    public IntegerEditTextPrefBuilder addPrefChangedCallback(@NonNull PrefChangedCallback<EditTextPreference, String> callback) {
        prefChangedCallbackList.add(callback);
        return this;
    }

    private boolean verifyNewValue(EditTextPreference preference, String newValue) {
        for (PrefChangedCallback<EditTextPreference, String> callback : prefChangedCallbackList) {
            if (!callback.onChanged(preference, newValue)) {
                return false;
            }
        }
        return true;
    }

    @NonNull
    @Override
    public EditTextPreference build() {
        addBindEditTextCallback(editText -> {
            editText.setInputType(InputType.TYPE_CLASS_NUMBER);
            editText.setKeyListener(DigitsKeyListener.getInstance("1234567890"));
        });
        return super.build();
    }
}
