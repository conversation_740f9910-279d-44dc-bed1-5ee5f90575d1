/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.list;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.entity.CvmType;

import java.util.ArrayList;
import java.util.List;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/19
 */
public class CvmTypeAdapter extends RecyclerView.Adapter<CvmTypeViewHolder> {
    private List<CvmType> cvmTypeList = new ArrayList<>();

    public CvmTypeAdapter setCvmTypeList(List<CvmType> cvmTypeList) {
        this.cvmTypeList = cvmTypeList;
        return this;
    }

    @NonNull
    @Override
    public CvmTypeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_cvm_type, parent, false);
        return new CvmTypeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CvmTypeViewHolder holder, int position) {
        CvmType cvmType = cvmTypeList.get(position);
        if (cvmType != null) {
            holder.cvmIcon.setImageResource(cvmType.getIcon());
            holder.cvmText.setText(cvmType.getText());
        }
    }

    @Override
    public int getItemCount() {
        return cvmTypeList.size();
    }
}
