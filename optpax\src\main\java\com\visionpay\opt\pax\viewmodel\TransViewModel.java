/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.viewmodel;

import android.app.Activity;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import com.pax.bizentity.entity.ETransType;
import com.pax.bizentity.entity.SearchMode;
import com.pax.bizlib.card.CardReaderHelper;
import com.pax.bizlib.trans.Device;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.commonlib.utils.LazyInit;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonui.clss.ClssLight;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.visionpay.opt.pax.emv.EmvPreProcessService;
import com.visionpay.opt.pax.emv.contact.ContactService;
import com.visionpay.opt.pax.activity.AdminActivity;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.dialog.RemoveCardDialog;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.contactless.ContactlessService;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.entity.EmvTag;
import com.visionpay.opt.pax.entity.ErrorEvent;
import com.visionpay.opt.pax.entity.TransStatus;
import com.visionpay.opt.pax.fragment.TransFragmentManager;
import com.visionpay.opt.pax.utils.DialogUtils;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.IEmvContactService;
import com.pax.emvservice.export.IEmvContactlessService;
import com.pax.poslib.utils.PosDeviceUtils;
import com.sankuai.waimai.router.Router;

import java.util.List;

/**
 * Trans View Model
 *
 * 主要负责启动寻卡、关闭寻卡、启动 EMV、维护状态。
 * TransViewModel 作为与寻卡类、EMV 流程类沟通的桥梁，用来显示并更新 Activity 的状态。根据 EMV 流程的进度，
 * TransViewModel 需要适时执行 TransFragmentManager 中的方法以及更新 TransStatus，以实现“卡片”显示和隐藏的控制
 * 以及“卡片”上显示的数据的更新。
 * 这样做的好处是，ViewModel 可以在设备经历旋转、切换深色模式等情况下不被销毁，因此也就可以保证界面上显示的内容不会
 * 因为这些情况而出现错乱。
 */
public class TransViewModel extends ViewModel {
    private static final String TAG = "TransViewModel";
    private static final byte defaultSearchCardType = SearchMode.INSERT | SearchMode.INTERNAL_WAVE;
    private static final String TIME_PATTERN_TRANS = "yyyyMMddHHmmss";

    private final LazyInit<IEmvContactService> emv = LazyInit.by(() ->
            Router.getService(IEmvContactService.class, EmvServiceConstant.EMVSERVICE_CONTACT));
    private final LazyInit<IEmvContactlessService> clss = LazyInit.by(() ->
            Router.getService(IEmvContactlessService.class, EmvServiceConstant.EMVSERVICE_CONTACTLESS));
    private final LazyInit<ContactService> emvService = LazyInit.by(ContactService::new);
    private final LazyInit<ContactlessService> clssService = LazyInit.by(ContactlessService::new);

    private TransStatus transStatus;
    private final MutableLiveData<Boolean> retryEnabled = new MutableLiveData<>(false);

    private final LazyInit<CardReaderHelper> cardReaderHelper =
            LazyInit.by(() -> new CardReaderHelper(new CardReaderCallback()));
    private boolean isEmvProcessing = false;
    private String procCode;
    private long amount;

    private RemoveCardCallback removeCardCallback = new RemoveCardCallback();
    private ConfirmCallback confirmCallback = new ConfirmCallback();
    private RetryCallback retryCallback = new RetryCallback();
    private ErrorCallback errorCallback = new ErrorCallback();
    private CompleteCallback completeCallback = new CompleteCallback();
    private SendingOnlineCallback sendingOnlineCallback = new SendingOnlineCallback();
    private RemoveErrorCardCallback removeErrorCardCallback;
    private MessageCallback messageCallback;
    private ErrorEventCallback errorEventCallback;

    private byte getSearchCardType() {
        return transStatus.getCurrentSearchMode();
    }

    private void updateStatus(TransStatus status) {
        this.transStatus = status;
        TransFragmentManager.getInstance().updateStatus(status);
    }

    private TransStatus getStatus() {
        return transStatus;
    }

    public LiveData<Boolean> listenRetryEnable() {
        return retryEnabled;
    }

    public boolean isEmvProcessing() {
        return isEmvProcessing;
    }

    public void initTrans(@NonNull ETransType transType, long amount) {
        LogUtils.d(TAG, "Init trans");
        this.procCode = transType.getProcCode();
        this.amount = amount;

        byte searchCardMode = new EmvPreProcessService(
                this.procCode,
                amount,
                Device.getTime(TIME_PATTERN_TRANS),
                0,
                defaultSearchCardType,
                CurrencyConverter.getCurrencyCode(),
                ""
        ).start();
        LogUtils.d(TAG, "Search Card Mode: " + searchCardMode);

        TransFragmentManager.getInstance().show(TransFragmentManager.SEARCH_CARD_PROMPT);
        updateStatus(TransStatus.newInstance(searchCardMode));

        PosDeviceUtils.enableHomeRecentKey(false);
        PosDeviceUtils.enableStatusBar(false);
    }

    public void startSearchCard() {
        LogUtils.d(TAG, "Start Search Card");
        cardReaderHelper.get().stopPolling();
        cardReaderHelper.get().polling(getSearchCardType());
        retryEnabled.postValue(false);
    }

    public void stopSearchCard() {
        LogUtils.d(TAG, "Stop Search Card");
        cardReaderHelper.get().stopPolling();
        updateStatus(getStatus().stopSearchCard()
                .setIccStatusPrompt("Stop")
                .setPiccStatusPrompt("Stop"));
    }

    public void retry() {
        if (removeErrorCardCallback != null) {
            removeErrorCardCallback.onRemove();
        }
        byte searchCardMode = new EmvPreProcessService(
                this.procCode,
                this.amount,
                Device.getTime(TIME_PATTERN_TRANS),
                0,
                defaultSearchCardType,
                CurrencyConverter.getCurrencyCode(),
                ""
        ).start();
        TransFragmentManager.getInstance()
                .hideAll()
                .show(TransFragmentManager.SEARCH_CARD_PROMPT);
        updateStatus(TransStatus.newInstance(searchCardMode));
        PosDeviceUtils.enableHomeRecentKey(false);
        PosDeviceUtils.enableStatusBar(false);
        startSearchCard();
    }

    public void setRemoveErrorCardCallback(@Nullable RemoveErrorCardCallback callback) {
        this.removeErrorCardCallback = callback;
    }

    public void setMessageCallback(@Nullable MessageCallback messageCallback) {
        this.messageCallback = messageCallback;
    }

    public void setErrorEventCallback(@Nullable ErrorEventCallback errorEventCallback) {
        this.errorEventCallback = errorEventCallback;
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        stopSearchCard();
        removeCardCallback = null;
        confirmCallback = null;
        retryCallback = null;
        errorCallback = null;
        completeCallback = null;
        removeErrorCardCallback = null;
        messageCallback = null;
        errorEventCallback = null;
        App.getApp().runInBackground(() -> {
            PosDeviceUtils.enableHomeRecentKey(true);
            PosDeviceUtils.enableStatusBar(true);
        });
    }

    private class CardReaderCallback implements CardReaderHelper.Callback {
        private String lastMessage = null;
        private long lastTime = 0;

        @Override
        public void onIccDisable() {
            byte type = getSearchCardType();
            if (SearchMode.isSupportIcc(type)) {
                // Disable Icc
                updateStatus(getStatus().disableIcc());
            }
        }

        @Override
        public void onPiccDisable() {
            byte type = getSearchCardType();
            if (SearchMode.isWave(type)) {
                // Disable Picc
                updateStatus(getStatus().disablePicc());
            }
        }

        @Override
        public void onError(String message) {
            // Display SnackBar or Toast
            if (lastMessage != null && lastMessage.equals(message) && (System.currentTimeMillis() - lastTime < 2000)) {
                return;
            }
            lastMessage = message;
            lastTime = System.currentTimeMillis();
            if (messageCallback != null) {
                messageCallback.onSend(message);
            }
        }

        @Override
        public void onIccDetected() {
            Activity activity = ActivityStack.getInstance().top();
            if (activity instanceof FragmentActivity) {
                DialogUtils.createProcessingDialog((FragmentActivity) activity, "EMV Processing",
                        "Contact EMV service running, please wait...");
            }
            updateStatus(getStatus().insertProcessing());
            emvService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .setSendinfOnlineCallback(sendingOnlineCallback)
                    .start(emv.get(), amount, "", SearchMode.INSERT, "");
            isEmvProcessing = true;
        }

        @Override
        public void onPiccDetected(byte[] serialInfo) {
            Activity activity = ActivityStack.getInstance().top();
            if (activity instanceof FragmentActivity) {
                DialogUtils.createProcessingDialog((FragmentActivity) activity, "EMV Processing",
                        "Contactless EMV service running, please wait...");
            }
            updateStatus(getStatus().clssProcessing());
            clssService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .setRemoveCardCallback(removeCardCallback)
                    .setRetryCallback(retryCallback)
                    .setSendinfOnlineCallback(sendingOnlineCallback)
                    .start(clss.get(), amount, "", SearchMode.WAVE, "");
            isEmvProcessing = true;
        }
    }

    private class SendingOnlineCallback implements ISendingOnlineCallback {

        @Override
        public void onStart() {
        }

        @Override
        public void onComplete(boolean approved, boolean hasPin, String merchantId, String STAN, String gatewayReposnse, String gatewayResponseCode, String authorizationId) {
        }

        @Override
        public void onError() {
        }

        @Override
        public void onTimeOut() {
        }
    }

    private class RemoveCardCallback implements ContactlessService.RemoveCardCallback {
        @Override
        public void onRemove() {
            updateStatus(getStatus().clssDone());
        }
    }

    private class ConfirmCallback implements IEmvService.ConfirmCallback {
        @Override
        public void onConfirm(
                @NonNull String pan,
                int issuerIconId,
                @NonNull String issuerName,
                @NonNull String cardHolderName,
                @Nullable List<EmvAidInfo> infoList,
                @Nullable String editAidPath,
                @Nullable String editAidParam,
                @Nullable String cardSequenceNumber,
                @Nullable String cardExpiryDate,
                @Nullable String applicationLabel,
                @Nullable int applicationTransactionCounter,
                @Nullable boolean isBankCard) {
            String enterMode;
            if (getStatus().isContactService()) {
                enterMode = "Contact";
            } else {
                enterMode = "Contactless";
            }
            TransFragmentManager.getInstance()
                    .hide(TransFragmentManager.SEARCH_CARD_PROMPT)
                    .show(TransFragmentManager.CONFIRM_AID);
            updateStatus(getStatus()
                    .setPan(formatPan(pan))
                    .setIssuerIconId(issuerIconId)
                    .setIssuerName(issuerName)
                    .setCardHolderName(cardHolderName)
                    .setEnterMode(enterMode)
                    .setAidInfoList(infoList)
                    .setEditAidPath(editAidPath)
                    .setEditAidParam(editAidParam));
        }

        private String formatPan(String str) {
            StringBuilder result = new StringBuilder();
            for (int i = 1; i <= str.length(); i++) {
                result.append(str.charAt(i - 1));
                if (i == str.length()) {
                    break;
                }
                if (i % 4 == 0) {
                    result.append(" ");
                }
            }
            return result.toString();
        }
    }

    private class RetryCallback implements ContactlessService.RetryCallback {
        @Override
        public void onRetry(byte searchMode, String contactPrompt, String clssPrompt,
                boolean needRemoveCard) {
            DialogUtils.dismiss();
            if (needRemoveCard) {
                try {
                    final boolean[] isShown = { false };
                    RemoveCardDialog dialog = new RemoveCardDialog();
                    Device.removeCard(result -> {
                        if (!isShown[0]) {
                            Activity activity = ActivityStack.getInstance().top();
                            if (activity instanceof FragmentActivity) {
                                FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
                                dialog.show(manager, "Remove Card");
                                isShown[0] = true;
                            }
                        }
                    });
                    dialog.dismissAllowingStateLoss();
                } catch (Exception e) {
                    LogUtils.e(TAG, "Remove card error", e);
                }
            }
            TransFragmentManager.getInstance()
                    .hideAll()
                    .show(TransFragmentManager.SEARCH_CARD_PROMPT);
            updateStatus(TransStatus.newInstance(getSearchCardType())
                    .setCurrentSearchMode(searchMode)
                    .setLight1Status(SearchMode.isWave(searchMode) ? ClssLight.BLINK : ClssLight.OFF)
                    .setIccStatusPrompt(contactPrompt)
                    .setPiccStatusPrompt(clssPrompt));
            startSearchCard();
        }
    }

    private class ErrorCallback implements IEmvService.ErrorCallback {
        @Override
        public void onError(@NonNull String title, String reason) {
            updateStatus(getStatus()
                    .stopSearchCard()
                    .setIccStatusPrompt("Stop")
                    .setPiccStatusPrompt("Stop"));
            // Show error reason
            if (errorEventCallback != null) {
                errorEventCallback.onSend(new ErrorEvent()
                        .setTitle(title)
                        .setInfo(reason)
                        .setButtonContent("FINISH")
                        .setButtonClickListener(v -> ActivityStack.getInstance().popTo(AdminActivity.class)));
            }
            PosDeviceUtils.enableHomeRecentKey(true);
            PosDeviceUtils.enableStatusBar(true);
            isEmvProcessing = false;

            DialogUtils.dismiss();
            if (messageCallback != null) {
                messageCallback.onSend("EMV Process Failed.");
            }
            retryEnabled.postValue(true);
        }
    }

    private class CompleteCallback implements IEmvService.CompleteCallback {
        @Override
        public void onComplete(boolean isContact, @NonNull TransResultEnum result,
                @NonNull List<CvmType> cvmTypeList,
                @NonNull List<EmvTag> emvTagList) {
            // Show EMV tag
            TransFragmentManager.getInstance().show(TransFragmentManager.EMV_TAG);
            updateStatus(getStatus()
                    .stopSearchCard()
                    .setLight3Status(isContact ? ClssLight.OFF : ClssLight.ON)
                    .setContactService(isContact)
                    .setTransResult(getTransResult(result))
                    .setCvmTypeList(cvmTypeList)
                    .setEmvTagList(emvTagList));
            PosDeviceUtils.enableHomeRecentKey(true);
            PosDeviceUtils.enableStatusBar(true);
            isEmvProcessing = false;

            DialogUtils.dismiss();
            if (messageCallback != null) {
                messageCallback.onSend("EMV Process Complete.");
            }
            retryEnabled.postValue(true);
        }

        private String getTransResult(TransResultEnum resultEnum) {
            switch (resultEnum) {
                case RESULT_FALLBACK:
                    return "Fallback";
                case RESULT_TRY_AGAIN:
                    return "Try Again";
                case RESULT_REQ_ONLINE:
                    return "Request Online";
                case RESULT_ONLINE_DENIED:
                    return "Online Denied";
                case RESULT_ONLINE_FAILED:
                    return "Online Failed";
                case RESULT_CLSS_SEE_PHONE:
                    return "See Phone";
                case RESULT_OFFLINE_DENIED:
                    return "Offline Denied";
                case RESULT_ONLINE_APPROVED:
                    return "Online Approved";
                case RESULT_SIMPLE_FLOW_END:
                    return "Simple Flow End";
                case RESULT_OFFLINE_APPROVED:
                    return "Offline Approved";
                case RESULT_ONLINE_CARD_DENIED:
                    return "Online Approved Card Denied";
                case RESULT_CLSS_TRY_ANOTHER_INTERFACE:
                    return "Try Another Interface";
                case RESULT_ONLINE_FAILED_CARD_APPROVED:
                    return "Online Failed Card Approved";
            }
            return resultEnum.name();
        }
    }

    public interface RemoveErrorCardCallback {
        void onRemove();
    }

    public interface MessageCallback {
        void onSend(String message);
    }

    public interface ErrorEventCallback {
        void onSend(ErrorEvent event);
    }
}
