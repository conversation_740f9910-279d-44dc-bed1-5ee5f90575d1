<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/04/08                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/main_background"
    tools:context="com.visionpay.opt.pax.activity.TransActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:liftOnScroll="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/trans_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/main_background"
            app:title="@string/emv_process_title"
            app:titleTextColor="@color/main_toolbar_content"
            app:titleCentered="true"
            app:contentInsetStart="@dimen/toolbar_content_to_screen_edge"/>

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/page_content_horizontal_spacing"
        android:layout_marginEnd="@dimen/page_content_horizontal_spacing"
        android:orientation="horizontal"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:overScrollMode="never">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/trans_amount_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/card_margin_horizontal"
                    android:layout_marginEnd="@dimen/card_margin_horizontal"
                    android:layout_marginTop="@dimen/page_content_to_toolbar"
                    android:layout_marginBottom="@dimen/page_content_to_screen_edge"
                    app:cardElevation="0dp"
                    app:cardCornerRadius="@dimen/card_corner_radius"
                    app:strokeWidth="@dimen/card_stroke_width"
                    app:strokeColor="@color/common_divider">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/card_padding_vertical"
                        android:paddingHorizontal="@dimen/card_padding_horizontal">

                        <TextView
                            android:id="@+id/trans_amount_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/amount"
                            android:textColor="@color/main_text"
                            android:textSize="@dimen/text_medium_size"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:id="@+id/trans_amount_content"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textColor="@color/main_text"
                            android:textSize="@dimen/text_medium_size"
                            android:fontFamily="monospace"
                            tools:text="$123.45"/>

                        <TextView
                            android:id="@+id/trans_type_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            android:text="@string/trans_type"
                            android:textColor="@color/main_text"
                            android:textSize="@dimen/text_medium_size"
                            android:fontFamily="sans-serif-medium" />

                        <TextView
                            android:id="@+id/trans_type_content"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textColor="@color/main_text"
                            android:textSize="@dimen/text_medium_size"
                            tools:text="Sale"/>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/trans_scroll_view"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:overScrollMode="never">

            <LinearLayout
                android:id="@+id/trans_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/scrollable_page_content_to_screen_bottom"
                android:orientation="vertical" />

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/trans_retry_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_marginBottom="24dp"
        android:layout_marginEnd="@dimen/page_content_to_screen_edge"
        android:visibility="gone"
        android:text="@string/search_card_retry"
        app:icon="@drawable/ic_retry" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>