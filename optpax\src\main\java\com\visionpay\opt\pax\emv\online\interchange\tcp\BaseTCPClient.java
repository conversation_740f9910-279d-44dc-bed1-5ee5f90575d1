package com.visionpay.opt.pax.emv.online.interchange.tcp;

import java.io.IOException;
import java.util.concurrent.locks.ReentrantLock;

public abstract class BaseTCPClient {
    protected String host, ip, port;

    protected ReentrantLock Terminate, Terminated;
    protected static Thread Thread;

    protected TCPEvents handler;

    protected boolean Connected = false;

    public BaseTCPClient(String hostName, String hostIP, String port){
        this.host = hostName;
        this.ip = hostIP;
        this.port = port;
    }

    public abstract boolean open() throws IOException;
    public abstract void close() throws IOException;

    public abstract boolean send(String s) throws IOException;
    public abstract boolean send(byte[] buffer) throws IOException;

    protected void threadMethod(){
        Terminated.lock();
        boolean alive = true;
        while (alive && Terminate.tryLock())
        {
            alive = threadLoopMethod();
            Terminate.unlock();
            try {
                Terminated.wait(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        Terminate.unlock();
        Terminated.unlock();
    }

    protected boolean threadLoopMethod(){
        return true;
    }
}
