/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                  Author	                 Action
 * 20190108  	         xieYb                   Create
 * ===========================================================================================
 */

package com.pax.poslib.neptune;

import com.pax.dal.IIDReaderEx;
/**
 * neptune IIDReaderEx
 */
class DemoReaderEx implements IIDReaderEx {
    @Override
    public void start(IDReaderExListener idReaderExListener) {
        //do nothing
    }

    @Override
    public void stop() {
        //do nothing
    }
}
