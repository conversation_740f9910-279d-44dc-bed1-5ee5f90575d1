/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.visionpay.opt.pax.entity.CvmType;

import java.util.List;

/**
 * EMV Process Callback
 */
public interface IEmvProcessCallback {
    @NonNull
    IEmvProcessCallback setConfirmCallback(@Nullable IEmvService.ConfirmCallback callback);

    @NonNull
    IEmvProcessCallback setErrorCallback(@Nullable IEmvService.ErrorCallback callback);

    @NonNull
    IEmvProcessCallback setCvmTypeList(List<CvmType> cvmTypeList);
}
