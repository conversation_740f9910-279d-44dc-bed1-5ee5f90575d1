/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/13                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.annotation.Nullable;
import com.pax.bizentity.db.helper.PaywaveFloorLimitDbHelper;
import com.pax.bizentity.entity.clss.paywave.PayWaveInterFloorLimitBean;

/**
 * Contactless PayWave Floor Limit DataStore.
 */
public class PayWaveFloorLimitDataStore extends BaseEmvParamDataStore<PayWaveFloorLimitDataStore> {
    public static final String TRANS_LIMIT = "TRANS_LIMIT";
    public static final String FLOOR_LIMIT = "FLOOR_LIMIT";
    public static final String CVM_LIMIT = "CVM_LIMIT";

    @Nullable
    private final PayWaveInterFloorLimitBean limitBean;

    public PayWaveFloorLimitDataStore(@Nullable PayWaveInterFloorLimitBean limitBean) {
        this.limitBean = limitBean;
    }

    @Override
    public void putLong(String key, long value) {
        if (limitBean == null) {
            return;
        }
        switch (key) {
            case TRANS_LIMIT:
                limitBean.setTransLimit(value);
                break;
            case FLOOR_LIMIT:
                limitBean.setFloorLimit(value);
                break;
            case CVM_LIMIT:
                limitBean.setCvmLimit(value);
                break;
            default: return;
        }
        PaywaveFloorLimitDbHelper.getInstance().update(limitBean);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Override
    public long getLong(String key, long defValue) {
        if (limitBean == null) {
            return defValue;
        }
        switch (key) {
            case TRANS_LIMIT: return limitBean.getTransLimit();
            case FLOOR_LIMIT: return limitBean.getFloorLimit();
            case CVM_LIMIT: return limitBean.getCvmLimit();
            default: return defValue;
        }
    }
}
