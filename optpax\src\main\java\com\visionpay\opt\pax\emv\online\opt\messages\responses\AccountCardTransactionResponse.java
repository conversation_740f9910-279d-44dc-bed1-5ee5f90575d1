package com.visionpay.opt.pax.emv.online.opt.messages.responses;

import androidx.annotation.Keep;

import com.visionpay.opt.pax.emv.online.interchange.messages.responses.ResponseCodeHelper;

@Keep
public class AccountCardTransactionResponse extends BaseResponse {
    private String authCode;
    private String hostResponse;
    private String merchantId;
    private long stan;
    private String rrn;

    public AccountCardTransactionResponse(){
        merchantId = "";
        authCode = "";
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getHostResponse() {
        return hostResponse;
    }

    public void setHostResponse(String hostResponse) {
        this.hostResponse = hostResponse;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public long getSTAN() {
        return stan;
    }

    public void setSTAN(long stan) {
        this.stan = stan;
    }

    public String getRRN() {
        return rrn;
    }

    public void setRRN(String rrn) {
        this.rrn = rrn;
    }

    public boolean isApproved(){
        return this.hostResponse.equals("00") || this.hostResponse.equals("08")
                || this.hostResponse.equals("10") || this.hostResponse.equals("11")
                || this.hostResponse.equals("16");
    }

    public boolean isTimeout(){
        return this.hostResponse.equals("V0");
    }

    public boolean isError(){
        return this.hostResponse.equals("96") || this.hostResponse.equals("06");
    }

    public boolean isDecline(){
        return !isApproved() && !isTimeout() && !isError();
    }

    public String getGatwayResponse(){
        return ResponseCodeHelper.getGatwayResponse(hostResponse);
    }
}
