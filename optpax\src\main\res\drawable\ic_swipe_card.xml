<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2021/04/01                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <path
        android:pathData="M11,8L17,8A2,2 0,0 1,19 10L19,19A2,2 0,0 1,17 21L11,21A2,2 0,0 1,9 19L9,10A2,2 0,0 1,11 8z"
        android:strokeWidth="2"
        android:strokeColor="@color/color_search_card_icon"/>
    <path
        android:pathData="M9,14L9,14L5,14C3.8954,14 3,13.1792 3,12.1667L3,4.8333C3,3.8208 3.8954,3 5,3L19,3C20.1046,3 21,3.8208 21,4.8333L21,11.25L21,11.25L21,12.1667C21,13.1792 20.1046,14 19,14"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:strokeColor="@color/color_search_card_icon"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M3,9h6v3h-6z"
        android:fillColor="@color/color_search_card_icon"/>
</vector>
