/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/12                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.annotation.Nullable;
import com.pax.bizentity.db.helper.DpasAidDbHelper;
import com.pax.bizentity.entity.clss.dpas.DpasAidBean;

/**
 * Contactless DPAS AID DataStore.
 */
public class DpasAidDataStore extends BaseEmvParamDataStore<DpasAidDataStore> {
    public static final String APP_NAME = "APP_NAME";
    public static final String TRANS_LIMIT = "TRANS_LIMIT";
    public static final String FLOOR_LIMIT = "FLOOR_LIMIT";
    public static final String CVM_LIMIT = "CVM_LIMIT";
    public static final String TAC_DENIAL = "TAC_DENIAL";
    public static final String TAC_ONLINE = "TAC_ONLINE";
    public static final String TAC_DEFAULT = "TAC_DEFAULT";
    public static final String TERM_CAP = "TERM_CAP";
    public static final String TTQ = "TTQ";

    @Nullable
    private final DpasAidBean aid;

    public DpasAidDataStore(@Nullable DpasAidBean aid) {
        this.aid = aid;
    }

    @Override
    public void putString(String key, @Nullable String value) {
        if (aid == null) {
            return;
        }
        switch (key) {
            case TAC_DENIAL:
                aid.setTacDenial(value);
                break;
            case TAC_ONLINE:
                aid.setTacOnline(value);
                break;
            case TAC_DEFAULT:
                aid.setTacDefault(value);
                break;
            case TERM_CAP:
                aid.setTerminalCapability(value);
                break;
            case TTQ:
                aid.setTtq(value);
                break;
            default: return;
        }
        DpasAidDbHelper.getInstance().update(aid);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Override
    public void putLong(String key, long value) {
        if (aid == null) {
            return;
        }
        switch (key) {
            case TRANS_LIMIT:
                aid.setTransLimit(value);
                break;
            case FLOOR_LIMIT:
                aid.setFloorLimit(value);
                break;
            case CVM_LIMIT:
                aid.setCvmLimit(value);
                break;
            default: return;
        }
        DpasAidDbHelper.getInstance().update(aid);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Nullable
    @Override
    public String getString(String key, @Nullable String defValue) {
        if (aid == null) {
            return defValue;
        }
        switch (key) {
            case APP_NAME: return aid.getAppName();
            case TAC_DEFAULT: return aid.getTacDefault();
            case TAC_DENIAL: return aid.getTacDenial();
            case TAC_ONLINE: return aid.getTacOnline();
            case TERM_CAP: return aid.getTerminalCapability();
            case TTQ: return aid.getTtq();
            default: return defValue;
        }
    }

    @Override
    public long getLong(String key, long defValue) {
        if (aid == null) {
            return defValue;
        }
        switch (key) {
            case TRANS_LIMIT: return aid.getTransLimit();
            case FLOOR_LIMIT: return aid.getFloorLimit();
            case CVM_LIMIT: return aid.getCvmLimit();
            default: return defValue;
        }
    }
}
