/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import android.text.TextUtils;
import androidx.annotation.ArrayRes;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.preference.ListPreference;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.preflib.builder.callback.ConvertCallback;
import com.pax.preflib.builder.callback.PrefChangedCallback;
import com.pax.preflib.builder.factory.PrefFactory;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * Create a ListPreference.
 * <br>
 * 创建一个 ListPreference
 */
public class ListPrefBuilder extends BasePrefBuilder<ListPreference, ListPrefBuilder> {
    private List<String> displayEntries = new LinkedList<>();
    private List<String> storageEntries = new LinkedList<>();
    private final List<PrefChangedCallback<ListPreference, String>> prefChangedCallbackList =
            new LinkedList<>();

    private ListPrefBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        super(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param titleId Title string resource id
     * @return New instance
     */
    public static ListPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @StringRes int titleId) {
        return new ListPrefBuilder(context, key, context.getString(titleId));
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param title Title
     * @return New instance
     */
    public static ListPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @NonNull String title) {
        return new ListPrefBuilder(context, key, title);
    }

    /**
     * Add a list item.
     *
     * @param displayValue Display content
     * @param storageValue Storage value
     * @return This builder
     */
    public ListPrefBuilder addEntry(@NonNull String displayValue, @NonNull String storageValue) {
        if (!TextUtils.isEmpty(displayValue) && !TextUtils.isEmpty(storageValue)) {
            displayEntries.add(displayValue);
            storageEntries.add(storageValue);
        }
        return this;
    }

    /**
     * Add a list item with the same display content and storage value.
     *
     * @param value Display content and storage value
     * @return This builder
     */
    public ListPrefBuilder addEntry(@NonNull String value) {
        return addEntry(value, value);
    }

    /**
     * Add list items from a List.
     *
     * @param list List object
     * @param displayValueConvertCallback Display content convert callback. This method will take
     * out the items in the incoming List one by one, and then use this callback to convert the
     * taken out items into String type content as the display content.
     * <br>
     * 显示内容转换回调。该方法会将传入的 List 中的 item 逐一取出，然后通过这个 callback 将取出的 item
     * 转换成字符串类型的内容作为显示内容。
     * @param storageValueConvertCallback Storage value convert callback. This method will take
     * out the items in the incoming List one by one, and then use this callback to convert the
     * taken out items into String type content as the storage value.
     * <br>
     * 存储内容转换回调。该方法会将传入的 List 中的 item 逐一取出，然后通过这个 callback 将取出的 item
     * 转换成字符串类型的内容作为存储内容。
     * @param <T> The type of each item in the list
     * @return This builder
     */
    public <T> ListPrefBuilder addEntries(@NonNull List<T> list,
            @NonNull ConvertCallback<T, String> displayValueConvertCallback,
            @NonNull ConvertCallback<T, String> storageValueConvertCallback) {
        for (T item : list) {
            addEntry(displayValueConvertCallback.onConvert(item),
                    storageValueConvertCallback.onConvert(item));
        }
        return this;
    }

    /**
     * Add list items from a List.
     *
     * @param list List object
     * @param valueConvertCallback Display content and storage value convert callback. This method
     * will take out the items in the incoming List one by one, and then use this callback to
     * convert the taken out items into String type content as the display content and storage
     * value.
     * <br>
     * 显示内容和存储内容转换回调。该方法会将传入的 List 中的 item 逐一取出，然后通过这个 callback 将取出的 item
     * 转换成字符串类型的内容作为显示内容和存储内容。
     * @param <T> The type of each item in the list
     * @return This builder
     */
    public <T> ListPrefBuilder addEntries(@NonNull List<T> list,
            @NonNull ConvertCallback<T, String> valueConvertCallback) {
        return addEntries(list, valueConvertCallback, valueConvertCallback);
    }

    /**
     * Batch add display content and storage value.
     *
     * @param value Display content and storage value
     * @return This builder
     */
    public ListPrefBuilder addEntries(@NonNull String... value) {
        return addEntries(Arrays.asList(value), item -> item);
    }

    /**
     * Add list items from resource.
     *
     * @param displayValueArrayId Display content array resource id
     * @param storageValueArrayId Storage value array resource id
     * @return This builder
     */
    public ListPrefBuilder addEntries(@ArrayRes int displayValueArrayId, @ArrayRes int storageValueArrayId) {
        displayEntries = ResourceUtil.getMutableList(displayValueArrayId);
        storageEntries = ResourceUtil.getMutableList(storageValueArrayId);
        return this;
    }

    /**
     * Add a callback to check whether the new value is legal before storing.
     * <br>
     * In fact, this callback is not much different from the {@code OnPreferenceChangeListener}
     * provided by the Preference library. But this callback passed the type conversion during
     * execution, so the value passed in this callback is a certain {@code String} type instead
     * of {@code Object}. Using this callback, you don't need to perform type judgment and
     * conversion yourself.
     * <br>
     * Please do not execute the {@code setOnPreferenceChangeListener()} method after executing
     * this method. The {@code setOnPreferenceChangeListener()} method overrides the inspection
     * rules set by this method.
     * <br><br>
     * 添加用于在存储前检查新的值是否合法的回调。
     * <br>
     * 实际上，这个回调和 Preference 库提供的 {@code OnPreferenceChangeListener} 没有太大的区别。但是这个
     * 回调在执行时通过了类型转换，因此，这个回调传入的值是确定的 {@code String} 类型，而不是 {@code Object}。
     * 使用这个回调，你就不需要自己再进行类型判断和转换了。
     * <br>
     * 请您不要在执行了该方法以后再执行 {@code setOnPreferenceChangeListener()} 方法。{@code
     * setOnPreferenceChangeListener()} 方法会覆盖该方法设定的检查规则。
     *
     * @param callback A callback to check whether the new value is legal before storing. The
     * callback will put in a String value, which is the value the user wants to save. If this
     * value is legal, then please return {@code true} so that this value will be stored. If it
     * is not legal, please return {@code false} and this value will be discarded.
     * <br>
     * 用于在存储前检查新的值是否合法的回调。该回调会传入一个String值，这个值就是用户想要保存的值。如果这个值是合法
     * 的，那么请返回 {@code true}，这样就会将这个值存储。如果不合法，请返回 {@code false}，这个值就会被丢弃。
     * @return This builder
     */
    public ListPrefBuilder addPrefChangedCallback(@NonNull PrefChangedCallback<ListPreference, String> callback) {
        prefChangedCallbackList.add(callback);
        return this;
    }

    private boolean verifyNewValue(ListPreference preference, String newValue) {
        for (PrefChangedCallback<ListPreference, String> callback : prefChangedCallbackList) {
            if (!callback.onChanged(preference, newValue)) {
                return false;
            }
        }
        return true;
    }

    @NonNull
    @Override
    public ListPreference build() {
        ListPreference preference = PrefFactory.getInstance().createListPreference(context);
        preference.setKey(key);
        preference.setTitle(title);
        preference.setDialogTitle(title);
        preference.setSummaryProvider(ListPreference.SimpleSummaryProvider.getInstance());
        preference.setEntries(displayEntries.toArray(new String[]{}));
        preference.setEntryValues(storageEntries.toArray(new String[]{}));
        preference.setIconSpaceReserved(false);
        if (!prefChangedCallbackList.isEmpty()) {
            preference.setOnPreferenceChangeListener((pref, newValue) -> {
                if (newValue instanceof String) {
                    return verifyNewValue(preference, (String) newValue);
                } else if (newValue != null) {
                    return verifyNewValue(preference, newValue.toString());
                } else {
                    return verifyNewValue(preference, null);
                }
            });
        }
        if (onPreferenceClickListener != null) {
            preference.setOnPreferenceClickListener(onPreferenceClickListener);
        }
        return preference;
    }
}
