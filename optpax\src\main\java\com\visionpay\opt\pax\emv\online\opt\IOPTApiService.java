package com.visionpay.opt.pax.emv.online.opt;

import com.visionpay.opt.pax.emv.online.opt.messages.requests.AccountCardAuthenticateRequest;
import com.visionpay.opt.pax.emv.online.opt.messages.requests.AccountCardAuthoriseRequest;
import com.visionpay.opt.pax.emv.online.opt.messages.requests.AccountCardTransactionRequest;
import com.visionpay.opt.pax.emv.online.opt.messages.responses.AccountCardTransactionResponse;
import com.visionpay.opt.pax.emv.online.opt.messages.responses.BaseResponse;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

public interface IOPTApiService {
    @POST("/api/accountcard/terminal/authenticate")
    Call<BaseResponse>
    authenticate(@Body AccountCardAuthenticateRequest request);

    @POST("/api/accountcard/authorise")
    Call<AccountCardTransactionResponse>
    authorise(@Body AccountCardAuthoriseRequest request, @Header("Authorization") String token);

    @POST("/api/accountcard/capture")
    Call<AccountCardTransactionResponse>
    capture(@Body AccountCardTransactionRequest request, @Header("Authorization") String token);

    @POST("/api/accountcard/reverse")
    Call<AccountCardTransactionResponse>
    reverse(@Body AccountCardTransactionRequest request, @Header("Authorization") String token);
}
