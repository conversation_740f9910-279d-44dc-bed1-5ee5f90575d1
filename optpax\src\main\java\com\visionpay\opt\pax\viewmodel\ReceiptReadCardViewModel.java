package com.visionpay.opt.pax.viewmodel;
import static com.pax.commonlib.utils.ConvertUtils.TIME_PATTERN_TRANS;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModel;

import com.pax.bizentity.entity.ETransType;
import com.pax.bizentity.entity.SearchMode;
import com.pax.bizlib.card.CardReaderHelper;
import com.pax.bizlib.trans.Device;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LazyInit;
import com.pax.commonlib.utils.LogUtils;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.visionpay.opt.pax.activity.AdminActivity;
import com.visionpay.opt.pax.emv.EmvPreProcessService;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.ISimpleEmvService;
import com.visionpay.opt.pax.emv.SimpleContactService;
import com.visionpay.opt.pax.emv.SimpleEmvService;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.entity.EmvTag;
import com.visionpay.opt.pax.entity.ErrorEvent;
import com.visionpay.opt.pax.utils.DialogUtils;

import java.util.List;
public class ReceiptReadCardViewModel extends ViewModel {
    private static final String TAG = "ReceiptReadCardViewModel";
    private static final byte defaultSearchCardType = SearchMode.INSERT | SearchMode.INTERNAL_WAVE | SearchMode.SWIPE | SearchMode.INTERNAL_MIFARE;

    private final LazyInit<ISimpleEmvService> emv = LazyInit.by(SimpleEmvService::new);

    private final LazyInit<SimpleContactService> emvService = LazyInit.by(SimpleContactService::new);
    private final LazyInit<CardReaderHelper> cardReaderHelper =
            LazyInit.by(() -> new CardReaderHelper(new CardReaderCallback()));

    private ConfirmCallback confirmCallback = new ConfirmCallback();
    private ErrorCallback errorCallback = new ErrorCallback();
    private CompleteCallback completeCallback = new CompleteCallback();

    private MessageCallback messageCallback;
    private ErrorEventCallback errorEventCallback;
    private CardDetectCallback cardDetectCallback;

    private CompleteReaderCallback completeReaderCallback;

    private String cardSignature;

    private String lastMessage;
    private long lastTime;

    private byte getSearchCardType() {
        return defaultSearchCardType;
    }

    public void initTrans(@NonNull ETransType transType) {
        LogUtils.d(TAG, "Init trans");

        byte searchCardMode = new EmvPreProcessService(
                transType.getProcCode(),
                1,
                Device.getTime(TIME_PATTERN_TRANS),
                0,
                defaultSearchCardType,
                ConvertUtils.strToBcdPaddingLeft(String.valueOf(36)),
                "0"
        ).start();
        LogUtils.d(TAG, "Search Card Mode: " + searchCardMode);
    }

    public void startSearchCard() {
        LogUtils.d(TAG, "Start Search Card");
        cardReaderHelper.get().stopPolling();
        cardReaderHelper.get().polling(getSearchCardType());
    }

    public void stopSearchCard() {
        LogUtils.d(TAG, "Stop Search Card");
        cardReaderHelper.get().stopPolling();
    }

    public void setMessageCallback(@Nullable MessageCallback messageCallback) {
        this.messageCallback = messageCallback;
    }

    public void setCardDetectCallback(@Nullable CardDetectCallback cardDetectCallback) {
        this.cardDetectCallback = cardDetectCallback;
    }

    public void setCompleteReaderCallback(@Nullable CompleteReaderCallback completeReaderCallback) {
        this.completeReaderCallback = completeReaderCallback;
    }

    public void setErrorEventCallback(@Nullable ErrorEventCallback errorEventCallback) {
        this.errorEventCallback = errorEventCallback;
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        stopSearchCard();
        confirmCallback = null;
        errorCallback = null;
        completeCallback = null;
        messageCallback = null;
        errorEventCallback = null;
        cardDetectCallback = null;
    }

    private class CardReaderCallback implements CardReaderHelper.Callback {
        @Override
        public void onIccDetected() {
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.INSERT);

            emvService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .start(emv.get(), 0, "", SearchMode.INSERT, "");
        }

        @Override
        public void onPiccDetected(byte[] serialInfo) {
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.WAVE);

            emvService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .start(emv.get(), 0, "", SearchMode.WAVE, "");
        }

        @Override
        public void onMagDetected(String trackData1, String trackData2, String trackData3) {
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.SWIPE);

            emv.get().setTrackData(trackData1, trackData2, trackData3);
            emvService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .start(emv.get(), 0, "", SearchMode.SWIPE, "");
        }

        @Override
        public void onMifareDetected(byte[] serialInfo) {
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.WAVE);

            emv.get().setTlv(0x100000, serialInfo);
            emvService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .start(emv.get(), 0, "", SearchMode.INTERNAL_MIFARE, "");
        }

        @Override
        public void onError(String message) {
            if (lastMessage != null && lastMessage.equals(message) && (System.currentTimeMillis() - lastTime < 2000)) {
                return;
            }
            lastMessage = message;
            lastTime = System.currentTimeMillis();

            if (messageCallback != null) {
                messageCallback.onSend(message);
            }
            if (errorCallback != null) {
                errorCallback.onError("", message);
            }
        }
    }

    private class ConfirmCallback implements IEmvService.ConfirmCallback {
        @Override
        public void onConfirm(
                @NonNull String pan,
                int issuerIconId,
                @NonNull String issuerName,
                @NonNull String cardHolderName,
                @Nullable List<EmvAidInfo> infoList,
                @Nullable String editAidPath,
                @Nullable String editAidParam,
                @Nullable String cardSequenceNumber,
                @Nullable String cardExpiryDate,
                @Nullable String applicationLabel,
                @Nullable int applicationTransactionCounter,
                @Nullable boolean isBankCard) {
            cardSignature = pan.length() > 6 ? pan.substring(0, 6) + "**********" + pan.substring(pan.length() - 4) : "";
        }
    }

    private class ErrorCallback implements IEmvService.ErrorCallback {
        @Override
        public void onError(@NonNull String title, String reason) {
            // Show error reason
            if (errorEventCallback != null) {
                errorEventCallback.onSend(new ErrorEvent()
                        .setTitle(title)
                        .setInfo(reason)
                        .setButtonContent("FINISH")
                        .setButtonClickListener(v -> ActivityStack.getInstance().popTo(AdminActivity.class)));
            }

            DialogUtils.dismiss();
            if (messageCallback != null) {
                messageCallback.onSend("EMV Process Failed.");
            }
        }
    }

    private class CompleteCallback implements IEmvService.CompleteCallback {
        @Override
        public void onComplete(boolean isContact, @NonNull TransResultEnum result,
                               @NonNull List<CvmType> cvmTypeList,
                               @NonNull List<EmvTag> emvTagList) {
            DialogUtils.dismiss();
            if (messageCallback != null) {
                messageCallback.onSend("EMV Process Complete.");
            }
            if(cardSignature == null || cardSignature.isEmpty()){
                if (errorCallback != null) {
                    errorCallback.onError("", "Card Read error.");
                    return;
                }
            }

            completeReaderCallback.onComplete(cardSignature);
        }
    }

    public interface MessageCallback {
        void onSend(String message);
    }

    public interface ErrorEventCallback {
        void onSend(ErrorEvent event);
    }

    public interface CardDetectCallback {
        void onDetect(byte searchMode);
    }

    public interface CompleteReaderCallback {
        void onComplete(String cardSignature);
    }
}
