<vector android:height="12dp" android:viewportHeight="170.66667"
    android:viewportWidth="641.3333" android:width="45dp"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="@color/issuer_icon" android:fillAlpha="0.6"
        android:pathData="M603.789,13.595 L639.839,85.268 564.04,157.029Z"/>
    <path android:fillColor="@color/issuer_icon"
        android:pathData="M578.82,13.595 L614.816,85.268 539.084,157.029Z"/>
    <path android:fillColor="@color/issuer_icon"
        android:pathData="M0.628,130.932 L36.591,1.235l57.497,-0c17.979,-0 29.947,2.832 36.083,8.712 6.05,5.813 7.209,15.321 3.561,28.671 -2.232,7.876 -5.557,14.505 -10.19,19.684 -4.571,5.233 -10.606,9.359 -18.037,12.413 6.291,1.531 10.296,4.513 12.084,9.064 1.826,4.503 1.604,11.098 -0.575,19.737L112.646,117.635 112.632,118.133c-1.295,5.064 -0.87,7.789 1.174,8.011l-1.309,4.788L73.596,130.932c0.121,-3.048 0.378,-5.779 0.614,-8.078 0.29,-2.334 0.647,-4.16 1.024,-5.402l3.581,-12.94c1.855,-6.749 1.942,-11.456 0.237,-14.147 -1.672,-2.779 -5.523,-4.127 -11.572,-4.127L51.148,86.239L38.673,130.932ZM59.227,56.993l17.491,-0c6.17,-0 10.668,-0.889 13.466,-2.696 2.778,-1.841 4.875,-4.924 6.117,-9.407 1.227,-4.532 0.923,-7.722 -0.87,-9.495 -1.787,-1.84 -6.151,-2.744 -12.954,-2.744L65.996,32.651l-6.769,24.342"/>
    <path android:fillColor="@color/issuer_icon"
        android:pathData="M240.455,35.207 L213.928,130.932l-32.192,-0l3.932,-14.012c-5.639,5.576 -11.451,9.804 -17.297,12.514 -5.799,2.759 -11.949,4.141 -18.395,4.141 -5.3,-0 -9.885,-1.005 -13.567,-2.913 -3.697,-1.928 -6.494,-4.841 -8.349,-8.658 -1.638,-3.378 -2.339,-7.537 -2.117,-12.461 0.309,-4.895 2.044,-13.128 5.219,-24.666l13.755,-49.67l35.213,-0L166.428,84.654c-1.995,7.248 -2.473,12.326 -1.517,15.07 1.02,2.812 3.764,4.262 8.161,4.262 4.416,-0 8.165,-1.619 11.233,-4.895 3.169,-3.256 5.543,-8.112 7.383,-14.572l13.601,-49.312l35.165,-0"/>
    <path android:fillColor="@color/issuer_icon"
        android:pathData="m227.671,130.932 l35.913,-129.698l49.428,-0c10.896,-0 19.347,0.633 25.333,2.029 6.049,1.363 10.736,3.58 14.229,6.683 4.368,4.072 7.059,9.069 8.185,15.117 1.019,6.001 0.425,13.042 -1.86,21.275 -4.055,14.505 -11.095,25.603 -21.135,33.372 -10.108,7.654 -22.535,11.505 -37.451,11.505l-23.129,-0l-10.977,39.716zM285.945,59.563l12.413,-0c8.059,-0 13.683,-1.019 17.007,-2.913 3.171,-1.977 5.475,-5.402 6.891,-10.311 1.377,-4.977 0.971,-8.436 -1.127,-10.383 -1.961,-1.957 -7.127,-2.913 -15.393,-2.913l-12.393,-0l-7.397,26.52"/>
    <path android:fillColor="@color/issuer_icon"
        android:pathData="m400.336,130.932 l0.357,-9.083c-5.672,4.261 -11.436,7.465 -17.196,9.494 -5.764,2.063 -11.915,3.083 -18.461,3.083 -10.007,-0 -16.94,-2.711 -21.019,-7.978 -3.917,-5.267 -4.565,-12.833 -1.875,-22.462 2.595,-9.529 7.248,-16.52 13.843,-21.018 6.664,-4.518 17.709,-7.755 33.239,-9.852 1.961,-0.29 4.585,-0.56 7.909,-0.957 11.469,-1.329 17.929,-4.411 19.279,-9.388 0.748,-2.744 0.269,-4.74 -1.397,-6.001 -1.637,-1.295 -4.672,-1.908 -9.103,-1.908 -3.667,-0 -6.595,0.749 -9.035,2.353 -2.401,1.599 -4.209,3.918 -5.436,7.19L357.093,64.405c3.12,-10.77 9.46,-18.935 18.955,-24.357 9.493,-5.537 21.955,-8.2 37.464,-8.2 7.263,-0 13.824,0.681 19.555,2.151 5.793,1.411 10.021,3.44 12.745,5.895 3.377,3.054 5.32,6.528 5.933,10.384 0.648,3.831 -0.068,9.34 -2.063,16.534L434.873,120.177c-0.479,1.74 -0.545,3.309 -0.289,4.72l1.956,3.358 -0.783,2.677zM408.947,88.201c-3.803,1.498 -8.644,2.932 -14.693,4.551 -9.495,2.522 -14.815,5.934 -16.008,10.122 -0.801,2.73 -0.44,4.827 0.889,6.393 1.276,1.483 3.541,2.271 6.749,2.271 5.828,-0 10.533,-1.483 14.061,-4.402 3.493,-2.962 6.121,-7.62 7.909,-14.027l0.816,-3.068 0.276,-1.841"/>
    <path android:fillColor="@color/issuer_icon"
        android:pathData="m435.98,168.654 l7.841,-28.329l10.108,-0c3.305,-0 5.963,-0.647 7.856,-1.874 1.909,-1.261 3.204,-3.392 3.953,-6.257l0.787,-4.087c0.101,-1.604 0.101,-3.291 0,-5.252l-5.387,-87.647l35.639,-0l-0.58,58.092 31.179,-58.092l33.136,-0L505.489,130.473c-6.257,10.669 -10.804,17.95 -13.704,21.955 -2.812,3.938 -5.507,7.021 -8.112,9.084 -3.411,2.88 -7.228,4.909 -11.268,6.083 -4.107,1.213 -10.397,1.826 -18.8,1.826 -2.387,-0 -5.199,-0.068 -8.165,-0.203 -2.985,-0.14 -6.204,-0.29 -9.46,-0.565"/>
</vector>
