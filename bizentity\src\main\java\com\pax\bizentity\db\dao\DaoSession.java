package com.pax.bizentity.db.dao;

import java.util.Map;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.AbstractDaoSession;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.identityscope.IdentityScopeType;
import org.greenrobot.greendao.internal.DaoConfig;

import com.pax.bizentity.entity.AcqIssuerRelation;
import com.pax.bizentity.entity.Acquirer;
import com.pax.bizentity.entity.CapkRevokeBean;
import com.pax.bizentity.entity.CardBin;
import com.pax.bizentity.entity.CardBinBlack;
import com.pax.bizentity.entity.CardRange;
import com.pax.bizentity.entity.ClssTornLog;
import com.pax.bizentity.entity.EmvAid;
import com.pax.bizentity.entity.EmvCapk;
import com.pax.bizentity.entity.Issuer;
import com.pax.bizentity.entity.TransData;
import com.pax.bizentity.entity.TransTotal;
import com.pax.bizentity.entity.clss.amex.AmexAidBean;
import com.pax.bizentity.entity.clss.amex.AmexDrlBean;
import com.pax.bizentity.entity.clss.dpas.DpasAidBean;
import com.pax.bizentity.entity.clss.eft.EFTAidBean;
import com.pax.bizentity.entity.clss.jcb.JcbAidBean;
import com.pax.bizentity.entity.clss.mir.MirAidBean;
import com.pax.bizentity.entity.clss.paypass.PayPassAidBean;
import com.pax.bizentity.entity.clss.paywave.PayWaveInterFloorLimitBean;
import com.pax.bizentity.entity.clss.paywave.PaywaveAidBean;
import com.pax.bizentity.entity.clss.paywave.PaywaveDrlBean;
import com.pax.bizentity.entity.clss.pboc.PBOCAidBean;
import com.pax.bizentity.entity.clss.pure.PureAidBean;
import com.pax.bizentity.entity.clss.rupay.RupayAidBean;

import com.pax.bizentity.db.dao.AcqIssuerRelationDao;
import com.pax.bizentity.db.dao.AcquirerDao;
import com.pax.bizentity.db.dao.CapkRevokeBeanDao;
import com.pax.bizentity.db.dao.CardBinDao;
import com.pax.bizentity.db.dao.CardBinBlackDao;
import com.pax.bizentity.db.dao.CardRangeDao;
import com.pax.bizentity.db.dao.ClssTornLogDao;
import com.pax.bizentity.db.dao.EmvAidDao;
import com.pax.bizentity.db.dao.EmvCapkDao;
import com.pax.bizentity.db.dao.IssuerDao;
import com.pax.bizentity.db.dao.TransDataDao;
import com.pax.bizentity.db.dao.TransTotalDao;
import com.pax.bizentity.db.dao.AmexAidBeanDao;
import com.pax.bizentity.db.dao.AmexDrlBeanDao;
import com.pax.bizentity.db.dao.DpasAidBeanDao;
import com.pax.bizentity.db.dao.EFTAidBeanDao;
import com.pax.bizentity.db.dao.JcbAidBeanDao;
import com.pax.bizentity.db.dao.MirAidBeanDao;
import com.pax.bizentity.db.dao.PayPassAidBeanDao;
import com.pax.bizentity.db.dao.PayWaveInterFloorLimitBeanDao;
import com.pax.bizentity.db.dao.PaywaveAidBeanDao;
import com.pax.bizentity.db.dao.PaywaveDrlBeanDao;
import com.pax.bizentity.db.dao.PBOCAidBeanDao;
import com.pax.bizentity.db.dao.PureAidBeanDao;
import com.pax.bizentity.db.dao.RupayAidBeanDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see org.greenrobot.greendao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig acqIssuerRelationDaoConfig;
    private final DaoConfig acquirerDaoConfig;
    private final DaoConfig capkRevokeBeanDaoConfig;
    private final DaoConfig cardBinDaoConfig;
    private final DaoConfig cardBinBlackDaoConfig;
    private final DaoConfig cardRangeDaoConfig;
    private final DaoConfig clssTornLogDaoConfig;
    private final DaoConfig emvAidDaoConfig;
    private final DaoConfig emvCapkDaoConfig;
    private final DaoConfig issuerDaoConfig;
    private final DaoConfig transDataDaoConfig;
    private final DaoConfig transTotalDaoConfig;
    private final DaoConfig amexAidBeanDaoConfig;
    private final DaoConfig amexDrlBeanDaoConfig;
    private final DaoConfig dpasAidBeanDaoConfig;
    private final DaoConfig eFTAidBeanDaoConfig;
    private final DaoConfig jcbAidBeanDaoConfig;
    private final DaoConfig mirAidBeanDaoConfig;
    private final DaoConfig payPassAidBeanDaoConfig;
    private final DaoConfig payWaveInterFloorLimitBeanDaoConfig;
    private final DaoConfig paywaveAidBeanDaoConfig;
    private final DaoConfig paywaveDrlBeanDaoConfig;
    private final DaoConfig pBOCAidBeanDaoConfig;
    private final DaoConfig pureAidBeanDaoConfig;
    private final DaoConfig rupayAidBeanDaoConfig;

    private final AcqIssuerRelationDao acqIssuerRelationDao;
    private final AcquirerDao acquirerDao;
    private final CapkRevokeBeanDao capkRevokeBeanDao;
    private final CardBinDao cardBinDao;
    private final CardBinBlackDao cardBinBlackDao;
    private final CardRangeDao cardRangeDao;
    private final ClssTornLogDao clssTornLogDao;
    private final EmvAidDao emvAidDao;
    private final EmvCapkDao emvCapkDao;
    private final IssuerDao issuerDao;
    private final TransDataDao transDataDao;
    private final TransTotalDao transTotalDao;
    private final AmexAidBeanDao amexAidBeanDao;
    private final AmexDrlBeanDao amexDrlBeanDao;
    private final DpasAidBeanDao dpasAidBeanDao;
    private final EFTAidBeanDao eFTAidBeanDao;
    private final JcbAidBeanDao jcbAidBeanDao;
    private final MirAidBeanDao mirAidBeanDao;
    private final PayPassAidBeanDao payPassAidBeanDao;
    private final PayWaveInterFloorLimitBeanDao payWaveInterFloorLimitBeanDao;
    private final PaywaveAidBeanDao paywaveAidBeanDao;
    private final PaywaveDrlBeanDao paywaveDrlBeanDao;
    private final PBOCAidBeanDao pBOCAidBeanDao;
    private final PureAidBeanDao pureAidBeanDao;
    private final RupayAidBeanDao rupayAidBeanDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        acqIssuerRelationDaoConfig = daoConfigMap.get(AcqIssuerRelationDao.class).clone();
        acqIssuerRelationDaoConfig.initIdentityScope(type);

        acquirerDaoConfig = daoConfigMap.get(AcquirerDao.class).clone();
        acquirerDaoConfig.initIdentityScope(type);

        capkRevokeBeanDaoConfig = daoConfigMap.get(CapkRevokeBeanDao.class).clone();
        capkRevokeBeanDaoConfig.initIdentityScope(type);

        cardBinDaoConfig = daoConfigMap.get(CardBinDao.class).clone();
        cardBinDaoConfig.initIdentityScope(type);

        cardBinBlackDaoConfig = daoConfigMap.get(CardBinBlackDao.class).clone();
        cardBinBlackDaoConfig.initIdentityScope(type);

        cardRangeDaoConfig = daoConfigMap.get(CardRangeDao.class).clone();
        cardRangeDaoConfig.initIdentityScope(type);

        clssTornLogDaoConfig = daoConfigMap.get(ClssTornLogDao.class).clone();
        clssTornLogDaoConfig.initIdentityScope(type);

        emvAidDaoConfig = daoConfigMap.get(EmvAidDao.class).clone();
        emvAidDaoConfig.initIdentityScope(type);

        emvCapkDaoConfig = daoConfigMap.get(EmvCapkDao.class).clone();
        emvCapkDaoConfig.initIdentityScope(type);

        issuerDaoConfig = daoConfigMap.get(IssuerDao.class).clone();
        issuerDaoConfig.initIdentityScope(type);

        transDataDaoConfig = daoConfigMap.get(TransDataDao.class).clone();
        transDataDaoConfig.initIdentityScope(type);

        transTotalDaoConfig = daoConfigMap.get(TransTotalDao.class).clone();
        transTotalDaoConfig.initIdentityScope(type);

        amexAidBeanDaoConfig = daoConfigMap.get(AmexAidBeanDao.class).clone();
        amexAidBeanDaoConfig.initIdentityScope(type);

        amexDrlBeanDaoConfig = daoConfigMap.get(AmexDrlBeanDao.class).clone();
        amexDrlBeanDaoConfig.initIdentityScope(type);

        dpasAidBeanDaoConfig = daoConfigMap.get(DpasAidBeanDao.class).clone();
        dpasAidBeanDaoConfig.initIdentityScope(type);

        eFTAidBeanDaoConfig = daoConfigMap.get(EFTAidBeanDao.class).clone();
        eFTAidBeanDaoConfig.initIdentityScope(type);

        jcbAidBeanDaoConfig = daoConfigMap.get(JcbAidBeanDao.class).clone();
        jcbAidBeanDaoConfig.initIdentityScope(type);

        mirAidBeanDaoConfig = daoConfigMap.get(MirAidBeanDao.class).clone();
        mirAidBeanDaoConfig.initIdentityScope(type);

        payPassAidBeanDaoConfig = daoConfigMap.get(PayPassAidBeanDao.class).clone();
        payPassAidBeanDaoConfig.initIdentityScope(type);

        payWaveInterFloorLimitBeanDaoConfig = daoConfigMap.get(PayWaveInterFloorLimitBeanDao.class).clone();
        payWaveInterFloorLimitBeanDaoConfig.initIdentityScope(type);

        paywaveAidBeanDaoConfig = daoConfigMap.get(PaywaveAidBeanDao.class).clone();
        paywaveAidBeanDaoConfig.initIdentityScope(type);

        paywaveDrlBeanDaoConfig = daoConfigMap.get(PaywaveDrlBeanDao.class).clone();
        paywaveDrlBeanDaoConfig.initIdentityScope(type);

        pBOCAidBeanDaoConfig = daoConfigMap.get(PBOCAidBeanDao.class).clone();
        pBOCAidBeanDaoConfig.initIdentityScope(type);

        pureAidBeanDaoConfig = daoConfigMap.get(PureAidBeanDao.class).clone();
        pureAidBeanDaoConfig.initIdentityScope(type);

        rupayAidBeanDaoConfig = daoConfigMap.get(RupayAidBeanDao.class).clone();
        rupayAidBeanDaoConfig.initIdentityScope(type);

        acqIssuerRelationDao = new AcqIssuerRelationDao(acqIssuerRelationDaoConfig, this);
        acquirerDao = new AcquirerDao(acquirerDaoConfig, this);
        capkRevokeBeanDao = new CapkRevokeBeanDao(capkRevokeBeanDaoConfig, this);
        cardBinDao = new CardBinDao(cardBinDaoConfig, this);
        cardBinBlackDao = new CardBinBlackDao(cardBinBlackDaoConfig, this);
        cardRangeDao = new CardRangeDao(cardRangeDaoConfig, this);
        clssTornLogDao = new ClssTornLogDao(clssTornLogDaoConfig, this);
        emvAidDao = new EmvAidDao(emvAidDaoConfig, this);
        emvCapkDao = new EmvCapkDao(emvCapkDaoConfig, this);
        issuerDao = new IssuerDao(issuerDaoConfig, this);
        transDataDao = new TransDataDao(transDataDaoConfig, this);
        transTotalDao = new TransTotalDao(transTotalDaoConfig, this);
        amexAidBeanDao = new AmexAidBeanDao(amexAidBeanDaoConfig, this);
        amexDrlBeanDao = new AmexDrlBeanDao(amexDrlBeanDaoConfig, this);
        dpasAidBeanDao = new DpasAidBeanDao(dpasAidBeanDaoConfig, this);
        eFTAidBeanDao = new EFTAidBeanDao(eFTAidBeanDaoConfig, this);
        jcbAidBeanDao = new JcbAidBeanDao(jcbAidBeanDaoConfig, this);
        mirAidBeanDao = new MirAidBeanDao(mirAidBeanDaoConfig, this);
        payPassAidBeanDao = new PayPassAidBeanDao(payPassAidBeanDaoConfig, this);
        payWaveInterFloorLimitBeanDao = new PayWaveInterFloorLimitBeanDao(payWaveInterFloorLimitBeanDaoConfig, this);
        paywaveAidBeanDao = new PaywaveAidBeanDao(paywaveAidBeanDaoConfig, this);
        paywaveDrlBeanDao = new PaywaveDrlBeanDao(paywaveDrlBeanDaoConfig, this);
        pBOCAidBeanDao = new PBOCAidBeanDao(pBOCAidBeanDaoConfig, this);
        pureAidBeanDao = new PureAidBeanDao(pureAidBeanDaoConfig, this);
        rupayAidBeanDao = new RupayAidBeanDao(rupayAidBeanDaoConfig, this);

        registerDao(AcqIssuerRelation.class, acqIssuerRelationDao);
        registerDao(Acquirer.class, acquirerDao);
        registerDao(CapkRevokeBean.class, capkRevokeBeanDao);
        registerDao(CardBin.class, cardBinDao);
        registerDao(CardBinBlack.class, cardBinBlackDao);
        registerDao(CardRange.class, cardRangeDao);
        registerDao(ClssTornLog.class, clssTornLogDao);
        registerDao(EmvAid.class, emvAidDao);
        registerDao(EmvCapk.class, emvCapkDao);
        registerDao(Issuer.class, issuerDao);
        registerDao(TransData.class, transDataDao);
        registerDao(TransTotal.class, transTotalDao);
        registerDao(AmexAidBean.class, amexAidBeanDao);
        registerDao(AmexDrlBean.class, amexDrlBeanDao);
        registerDao(DpasAidBean.class, dpasAidBeanDao);
        registerDao(EFTAidBean.class, eFTAidBeanDao);
        registerDao(JcbAidBean.class, jcbAidBeanDao);
        registerDao(MirAidBean.class, mirAidBeanDao);
        registerDao(PayPassAidBean.class, payPassAidBeanDao);
        registerDao(PayWaveInterFloorLimitBean.class, payWaveInterFloorLimitBeanDao);
        registerDao(PaywaveAidBean.class, paywaveAidBeanDao);
        registerDao(PaywaveDrlBean.class, paywaveDrlBeanDao);
        registerDao(PBOCAidBean.class, pBOCAidBeanDao);
        registerDao(PureAidBean.class, pureAidBeanDao);
        registerDao(RupayAidBean.class, rupayAidBeanDao);
    }
    
    public void clear() {
        acqIssuerRelationDaoConfig.clearIdentityScope();
        acquirerDaoConfig.clearIdentityScope();
        capkRevokeBeanDaoConfig.clearIdentityScope();
        cardBinDaoConfig.clearIdentityScope();
        cardBinBlackDaoConfig.clearIdentityScope();
        cardRangeDaoConfig.clearIdentityScope();
        clssTornLogDaoConfig.clearIdentityScope();
        emvAidDaoConfig.clearIdentityScope();
        emvCapkDaoConfig.clearIdentityScope();
        issuerDaoConfig.clearIdentityScope();
        transDataDaoConfig.clearIdentityScope();
        transTotalDaoConfig.clearIdentityScope();
        amexAidBeanDaoConfig.clearIdentityScope();
        amexDrlBeanDaoConfig.clearIdentityScope();
        dpasAidBeanDaoConfig.clearIdentityScope();
        eFTAidBeanDaoConfig.clearIdentityScope();
        jcbAidBeanDaoConfig.clearIdentityScope();
        mirAidBeanDaoConfig.clearIdentityScope();
        payPassAidBeanDaoConfig.clearIdentityScope();
        payWaveInterFloorLimitBeanDaoConfig.clearIdentityScope();
        paywaveAidBeanDaoConfig.clearIdentityScope();
        paywaveDrlBeanDaoConfig.clearIdentityScope();
        pBOCAidBeanDaoConfig.clearIdentityScope();
        pureAidBeanDaoConfig.clearIdentityScope();
        rupayAidBeanDaoConfig.clearIdentityScope();
    }

    public AcqIssuerRelationDao getAcqIssuerRelationDao() {
        return acqIssuerRelationDao;
    }

    public AcquirerDao getAcquirerDao() {
        return acquirerDao;
    }

    public CapkRevokeBeanDao getCapkRevokeBeanDao() {
        return capkRevokeBeanDao;
    }

    public CardBinDao getCardBinDao() {
        return cardBinDao;
    }

    public CardBinBlackDao getCardBinBlackDao() {
        return cardBinBlackDao;
    }

    public CardRangeDao getCardRangeDao() {
        return cardRangeDao;
    }

    public ClssTornLogDao getClssTornLogDao() {
        return clssTornLogDao;
    }

    public EmvAidDao getEmvAidDao() {
        return emvAidDao;
    }

    public EmvCapkDao getEmvCapkDao() {
        return emvCapkDao;
    }

    public IssuerDao getIssuerDao() {
        return issuerDao;
    }

    public TransDataDao getTransDataDao() {
        return transDataDao;
    }

    public TransTotalDao getTransTotalDao() {
        return transTotalDao;
    }

    public AmexAidBeanDao getAmexAidBeanDao() {
        return amexAidBeanDao;
    }

    public AmexDrlBeanDao getAmexDrlBeanDao() {
        return amexDrlBeanDao;
    }

    public DpasAidBeanDao getDpasAidBeanDao() {
        return dpasAidBeanDao;
    }

    public EFTAidBeanDao getEFTAidBeanDao() {
        return eFTAidBeanDao;
    }

    public JcbAidBeanDao getJcbAidBeanDao() {
        return jcbAidBeanDao;
    }

    public MirAidBeanDao getMirAidBeanDao() {
        return mirAidBeanDao;
    }

    public PayPassAidBeanDao getPayPassAidBeanDao() {
        return payPassAidBeanDao;
    }

    public PayWaveInterFloorLimitBeanDao getPayWaveInterFloorLimitBeanDao() {
        return payWaveInterFloorLimitBeanDao;
    }

    public PaywaveAidBeanDao getPaywaveAidBeanDao() {
        return paywaveAidBeanDao;
    }

    public PaywaveDrlBeanDao getPaywaveDrlBeanDao() {
        return paywaveDrlBeanDao;
    }

    public PBOCAidBeanDao getPBOCAidBeanDao() {
        return pBOCAidBeanDao;
    }

    public PureAidBeanDao getPureAidBeanDao() {
        return pureAidBeanDao;
    }

    public RupayAidBeanDao getRupayAidBeanDao() {
        return rupayAidBeanDao;
    }

}
