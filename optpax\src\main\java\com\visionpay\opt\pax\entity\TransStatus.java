/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/23                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.entity;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.pax.bizentity.entity.SearchMode;
import com.pax.commonui.clss.ClssLight;
import com.visionpay.opt.pax.R;
import java.util.List;

/**
 * Search card status
 *
 * 通过改变该类中字段的值，就可以实现更新 UI。
 */
public class TransStatus {
    private static final String TAG = "SearchCardStatus";

    private boolean isTitlePromptDisplay = true;
    private boolean isIccPromptDisplay = true;
    private boolean isPiccPromptDisplay = true;

    private byte allSupportedSearchMode = SearchMode.INSERT | SearchMode.WAVE;
    private byte currentSearchMode = SearchMode.INSERT | SearchMode.WAVE;
    private String iccStatusPrompt = "Running...";
    private String piccStatusPrompt = "Running...";
    private int light1Status = ClssLight.BLINK;
    private int light2Status = ClssLight.OFF;
    private int light3Status = ClssLight.OFF;
    private int light4Status = ClssLight.OFF;

    private String pan = "";
    private int issuerIconId = R.drawable.ic_issuer_other;
    private String issuerName = "Other";
    private String cardHolderName = "";
    private String enterMode = "Contact";
    private List<EmvAidInfo> aidInfoList;
    private String editAidPath;
    private String editAidParam;

    private boolean isContactService = true;
    private boolean isMagService = false;
    private String transResult = "";
    private List<CvmType> cvmTypeList;
    private List<EmvTag> emvTagList;

    private TransStatus() {
        // do nothing
    }

    public static TransStatus newInstance(byte allSupportedSearchMode) {
        TransStatus status = new TransStatus();
        status.allSupportedSearchMode = allSupportedSearchMode;
        status.currentSearchMode = allSupportedSearchMode;
        status.isIccPromptDisplay = SearchMode.isSupportIcc(allSupportedSearchMode);
        status.isPiccPromptDisplay = SearchMode.isWave(allSupportedSearchMode);
        return status;
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////

    public boolean isTitlePromptDisplay() {
        return isTitlePromptDisplay;
    }

    public boolean isIccPromptDisplay() {
        return isIccPromptDisplay;
    }

    public boolean isPiccPromptDisplay() {
        return isPiccPromptDisplay;
    }

    public int getLight1Status() {
        return light1Status;
    }

    public int getLight2Status() {
        return light2Status;
    }

    public int getLight3Status() {
        return light3Status;
    }

    public int getLight4Status() {
        return light4Status;
    }

    public byte getAllSupportedSearchMode() {
        return allSupportedSearchMode;
    }

    public byte getCurrentSearchMode() {
        return currentSearchMode;
    }

    public String getIccStatusPrompt() {
        return iccStatusPrompt;
    }

    public String getPiccStatusPrompt() {
        return piccStatusPrompt;
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////

    public int getIssuerIconId() {
        return issuerIconId;
    }

    @NonNull
    public String getIssuerName() {
        return issuerName;
    }

    @NonNull
    public String getPan() {
        return pan;
    }

    @NonNull
    public String getEnterMode() {
        return enterMode;
    }

    @NonNull
    public String getCardHolderName() {
        return cardHolderName;
    }

    @Nullable
    public List<EmvAidInfo> getAidInfoList() {
        return aidInfoList;
    }

    @Nullable
    public String getEditAidPath() {
        return editAidPath;
    }

    @Nullable
    public String getEditAidParam() {
        return editAidParam;
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////

    public boolean isContactService() {
        return isContactService;
    }

    public boolean isMagService() {
        return isMagService;
    }

    @NonNull
    public String getTransResult() {
        return transResult;
    }

    @Nullable
    public List<CvmType> getCvmTypeList() {
        return cvmTypeList;
    }

    @NonNull
    public List<EmvTag> getEmvTagList() {
        return emvTagList;
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////////

    public TransStatus setCurrentSearchMode(byte currentSearchMode) {
        this.currentSearchMode = currentSearchMode;
        return this;
    }

    public TransStatus stopSearchCard() {
        light1Status = ClssLight.OFF;
        light2Status = ClssLight.OFF;
        light3Status = ClssLight.OFF;
        light4Status = ClssLight.OFF;
        return this;
    }

    public TransStatus disableIcc() {
        if (SearchMode.isSupportIcc(currentSearchMode)) {
            currentSearchMode = (byte) (currentSearchMode ^ SearchMode.INSERT);
        }
        iccStatusPrompt = "Disabled";
        return this;
    }

    public TransStatus disablePicc() {
        if (SearchMode.isSupportInternalPicc(currentSearchMode)) {
            currentSearchMode = (byte) (currentSearchMode ^ SearchMode.INTERNAL_WAVE);
        }
        if (SearchMode.isSupportExternalPicc(currentSearchMode)) {
            currentSearchMode = (byte) (currentSearchMode ^ SearchMode.EXTERNAL_WAVE);
        }
        piccStatusPrompt = "Disabled";
        return this;
    }

    public TransStatus clssProcessing() {
        isTitlePromptDisplay = false;
        isPiccPromptDisplay = true;
        light1Status = ClssLight.OFF;
        light2Status = ClssLight.BLINK;
        light3Status = ClssLight.OFF;
        light4Status = ClssLight.OFF;
        iccStatusPrompt = "Stop, Contactless Processing";
        piccStatusPrompt = "Processing...";
        isContactService = false;
        return this;
    }

    public TransStatus clssDone() {
        isTitlePromptDisplay = false;
        isPiccPromptDisplay = true;
        light1Status = ClssLight.OFF;
        light2Status = ClssLight.OFF;
        light3Status = ClssLight.ON;
        light4Status = ClssLight.OFF;
        iccStatusPrompt = "Stop";
        piccStatusPrompt = "Done";
        return this;
    }

    public TransStatus insertProcessing() {
        isTitlePromptDisplay = false;
        isIccPromptDisplay = true;
        light1Status = ClssLight.OFF;
        light2Status = ClssLight.OFF;
        light3Status = ClssLight.OFF;
        light4Status = ClssLight.OFF;
        isContactService = true;
        return this;
    }

    public TransStatus insertMagProcessing() {
        isTitlePromptDisplay = false;
        isIccPromptDisplay = true;
        light1Status = ClssLight.OFF;
        light2Status = ClssLight.OFF;
        light3Status = ClssLight.OFF;
        light4Status = ClssLight.OFF;
        isContactService = false;
        isMagService = true;
        return this;
    }

    public TransStatus setTitlePromptDisplay(boolean titlePromptDisplay) {
        isTitlePromptDisplay = titlePromptDisplay;
        return this;
    }

    public TransStatus setIccStatusPrompt(@NonNull String iccStatusPrompt) {
        this.iccStatusPrompt = iccStatusPrompt;
        return this;
    }

    public TransStatus setPiccStatusPrompt(@NonNull String piccStatusPrompt) {
        this.piccStatusPrompt = piccStatusPrompt;
        return this;
    }

    public TransStatus setLight1Status(int light1Status) {
        this.light1Status = light1Status;
        return this;
    }

    public TransStatus setLight2Status(int light2Status) {
        this.light2Status = light2Status;
        return this;
    }

    public TransStatus setLight3Status(int light3Status) {
        this.light3Status = light3Status;
        return this;
    }

    public TransStatus setLight4Status(int light4Status) {
        this.light4Status = light4Status;
        return this;
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////

    public TransStatus setPan(@NonNull String pan) {
        this.pan = pan;
        return this;
    }

    public TransStatus setIssuerIconId(int issuerIconId) {
        this.issuerIconId = issuerIconId;
        return this;
    }

    public TransStatus setIssuerName(@NonNull String issuerName) {
        this.issuerName = issuerName;
        return this;
    }

    public TransStatus setCardHolderName(@NonNull String cardHolderName) {
        this.cardHolderName = cardHolderName;
        return this;
    }

    public TransStatus setEnterMode(@NonNull String enterMode) {
        this.enterMode = enterMode;
        return this;
    }

    public TransStatus setAidInfoList(@Nullable List<EmvAidInfo> aidInfoList) {
        this.aidInfoList = aidInfoList;
        return this;
    }

    public TransStatus setEditAidPath(String editAidPath) {
        this.editAidPath = editAidPath;
        return this;
    }

    public TransStatus setEditAidParam(String editAidParam) {
        this.editAidParam = editAidParam;
        return this;
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////

    public TransStatus setContactService(boolean contactService) {
        isContactService = contactService;
        return this;
    }

    public TransStatus setTransResult(@NonNull String transResult) {
        this.transResult = transResult;
        return this;
    }

    public TransStatus setCvmTypeList(@NonNull List<CvmType> cvmTypeList) {
        this.cvmTypeList = cvmTypeList;
        return this;
    }

    public TransStatus setEmvTagList(@NonNull List<EmvTag> emvTagList) {
        this.emvTagList = emvTagList;
        return this;
    }
}
