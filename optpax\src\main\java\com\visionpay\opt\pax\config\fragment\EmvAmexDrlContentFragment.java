/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/11                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;
import com.google.android.material.snackbar.Snackbar;
import com.visionpay.opt.pax.config.datastore.AmexDrlDataStore;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.bizentity.db.helper.AmexDrlDbHelper;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.AmountEditTextPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

/**
 * AMEX contactless DRL content page
 *
 * Settings -> Param -> AMEX Kernel Param -> Program ID
 */
@RouterService(interfaces = EmvParamBaseFragment.class, key = EmvRouterConst.CONFIG_PARAM_CLSS_AMEX_DRL)
public class EmvAmexDrlContentFragment extends EmvParamBaseFragment {
    private String drl;

    @Override
    public void setParam(String param) {
        drl = param;
    }

    @NonNull
    @Override
    public String getFragmentTitle() {
        return drl;
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new AmexDrlDataStore(AmexDrlDbHelper.getInstance().findDRL(drl))
                    .setRefreshCachedParamCallback(() -> Snackbar
                            .make(getListView(), "Need Refresh Cached EMV Param", Snackbar.LENGTH_LONG)
                            .setAction("REFRESH", v -> refreshCachedParam())
                            .show()));
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // Trans limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, AmexDrlDataStore.TRANS_LIMIT, R.string.config_param_trans_limit)
                    .build());

            // Floor limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, AmexDrlDataStore.FLOOR_LIMIT, R.string.config_param_floor_limit)
                    .build());

            // CVM limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, AmexDrlDataStore.CVM_LIMIT, R.string.config_param_cvm_limit)
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
