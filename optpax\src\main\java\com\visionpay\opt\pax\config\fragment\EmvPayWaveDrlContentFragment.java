/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/13                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;
import com.google.android.material.snackbar.Snackbar;
import com.visionpay.opt.pax.config.datastore.PayWaveDrlDataStore;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.bizentity.db.helper.PaywaveDrlDbHelper;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.AmountEditTextPrefBuilder;
import com.pax.preflib.builder.StringEditTextPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

/**
 * PayWave contactless DRL content page
 *
 * Settings -> Param -> PayWave Kernel Param -> Program ID
 */
@RouterService(interfaces = EmvParamBaseFragment.class, key = EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE_PROGRAM_ID)
public class EmvPayWaveDrlContentFragment extends EmvParamBaseFragment {
    private String programId;

    @Override
    public void setParam(String param) {
        programId = param;
    }

    @NonNull
    @Override
    public String getFragmentTitle() {
        return programId;
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new PayWaveDrlDataStore(PaywaveDrlDbHelper.getInstance().findDRL(programId))
                    .setRefreshCachedParamCallback(() -> Snackbar
                            .make(getListView(), "Need Refresh Cached EMV Param", Snackbar.LENGTH_LONG)
                            .setAction("REFRESH", v -> refreshCachedParam())
                            .show()));
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // Trans limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, PayWaveDrlDataStore.TRANS_LIMIT, R.string.config_param_trans_limit)
                    .build());

            // Floor limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, PayWaveDrlDataStore.FLOOR_LIMIT, R.string.config_param_floor_limit)
                    .build());

            // CVM limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, PayWaveDrlDataStore.CVM_LIMIT, R.string.config_param_cvm_limit)
                    .build());

            // TTQ
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PayWaveDrlDataStore.TTQ, R.string.config_param_ttq)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 8))
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
