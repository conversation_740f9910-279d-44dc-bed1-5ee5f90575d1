/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/28                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.pin;

import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.exceptions.EPedDevException;
import com.pax.emvbase.constant.EmvConstant;
import com.visionpay.opt.pax.app.App;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.exceptions.PinException;
import com.pax.emvservice.export.pin.IPinService;
import com.pax.emvservice.export.pin.PinInputCallback;
import com.sankuai.waimai.router.Router;

/**
 * Base PIN Task
 */
public abstract class BasePinTask implements IPinTask {
    private static final String TAG = "BasePinTask";

    protected IPinService pinService = Router.getService(IPinService.class, EmvServiceConstant.EMVSERVICE_PIN);

    protected int handleException(PinException e, PinCallback callback) {
        if (String.valueOf(EPedDevException.PED_ERR_INPUT_CANCEL.getErrCodeFromBasement()).equals(e.getErrCode())
                || String.valueOf(-306).equals(e.getErrCode())) {
            App.getApp().runOnUiThread(callback::onCancel);
            return EmvConstant.ContactCallbackStatus.USER_CANCEL;
        } else if (String.valueOf(EPedDevException.PED_ERR_INPUT_TIMEOUT.getErrCodeFromBasement()).equals(e.getErrCode())
                || String.valueOf(-315).equals(e.getErrCode())) {
            App.getApp().runOnUiThread(callback::onTimeout);
            return EmvConstant.ContactCallbackStatus.TIMEOUT;
        } else if (String.valueOf(-305).equals(e.getErrCode())) {
            // in p2pe, -305 means no password
            App.getApp().runOnUiThread(callback::onFinish);
            return EmvConstant.ContactCallbackStatus.NO_PASSWORD;
        } else if (String.valueOf(EPedDevException.PED_ERR_COMM.getErrCodeFromBasement()).equals(e.getErrCode())) {
            // External type ped communication error
            App.getApp().runOnUiThread(callback::onNoPinPad);
            return EmvConstant.ContactCallbackStatus.NO_PINPAD;
        } else {
            App.getApp().runOnUiThread(() -> callback.onError(e.getErrMsg()));
            return EmvConstant.ContactCallbackStatus.DENIAL;
        }
    }

    protected static class PinServiceInputListener implements PinInputCallback.NormalCallback {
        private int len = 0;
        private IPinTask.PinCallback callback;

        public PinServiceInputListener(IPinTask.PinCallback callback) {
            this.callback = callback;
        }

        @Override
        public void keyEvent(PinInputCallback.EKeyCode key) {
            if (key == PinInputCallback.EKeyCode.KEY_CLEAR) {
                len = 0;
            } else if (key != PinInputCallback.EKeyCode.KEY_ENTER
                    && key != PinInputCallback.EKeyCode.KEY_CANCEL) {
                len++;
            } else {
                len = 0;
                callback = null;
                return;
            }
            App.getApp().runOnUiThread(() -> {
                if (callback != null) {
                    callback.onInput(len);
                }
            });
        }
    }

    protected class PinServicePCIInputListener implements PinInputCallback.PCICallback {
        private int len = 0;
        private final IPinTask.PinCallback callback;

        public PinServicePCIInputListener(PinCallback callback) {
            this.callback = callback;
        }

        @Override
        public void keyEvent(PinInputCallback.EKeyCode key) {
            if (key == PinInputCallback.EKeyCode.KEY_CLEAR) {
                len = 0;
            } else if (key == PinInputCallback.EKeyCode.KEY_ENTER) {
                if (len > 3 || len == 0) {
                    pinService.setInputPinListener(null);
                    len = 0;
                    App.getApp().runOnUiThread(() -> {
                        if (callback != null) {
                            App.getApp().runOnUiThread(() -> {
                                try {
                                    callback.onFinish();
                                } catch (Exception e) {
                                    LogUtils.e(TAG, "Execute PIN Callback Failed", e);
                                }
                            });
                        }
                    });
                    return;
                }
            } else if (key == PinInputCallback.EKeyCode.KEY_CANCEL) {
                pinService.setInputPinListener(null);
                len = 0;
                if (callback != null) {
                    App.getApp().runOnUiThread(() -> {
                        try {
                            callback.onCancel();
                        } catch (Exception e) {
                            LogUtils.e(TAG, "Execute PIN Callback Failed", e);
                        }
                    });
                }
                return;
            } else {
                len++;
            }
            App.getApp().runOnUiThread(() -> {
                if (callback != null) {
                    callback.onInput(len);
                }
            });
        }
    }
}
