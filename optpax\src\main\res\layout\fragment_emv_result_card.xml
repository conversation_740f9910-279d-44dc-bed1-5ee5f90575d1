<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/trans_cvm_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/card_margin_horizontal"
        android:layout_marginEnd="@dimen/card_margin_horizontal"
        android:layout_marginTop="@dimen/card_spacing"
        app:cardElevation="0dp"
        app:cardCornerRadius="@dimen/card_corner_radius"
        app:strokeWidth="@dimen/card_stroke_width"
        app:strokeColor="@color/common_divider"
        app:cardBackgroundColor="@color/main_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/card_padding_vertical"
            android:paddingHorizontal="@dimen/card_padding_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/trans_cvm_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/cvm_title"
                android:textColor="@color/main_text"
                android:textSize="@dimen/card_title_size"
                android:fontFamily="sans-serif-medium" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/trans_cvm_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/list_margin_vertical" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/trans_emv_tag_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/card_margin_horizontal"
        android:layout_marginEnd="@dimen/card_margin_horizontal"
        android:layout_marginTop="@dimen/card_spacing"
        app:cardElevation="0dp"
        app:cardCornerRadius="@dimen/card_corner_radius"
        app:strokeWidth="@dimen/card_stroke_width"
        app:strokeColor="@color/common_divider"
        app:cardBackgroundColor="@color/main_background">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/card_padding_vertical"
            android:paddingHorizontal="@dimen/card_padding_horizontal">

            <TextView
                android:id="@+id/trans_result_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/trans_result_title"
                android:textColor="@color/main_text"
                android:textSize="@dimen/card_title_size"
                android:fontFamily="sans-serif-medium"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/trans_emv_tag_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/list_margin_vertical"
                android:overScrollMode="never"
                app:layout_constraintTop_toBottomOf="@id/trans_result_title" />

            <View
                android:id="@+id/trans_emv_tag_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_width"
                android:layout_marginTop="@dimen/list_margin_vertical"
                android:background="@color/common_divider"
                app:layout_constraintTop_toBottomOf="@id/trans_emv_tag_list"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/trans_emv_tag_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:text="@string/search"
                android:textColor="@color/main_highlight_button"
                android:minWidth="@dimen/text_button_min_width"
                app:icon="@drawable/ic_search"
                app:iconTint="@color/main_highlight_button"
                app:layout_constraintTop_toBottomOf="@id/trans_emv_tag_divider"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>
</LinearLayout>