package com.pax.emvlib.process.mifare;

import android.util.Log;

import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.IPicc;
import com.pax.dal.entity.EDetectMode;
import com.pax.dal.entity.EM1KeyType;
import com.pax.dal.entity.PiccCardInfo;
import com.pax.dal.exceptions.PiccDevException;
import com.pax.emvbase.BuildConfig;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.param.EmvProcessParam;
import com.pax.emvbase.param.EmvTransParam;
import com.pax.emvbase.process.EmvBase;
import com.pax.emvbase.process.entity.IssuerRspData;
import com.pax.emvbase.process.entity.TransResult;
import com.pax.emvbase.process.enums.CvmResultEnum;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvbase.process.mifare.IMifareCallback;
import com.pax.emvbase.utils.EmvDebugger;
import com.pax.jemv.clcommon.ACType;
import com.pax.jemv.clcommon.RetCode;
import com.pax.poslib.utils.PosDeviceUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class MifareProcess extends EmvBase {
    private static final String TAG = "MifareProcess";

    private static final boolean enableDebugLog = BuildConfig.DEBUG;

    private final List<EmvTag> emvTagList = new ArrayList<>();

    private IMifareCallback mifareStatusListener;

    private EmvTransParam emvTransParam;

    private MifareProcess() {
    }

    private static class Holder {
        private static final MifareProcess INSTANCE = new MifareProcess();
    }

    public static MifareProcess getInstance() {
        return Holder.INSTANCE;
    }

    public void registerMifareProcessListener(IMifareCallback mifareTransProcessListener) {
        mifareStatusListener = mifareTransProcessListener;
    }

    @Override
    public int preTransProcess(EmvProcessParam emvProcessParam) {
        this.emvTransParam = emvProcessParam.getEmvTransParam();
        return RetCode.EMV_OK;
    }

    @Override
    public TransResult startTransProcess() {
        LogUtils.i(TAG, "startTransProcess");

        IPicc picc = PosDeviceUtils.getInternalPicc();
        byte[] serialInfo = getTlv(0x100000);
        // read
        String str = "";
        byte[] value;
        try {
            picc.m1Auth(EM1KeyType.TYPE_A, (byte) emvTransParam.getBlockNum(), emvTransParam.getMifarePwd(), serialInfo);
            value = picc.m1Read((byte) emvTransParam.getBlockNum());
        } catch (PiccDevException e) {
            e.printStackTrace();
            return new TransResult(RetCode.EMV_DATA_ERR, TransResultEnum.RESULT_FALLBACK, CvmResultEnum.CVM_NO_CVM);
        }
        if (value != null) {
            str += ConvertUtils.bcd2Str(value);
        } else {
            return new TransResult(RetCode.EMV_NO_DATA, TransResultEnum.RESULT_FALLBACK, CvmResultEnum.CVM_NO_CVM);
        }

        setTlv(TagsTable.TRACK2, ConvertUtils.strToBcd(str, ConvertUtils.EPaddingPosition.PADDING_RIGHT));

        int ret = 0;
        if (mifareStatusListener != null){
            ret = mifareStatusListener.showConfirmCard();
            if (ret != RetCode.EMV_OK){//for example,timeout/data_error
                return new TransResult(ret, TransResultEnum.RESULT_OFFLINE_DENIED, CvmResultEnum.CVM_NO_CVM);
            }
        }

        TransResult transResult = new TransResult(RetCode.EMV_OK, TransResultEnum.RESULT_REQ_ONLINE, CvmResultEnum.CVM_NO_CVM);

        if (mifareStatusListener != null) {
            mifareStatusListener.onRemoveCard();
        }

        return transResult;
    }

    @Override
    public TransResult completeTransProcess(IssuerRspData issuerRspData) {
        TransResult completeTransResult = new TransResult(RetCode.EMV_OK, TransResultEnum.RESULT_ONLINE_CARD_DENIED, CvmResultEnum.CVM_NO_CVM);
        ACType acType = new ACType();
        if (issuerRspData.getAuthCode().length != 0) {
            LogUtils.i(TAG, "");
            setTlv(0x89, issuerRspData.getAuthCode());
            if (enableDebugLog) {
                LogUtils.i(TAG, "auth code(89):" + ConvertUtils.bcd2Str(issuerRspData.getAuthCode()));
            }
        }
        if (issuerRspData.getAuthData().length != 0) {
            setTlv(0x91, issuerRspData.getAuthData());
            if (enableDebugLog) {
                LogUtils.i(TAG, "auth data(91):" + ConvertUtils.bcd2Str(issuerRspData.getAuthData()));
            }
        }
        if (enableDebugLog) {
            LogUtils.i(TAG, "online result:" + issuerRspData.getOnlineResult());
            LogUtils.i(TAG, "issuer script:" + ConvertUtils.bcd2Str(issuerRspData.getScript()));
        }
        LogUtils.i(TAG, "completeTransProcess,acType:" + acType.type);
        if (acType.type == ACType.AC_TC) {
            completeTransResult.setTransResult(TransResultEnum.RESULT_ONLINE_APPROVED);
        } else if (acType.type == ACType.AC_AAC) {
            completeTransResult.setTransResult(TransResultEnum.RESULT_ONLINE_CARD_DENIED);
        }
        return completeTransResult;
    }

    @Override
    public byte[] getTlv(int tag) {
        byte[] value = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            Optional<EmvTag> emvTag = emvTagList.stream().filter(o -> o.getTag() == tag).findFirst();
            if (emvTag.isPresent()) {
                value = emvTag.get().getValue();
            }
        }

        EmvDebugger.d(TAG, "getTlv", tag, value);
        if (value == null) {
            return new byte[0];
        }
        return Arrays.copyOfRange(value, 0, value.length);
    }

    /**
     * Sets value on specific tag
     *
     * @param tag   emv tag
     * @param value tag value
     */
    @Override
    public void setTlv(int tag, byte[] value) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            Optional<EmvTag> emvTag = emvTagList.stream().filter(o -> o.getTag() == tag).findFirst();
            if (!emvTag.isPresent()) {
                emvTagList.add(new EmvTag(tag, value));
            }
            else {
                emvTag.get().setValue(value);
            }
        }else{
            emvTagList.add(new EmvTag(tag, value));
        }

        EmvDebugger.d(TAG, "setTlv", tag, value);
    }

    private class EmvTag {
        private int tag;
        private byte[] value;

        public EmvTag(int tag, byte[] value) {
            this.tag = tag;
            this.value = value;
        }

        public int getTag() {
            return tag;
        }

        public EmvTag setTag(int tag) {
            this.tag = tag;
            return this;
        }

        public byte[] getValue() {
            return value;
        }

        public EmvTag setValue(byte[] value) {
            this.value = value;
            return this;
        }
    }
}
