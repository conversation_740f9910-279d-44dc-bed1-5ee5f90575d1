/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/24                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.Window;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.ViewModelProvider;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ResourceUtil;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.viewmodel.SingleSelectionDialogViewModel;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/24
 */
public class SingleSelectionDialog extends DialogFragment {
    private SingleSelectionDialogViewModel viewModel;

    private String title;
    private final List<String> itemList = new LinkedList<>();
    private int checkedItem = -1;
    private DialogInterface.OnClickListener onItemClickListener;
    private String positiveButtonText;
    private Integer positiveButtonIconRes;
    private OnClickCallback positiveButtonClickListener;
    private String negativeButtonText;
    private Integer negativeButtonIconRes;
    private OnClickCallback negativeButtonClickListener;

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        viewModel = new ViewModelProvider(this).get(SingleSelectionDialogViewModel.class);
        if (!viewModel.isRestore()) {
            setViewModel();
        }
        viewModel.setRestore(false);

        AlertDialog.Builder builder = new AlertDialog.Builder(ActivityStack.getInstance().top())
                .setTitle(viewModel.getTitle())
                .setSingleChoiceItems(viewModel.getItemList().toArray(new String[0]),
                        viewModel.getCheckedItem(), viewModel.getOnItemClickListener())
                .setCancelable(false);

        if (viewModel.getPositiveButtonText() != null && !viewModel.getPositiveButtonText().isEmpty()) {
            builder.setPositiveButton(viewModel.getPositiveButtonText(), (dialog, which) -> {
                if (viewModel.getPositiveButtonClickListener() != null) {
                    viewModel.getPositiveButtonClickListener().onClick(dialog);
                }
            });
            if (viewModel.getPositiveButtonIconRes() != null) {
                builder.setPositiveButtonIcon(ResourceUtil.getDrawable(viewModel.getPositiveButtonIconRes()));
            }
        }

        if (viewModel.getNegativeButtonText() != null && !viewModel.getNegativeButtonText().isEmpty()) {
            builder.setNegativeButton(viewModel.getNegativeButtonText(), (dialog, which) -> {
                if (viewModel.getNegativeButtonClickListener() != null) {
                    viewModel.getNegativeButtonClickListener().onClick(dialog);
                }
            });
            if (viewModel.getNegativeButtonIconRes() != null) {
                builder.setNegativeButtonIcon(ResourceUtil.getDrawable(viewModel.getNegativeButtonIconRes()));
            }
        }

        Dialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(R.drawable.bg_dialog);
        }

        return dialog;
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        viewModel.setRestore(true);
    }

    private void setViewModel() {
        viewModel.setTitle(title);
        viewModel.setItemList(itemList);
        viewModel.setCheckedItem(checkedItem);
        viewModel.setOnItemClickListener(onItemClickListener);
        viewModel.setPositiveButtonText(positiveButtonText);
        viewModel.setPositiveButtonIconRes(positiveButtonIconRes);
        viewModel.setPositiveButtonClickListener(positiveButtonClickListener);
        viewModel.setNegativeButtonText(negativeButtonText);
        viewModel.setNegativeButtonIconRes(negativeButtonIconRes);
        viewModel.setNegativeButtonClickListener(negativeButtonClickListener);
    }

    public SingleSelectionDialog setTitle(@StringRes int titleId) {
        return setTitle(ResourceUtil.getString(titleId));
    }

    public SingleSelectionDialog setTitle(@Nullable String title) {
        this.title = title;
        return this;
    }

    public SingleSelectionDialog addChoice(@StringRes int itemStringId) {
        return addChoice(ResourceUtil.getString(itemStringId));
    }

    public SingleSelectionDialog addChoice(@NonNull String item) {
        return addChoice(item, false);
    }

    public SingleSelectionDialog addChoice(@NonNull String item, boolean isChecked) {
        itemList.add(item);
        if (isChecked && itemList.size() > 0) {
            checkedItem = Math.max(0, itemList.size() - 1);
        }
        return this;
    }

    public SingleSelectionDialog addChoices(
            @NonNull String[] items,
            int checkedItem,
            @Nullable DialogInterface.OnClickListener listener) {
        itemList.addAll(Arrays.asList(items));
        this.checkedItem = checkedItem;
        this.onItemClickListener = listener;
        return this;
    }

    public <T> SingleSelectionDialog addChoices(
            @NonNull T[] items,
            @NonNull ConvertCallback<T> converter,
            int checkedItem,
            @Nullable DialogInterface.OnClickListener listener) {
        return addChoices(Arrays.asList(items), converter, checkedItem, listener);
    }

    public <T> SingleSelectionDialog addChoices(
            @NonNull List<T> items,
            @NonNull ConvertCallback<T> converter,
            int checkedItem,
            @Nullable DialogInterface.OnClickListener listener) {
        for (T item : items) {
            if (item != null) {
                String convertResult = converter.onConvert(item);
                itemList.add(convertResult);
            }
        }
        this.checkedItem = checkedItem;
        this.onItemClickListener = listener;
        return this;
    }

    public SingleSelectionDialog setOnItemClickListener(DialogInterface.OnClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
        return this;
    }

    /**
     * Set positive button.
     *
     * Please execute set positive button before {@code show()} this dialog.
     * @param text Positive button text. If this param is {@code null} or is empty, this button
     * will not display on the dialog.
     * @param iconRes Positive button icon drawable resource. If this param is {@code null}, this
     * button will not display icon.
     * @param listener Positive button OnClickCallback. When user click positive button, this
     * callback will be executed.
     * @return This dialog.
     */
    public SingleSelectionDialog setPositiveButton(
            @Nullable String text,
            @Nullable @DrawableRes Integer iconRes,
            @Nullable OnClickCallback listener) {
        this.positiveButtonText = text;
        this.positiveButtonIconRes = iconRes;
        this.positiveButtonClickListener = listener;
        return this;
    }

    /**
     * Set negative button.
     *
     * Please execute set negative button before {@code show()} this dialog.
     * @param text Negative button text. If this param is {@code null} or is empty, this button
     * will not display on the dialog.
     * @param iconRes Negative button icon drawable resource. If this param is {@code null}, this
     * button will not display icon.
     * @param listener Negative button OnClickCallback. When user click negative button, this
     * callback will be executed.
     * @return This dialog.
     */
    public SingleSelectionDialog setNegativeButton(
            @Nullable String text,
            @Nullable @DrawableRes Integer iconRes,
            @Nullable OnClickCallback listener) {
        this.negativeButtonText = text;
        this.negativeButtonIconRes = iconRes;
        this.negativeButtonClickListener = listener;
        return this;
    }

    public void show() {
        Activity activity = ActivityStack.getInstance().top();
        if (activity instanceof FragmentActivity) {
            FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
            show(manager, null);
        }
    }

    public interface ConvertCallback<I> {
        @NonNull
        String onConvert(I input);
    }

    public interface OnClickCallback {
        void onClick(DialogInterface dialog);
    }
}
