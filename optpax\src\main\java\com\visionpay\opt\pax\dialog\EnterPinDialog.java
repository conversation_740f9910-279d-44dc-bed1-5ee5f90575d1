/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.dialog;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import com.google.android.material.textfield.TextInputEditText;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.callback.OnBindEditTextCallback;
import com.visionpay.opt.pax.viewmodel.EnterPinDialogViewModel;

/**
 * Enter PIN Dialog.
 */
public class EnterPinDialog extends BaseDialogFragment<EnterPinDialog> {
    private EnterPinDialogViewModel viewModel;

    private String title;
    private OnBindEditTextCallback onBindEditTextCallback;

    @Override
    protected int getContentLayoutRes() {
        return R.layout.dialog_enter_pin;
    }

    @Override
    protected void onCreateDialog(@NonNull Dialog dialog, @NonNull View view,
            @Nullable Window window, @Nullable Bundle savedInstanceState) {

        viewModel = new ViewModelProvider(this).get(EnterPinDialogViewModel.class);
        if (!viewModel.isRestore()) {
            setViewModel();
        } else {
            viewModel.setRestore(false);
        }

        TextView titleTextView = view.findViewById(R.id.enter_pin_title);
        if (viewModel.getTitle() != null && !viewModel.getTitle().isEmpty()) {
            titleTextView.setVisibility(View.VISIBLE);
            titleTextView.setText(viewModel.getTitle());
        } else {
            titleTextView.setVisibility(View.GONE);
        }

        TextInputEditText editText = view.findViewById(R.id.enter_pin_text_field);
        editText.requestFocus();
        if (viewModel.getOnBindEditTextCallback() != null) {
            viewModel.getOnBindEditTextCallback().onBind(editText);
        }
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        viewModel.setRestore(true);
    }

    private void setViewModel() {
        viewModel.setTitle(title);
        viewModel.setOnBindEditTextCallback(onBindEditTextCallback);
    }

    /**
     * Set dialog title.
     *
     * Please execute set dialog title before {@code show()} this dialog.
     * @param title Dialog title.
     * @return This dialog.
     */
    public EnterPinDialog setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * Set OnBindEditTextCallback.
     *
     * Please execute set OnBindEditTextCallback before {@code show()} this dialog.
     * @param onBindEditTextCallback OnBindEditTextCallback instance.
     * @return This dialog.
     */
    public EnterPinDialog setOnBindEditTextCallback(@Nullable OnBindEditTextCallback onBindEditTextCallback) {
        this.onBindEditTextCallback = onBindEditTextCallback;
        return this;
    }
}
