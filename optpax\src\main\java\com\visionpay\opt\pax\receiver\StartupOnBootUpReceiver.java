package com.visionpay.opt.pax.receiver;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.pax.commonlib.apppara.AppParaLoader;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.activity.SplashActivity;

import java.util.Locale;
public class StartupOnBootUpReceiver  extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {

        if(Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            Log.d(StartupOnBootUpReceiver.class.getSimpleName(), "ACTION_BOOT_COMPLETED");
            try {
                //IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
                //String type = configParamService.getString(ConfigKeyConstant.EDC_TYPE, "M");
                String configfile = AppParaLoader.getString("config.p");
                int index = configfile.indexOf(ConfigKeyConstant.EDC_TYPE);
                String type = "M";
                if(index > -1)
                    type = configfile.substring(index+9, index+10);
                Log.d(StartupOnBootUpReceiver.class.getSimpleName(), "Type: " + type);
                if (type.toUpperCase(Locale.ROOT).equals("M")) { //Main
                    Intent activityIntent = new Intent(context, SplashActivity.class);
                    activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(activityIntent);
                }
            }catch (Exception e){
                Log.e(StartupOnBootUpReceiver.class.getSimpleName(), e.getMessage());
            }
        }
    }
}
