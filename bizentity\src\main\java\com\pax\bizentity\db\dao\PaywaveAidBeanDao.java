package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.clss.paywave.PaywaveAidBean;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "paywave_aid".
*/
public class PaywaveAidBeanDao extends AbstractDao<PaywaveAidBean, Long> {

    public static final String TABLENAME = "paywave_aid";

    /**
     * Properties of entity PaywaveAidBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "paywaveAidId");
        public final static Property AppName = new Property(1, String.class, "appName", false, "APP_NAME");
        public final static Property Aid = new Property(2, String.class, "aid", false, "AID");
        public final static Property SelFlag = new Property(3, byte.class, "selFlag", false, "SEL_FLAG");
        public final static Property Version = new Property(4, String.class, "version", false, "VERSION");
        public final static Property TerminalType = new Property(5, String.class, "terminalType", false, "TERMINAL_TYPE");
        public final static Property Ttq = new Property(6, String.class, "ttq", false, "TTQ");
        public final static Property Crypto17Flag = new Property(7, byte.class, "crypto17Flag", false, "CRYPTO17_FLAG");
        public final static Property StatusCheckFlag = new Property(8, byte.class, "statusCheckFlag", false, "STATUS_CHECK_FLAG");
        public final static Property AmountZeroNoAllowed = new Property(9, byte.class, "amountZeroNoAllowed", false, "AMOUNT_ZERO_NO_ALLOWED");
        public final static Property SecurityCapability = new Property(10, String.class, "securityCapability", false, "SECURITY_CAPABILITY");
        public final static Property DomesticOnly = new Property(11, byte.class, "domesticOnly", false, "DOMESTIC_ONLY");
        public final static Property EnDDAVerNo = new Property(12, byte.class, "enDDAVerNo", false, "EN_DDAVER_NO");
    }

    private DaoSession daoSession;


    public PaywaveAidBeanDao(DaoConfig config) {
        super(config);
    }
    
    public PaywaveAidBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"paywave_aid\" (" + //
                "\"paywaveAidId\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"APP_NAME\" TEXT," + // 1: appName
                "\"AID\" TEXT," + // 2: aid
                "\"SEL_FLAG\" INTEGER NOT NULL ," + // 3: selFlag
                "\"VERSION\" TEXT," + // 4: version
                "\"TERMINAL_TYPE\" TEXT," + // 5: terminalType
                "\"TTQ\" TEXT," + // 6: ttq
                "\"CRYPTO17_FLAG\" INTEGER NOT NULL ," + // 7: crypto17Flag
                "\"STATUS_CHECK_FLAG\" INTEGER NOT NULL ," + // 8: statusCheckFlag
                "\"AMOUNT_ZERO_NO_ALLOWED\" INTEGER NOT NULL ," + // 9: amountZeroNoAllowed
                "\"SECURITY_CAPABILITY\" TEXT," + // 10: securityCapability
                "\"DOMESTIC_ONLY\" INTEGER NOT NULL ," + // 11: domesticOnly
                "\"EN_DDAVER_NO\" INTEGER NOT NULL );"); // 12: enDDAVerNo
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"paywave_aid\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, PaywaveAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(5, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(6, terminalType);
        }
 
        String ttq = entity.getTtq();
        if (ttq != null) {
            stmt.bindString(7, ttq);
        }
        stmt.bindLong(8, entity.getCrypto17Flag());
        stmt.bindLong(9, entity.getStatusCheckFlag());
        stmt.bindLong(10, entity.getAmountZeroNoAllowed());
 
        String securityCapability = entity.getSecurityCapability();
        if (securityCapability != null) {
            stmt.bindString(11, securityCapability);
        }
        stmt.bindLong(12, entity.getDomesticOnly());
        stmt.bindLong(13, entity.getEnDDAVerNo());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, PaywaveAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(5, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(6, terminalType);
        }
 
        String ttq = entity.getTtq();
        if (ttq != null) {
            stmt.bindString(7, ttq);
        }
        stmt.bindLong(8, entity.getCrypto17Flag());
        stmt.bindLong(9, entity.getStatusCheckFlag());
        stmt.bindLong(10, entity.getAmountZeroNoAllowed());
 
        String securityCapability = entity.getSecurityCapability();
        if (securityCapability != null) {
            stmt.bindString(11, securityCapability);
        }
        stmt.bindLong(12, entity.getDomesticOnly());
        stmt.bindLong(13, entity.getEnDDAVerNo());
    }

    @Override
    protected final void attachEntity(PaywaveAidBean entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public PaywaveAidBean readEntity(Cursor cursor, int offset) {
        PaywaveAidBean entity = new PaywaveAidBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // appName
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // aid
            (byte) cursor.getShort(offset + 3), // selFlag
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // version
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // terminalType
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // ttq
            (byte) cursor.getShort(offset + 7), // crypto17Flag
            (byte) cursor.getShort(offset + 8), // statusCheckFlag
            (byte) cursor.getShort(offset + 9), // amountZeroNoAllowed
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // securityCapability
            (byte) cursor.getShort(offset + 11), // domesticOnly
            (byte) cursor.getShort(offset + 12) // enDDAVerNo
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, PaywaveAidBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAppName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAid(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSelFlag((byte) cursor.getShort(offset + 3));
        entity.setVersion(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setTerminalType(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setTtq(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setCrypto17Flag((byte) cursor.getShort(offset + 7));
        entity.setStatusCheckFlag((byte) cursor.getShort(offset + 8));
        entity.setAmountZeroNoAllowed((byte) cursor.getShort(offset + 9));
        entity.setSecurityCapability(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setDomesticOnly((byte) cursor.getShort(offset + 11));
        entity.setEnDDAVerNo((byte) cursor.getShort(offset + 12));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(PaywaveAidBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(PaywaveAidBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(PaywaveAidBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
