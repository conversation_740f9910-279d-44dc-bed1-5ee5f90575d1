/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/22                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;
import com.pax.bizentity.entity.SearchMode;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.entity.TransStatus;

/**
 * Transaction Search Card Prompt Fragment ViewModel.
 */
public class TransPromptViewModel extends BaseTransCardViewModel {
    private static final String TAG = "TransPromptViewModel";

    private final MutableLiveData<TransStatus> statusLiveData = new MutableLiveData<>();

    @Override
    public void setStatus(TransStatus status) {
        LogUtils.d(TAG, "Update status");
        statusLiveData.postValue(status);
    }

    public LiveData<TransStatus> getStatusLiveData() {
        return statusLiveData;
    }

    public LiveData<Boolean> listenPromptTextDisplay() {
        return Transformations.map(getStatusLiveData(), TransStatus::isTitlePromptDisplay);
    }

    public LiveData<String> listenPromptText() {
        return Transformations.map(getStatusLiveData(), status -> {
            byte mode = status.getCurrentSearchMode();
            boolean mag = SearchMode.isSupportMag(mode);
            boolean icc = SearchMode.isSupportIcc(mode);
            boolean picc = SearchMode.isWave(mode);
            String prompt = "";
            if (mag && icc && picc) {
                prompt = ResourceUtil.getString(R.string.prompt_insert_swipe_wave_card);
            } else if (mag && icc) {
                prompt = ResourceUtil.getString(R.string.prompt_insert_swipe_card);
            } else if (icc && picc) {
                prompt = ResourceUtil.getString(R.string.prompt_insert_wave_card);
            } else if (mag && picc) {
                prompt = ResourceUtil.getString(R.string.prompt_swipe_wave_card);
            } else if (mag) {
                prompt = ResourceUtil.getString(R.string.prompt_swipe_card);
            } else if (icc) {
                prompt = ResourceUtil.getString(R.string.prompt_insert_card);
            } else if (picc) {
                prompt = ResourceUtil.getString(R.string.prompt_wave_card);
            }
            return prompt;
        });
    }

    public LiveData<Integer> listenLight1Status() {
        return Transformations.distinctUntilChanged(Transformations.map(getStatusLiveData(),
                TransStatus::getLight1Status));
    }

    public LiveData<Integer> listenLight2Status() {
        return Transformations.distinctUntilChanged(Transformations.map(getStatusLiveData(),
                TransStatus::getLight2Status));
    }

    public LiveData<Integer> listenLight3Status() {
        return Transformations.distinctUntilChanged(Transformations.map(getStatusLiveData(),
                TransStatus::getLight3Status));
    }

    public LiveData<Integer> listenLight4Status() {
        return Transformations.distinctUntilChanged(Transformations.map(getStatusLiveData(),
                TransStatus::getLight4Status));
    }

    public LiveData<Boolean> listenInsertDisplay() {
        return Transformations.map(getStatusLiveData(), TransStatus::isIccPromptDisplay);
    }

    public LiveData<Boolean> listenInsertIconEnabled() {
        return Transformations.map(getStatusLiveData(), status -> SearchMode.isSupportIcc(status.getCurrentSearchMode()));
    }

    public LiveData<String> listenInsertInfo() {
        return Transformations.map(getStatusLiveData(), TransStatus::getIccStatusPrompt);
    }

    public LiveData<Boolean> listenTapDisplay() {
        return Transformations.map(getStatusLiveData(), TransStatus::isPiccPromptDisplay);
    }

    public LiveData<Boolean> listenTapIconEnabled() {
        return Transformations.map(getStatusLiveData(), status -> SearchMode.isWave(status.getCurrentSearchMode()));
    }

    public LiveData<String> listenTapInfo() {
        return Transformations.map(getStatusLiveData(), TransStatus::getPiccStatusPrompt);
    }
}
