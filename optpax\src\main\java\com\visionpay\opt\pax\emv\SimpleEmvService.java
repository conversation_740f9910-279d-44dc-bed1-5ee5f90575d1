package com.visionpay.opt.pax.emv;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.pax.bizentity.entity.Issuer;
import com.pax.bizentity.entity.SearchMode;
import com.pax.bizlib.card.PanUtils;
import com.pax.bizlib.card.TrackUtils;
import com.pax.bizlib.trans.AcqManager;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.process.EmvBase;
import com.pax.emvbase.process.contact.IContactCallback;
import com.pax.emvbase.process.contactless.IContactlessCallback;
import com.pax.emvbase.process.entity.TransResult;
import com.pax.emvbase.process.enums.CvmResultEnum;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvbase.process.magnetic.IMagCallback;
import com.pax.emvbase.process.mifare.IMifareCallback;
import com.pax.emvlib.process.contact.EmvProcess;
import com.pax.emvlib.process.contactless.ClssProcess;
import com.pax.emvlib.process.magnetic.MagProcess;
import com.pax.emvlib.process.mifare.MifareProcess;
import com.pax.jemv.clcommon.RetCode;
public class SimpleEmvService implements ISimpleEmvService{
    private static final String TAG = "SimpleEmvService";

    private TransResult transResult;
    private String cachedTrack2Data = null;
    private boolean userCancel;
    private boolean timeOut;

    private int detectResult;
    private byte[] pin = new byte[0];

    /**
     * user cancel during contact process
     *
     * @param userCancel userCancel
     */
    @Override
    public void setUserCancel(boolean userCancel) {
        this.userCancel = userCancel;
    }

    /**
     * application timeout,and finish the emv process
     *
     * @param isTimeOut isTimeOut
     */
    @Override
    public void timeOut(boolean isTimeOut) {
        this.timeOut = isTimeOut;
    }
    @Override
    public int getKernelType() {
        return ClssProcess.getInstance().getKernType().kernType;
    }

    @Override
    public void setTrackData(String trackData1, String trackData2, String trackData3) {
        MagProcess.getInstance().setTrackData(trackData1, trackData2, trackData3);
    }

    /**
     * start contact process,need handle timeout situation
     *
     * @param contactCallback contactCallback
     * @return result
     */
    @Override
    public int startTransProcess(int detectResult, ISimpleContactCallback contactCallback) {
        EmvBase process = null;
        try{
            //reset every transaction
            cachedTrack2Data = null;
            timeOut(false);
            setUserCancel(false);
            this.detectResult = detectResult;
            if(detectResult == SearchMode.INSERT) {
                process = EmvProcess.getInstance();
                ((EmvProcess) process).registerEmvProcessListener(contactCallback);
            }
            else if(detectResult == SearchMode.SWIPE){
                process = MagProcess.getInstance();
                ((MagProcess) process).registerMagProcessListener(contactCallback);
            }
            else if(detectResult == SearchMode.WAVE){
                process = ClssProcess.getInstance();
                ((ClssProcess) process).registerClssProcessListener(contactCallback);
            }
            else if(detectResult == SearchMode.INTERNAL_MIFARE){
                process = MifareProcess.getInstance();
                ((MifareProcess) process).registerMifareProcessListener(contactCallback);
            }

            transResult = process.startTransProcess();
            int resultCode = transResult.getResultCode();
            if(resultCode != RetCode.EMV_OK){
                return resultCode;
            }

            if(detectResult == SearchMode.WAVE) {
                int ret = contactCallback.confirmCard();
                LogUtils.d("Contactless", "confirm card: " + ret);
                if (ret != RetCode.EMV_OK) {//for example,timeout/data_error
                    transResult = new TransResult(ret, TransResultEnum.RESULT_OFFLINE_DENIED, CvmResultEnum.CVM_NO_CVM);
                    return ret;
                }
            }

            return 0;
        }finally {
            if(process != null) {
                if (detectResult == SearchMode.INSERT)
                    ((EmvProcess) process).registerEmvProcessListener(null);
                else if (detectResult == SearchMode.SWIPE)
                    ((MagProcess) process).registerMagProcessListener(null);
                else if (detectResult == SearchMode.WAVE)
                    ((ClssProcess) process).registerClssProcessListener(null);
                else if (detectResult == SearchMode.INTERNAL_MIFARE)
                    ((MifareProcess) process).registerMifareProcessListener(null);
            }
        }
    }

    /**
     * Gets pan, the result is ciphertext in p2pe mode
     *
     * @return pan
     */
    @NonNull
    @Override
    public String getPan() {
        if (!TextUtils.isEmpty(cachedTrack2Data)){
            return TrackUtils.getPan(cachedTrack2Data);
        }
        String track2Data = getTrack2Data();
        if (!TextUtils.isEmpty(track2Data)){
            return TrackUtils.getPan(track2Data);
        }
        //some cards don't have track2 data
        byte[] panBytes= getTlv(TagsTable.PAN);
        String pan = ConvertUtils.bcd2Str(panBytes, panBytes.length);
        int indexF = pan.indexOf('F');
        return pan.substring(0, indexF != -1 ? indexF : pan.length());
    }

    /**
     * Gets pan block
     *
     * @return pan block
     */
    @NonNull
    @Override
    public String getPanBlock() {
        return PanUtils.getPanBlock(getPan(), PanUtils.X9_8_WITH_PAN);
    }

    /**
     * Gets masked pan
     *
     * @param pattern masked pattern
     * @return masked pan
     */
    @Override
    public String getMaskedPan(String pattern) {
        return PanUtils.maskCardNo(getPan(),pattern);
    }

    /**
     * Gets expire Date
     *
     * @return expire Date
     */
    @Override
    public String getExpireDate() {
        byte[] expireDate = getTlv(TagsTable.EXPIRE_DATE);
        if (expireDate !=null && expireDate.length >0){
            String tmp = ConvertUtils.bcd2Str(expireDate);
            return tmp.substring(0,4);
        }

        String track2Data = getTrack2Data();
        if (!TextUtils.isEmpty(track2Data)){
            return TrackUtils.getExpDate(track2Data);
        }

        return "";
    }

    /**
     * Gets cardholder name
     *
     * @return cardholder name
     */
    @Override
    public String getCardholderName() {
        byte[] cardholderName = getTlv(TagsTable.CARDHOLDER_NAME);
        return ConvertUtils.bcd2Str(cardholderName);
    }

    /**
     * Gets Issuer by pan(use maskedPan in p2pe mode)
     *
     * @return Issuer
     */
    @Override
    public Issuer getMatchedIssuerByPan() {
        return AcqManager.getInstance().findIssuerByPan(getPan());
    }

    /**
     * Gets track2 data
     *
     * @return track2 data,the result is ciphertext in p2pe mode
     */
    @Override
    public String getTrack2Data() {
        cachedTrack2Data = TrackUtils.getTrack2FromTag57(getTlv(TagsTable.TRACK2));
        return cachedTrack2Data;
    }

    @Override
    public void setPin(byte[] pin) {
        this.pin = pin;
    }

    @Override
    public byte[] getPin() {
        return pin;
    }

    @Override
    public byte[] getTlv(int tag) {
        if(detectResult == SearchMode.INSERT)
            return EmvProcess.getInstance().getTlv(tag);
        else if(detectResult == SearchMode.SWIPE)
            return MagProcess.getInstance().getTlv(tag);
        else if(detectResult == SearchMode.WAVE)
            return ClssProcess.getInstance().getTlv(tag);
        else //if(detectResult == SearchMode.INTERNAL_MIFARE)
            return MifareProcess.getInstance().getTlv(tag);
    }

    @Override
    public void setTlv(int tag, byte[] value) {
        if(detectResult == SearchMode.INSERT)
            EmvProcess.getInstance().setTlv(tag, value);
        else if(detectResult == SearchMode.SWIPE)
            MagProcess.getInstance().setTlv(tag, value);
        else if(detectResult == SearchMode.WAVE)
            ClssProcess.getInstance().setTlv(tag, value);
        else //if(detectResult == SearchMode.INTERNAL_MIFARE)
            MifareProcess.getInstance().setTlv(tag, value);
    }

    public interface ISimpleContactCallback extends IContactCallback, IContactlessCallback, IMagCallback, IMifareCallback {

    }
}
