<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/02/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<resources>
    <string name="app_name" translatable="false">VisionPay</string>

    <string name="num_1" translatable="false">1</string>
    <string name="num_2" translatable="false">2</string>
    <string name="num_3" translatable="false">3</string>
    <string name="num_4" translatable="false">4</string>
    <string name="num_5" translatable="false">5</string>
    <string name="num_6" translatable="false">6</string>
    <string name="num_7" translatable="false">7</string>
    <string name="num_8" translatable="false">8</string>
    <string name="num_9" translatable="false">9</string>
    <string name="num_0" translatable="false">0</string>
    <string name="num_00" translatable="false">00</string>
    <string name="num_000" translatable="false">000</string>
    <string name="key_clear" translatable="false">C</string>
    <string name="key_delete" translatable="false">⌫</string>
    <string name="key_ok" translatable="false">OK</string>

    <string name="trans_type" translatable="false">Transaction Type</string>
    <string name="trans_sale">Sale</string>
    <string name="trans_refund">Refund</string>
    <string name="trans_preAuth">Pre-Auth</string>

    <string name="emv_process_title" translatable="false">EMV Process</string>
    <string name="amount" translatable="false">Amount</string>
    <string name="ok" translatable="false">OK</string>
    <string name="search" translatable="false">Search</string>
    <string name="edit_aid" translatable="false">Edit AID</string>
    <string name="swipe_card_title" translatable="false">Swipe Mag Card</string>
    <string name="insert_card_title" translatable="false">Insert EMV Card</string>
    <string name="tap_card_title" translatable="false">Tap Contactless Card</string>
    <string name="search_card_info_running" translatable="false">Running…</string>
    <string name="search_card_info_disabled" translatable="false">Disabled</string>
    <string name="search_card_info_fallback" translatable="false">Fallback</string>
    <string name="search_card_info_device_not_support" translatable="false">Disabled, because this device not
        support</string>

    <string name="cardholder_name" translatable="false">Cardholder Name:</string>

    <string name="online_result" translatable="false">Online Result</string>
    <string name="online_approved" translatable="false">Online Approved</string>
    <string name="online_denied" translatable="false">Online Denied</string>
    <string name="online_failed" translatable="false">Online Failed</string>
    <string name="online_rsp_code" translatable="false">Response Code</string>
    <string name="online_rsp_icc_data" translatable="false">Response ICC Data</string>

    <string name="dialog_next" translatable="false">Next</string>
    <string name="dialog_clear" translatable="false">Clear</string>
    <string name="dialog_done" translatable="false">Done</string>

    <string name="cvm_title" translatable="false">CVM Type</string>
    <string name="cvm_online_pin" translatable="false">Online PIN</string>
    <string name="cvm_offline_pin" translatable="false">Offline PIN</string>
    <string name="cvm_signature" translatable="false">Signature</string>

    <string name="enter_online_pin" translatable="false">Enter PIN</string>
    <string name="enter_offline_pin" translatable="false">Enter PIN (%1$s times remain)</string>

    <string name="remove_card" translatable="false">Please Remove Card</string>

    <string name="trans_result_title" translatable="false">Transaction Result</string>

    <string name="prompt_insert_swipe_wave_card" translatable="false">Please Swipe/Insert/Wave Card</string>
    <string name="prompt_swipe_card" translatable="false">Please Swipe Card</string>
    <string name="prompt_wave_card" translatable="false">Please Wave Card</string>
    <string name="prompt_insert_card" translatable="false">Please Insert Card</string>
    <string name="prompt_insert_swipe_card" translatable="false">Please Swipe/Insert Card</string>
    <string name="prompt_insert_wave_card" translatable="false">Please Insert/Wave Card</string>
    <string name="prompt_swipe_wave_card" translatable="false">Please Swipe/Wave Card</string>
    <string name="prompt_see_phone_wave_card" translatable="false">Please See Phone, And Wave Card Again</string>
    <string name="prompt_please_retry" translatable="false">Read Card Failed, Please Retry</string>
    <string name="prompt_ic_card_input" translatable="false">The Card Is IC Card, Please Insert Card!</string>
    <string name="prompt_card_num" translatable="false">Card No.</string>
    <string name="prompt_card_date_err" translatable="false">Date Format Error, Please Check!</string>
    <string name="prompt_card_date_empty" translatable="false">Please enter the card expiration date</string>
    <string name="prompt_card_num_err" translatable="false">Card Number Error, Please Check!</string>
    <string name="prompt_card_num_manual" translatable="false">Or Enter Card Number Manually</string>
    <string name="prompt_date_default" translatable="false">MM/YY</string>
    <string name="prompt_confirm_card_info" translatable="false">Please Confirm Card Information</string>

    <string name="history" translatable="false">History</string>

    <string name="settings" translatable="false">Settings</string>

    <string name="search_card_title" translatable="false">Please Insert or Tap Card</string>
    <string name="search_card_insert" translatable="false">Insert</string>
    <string name="search_card_tap" translatable="false">Tap</string>
    <string name="valid_date" translatable="false">VALID DATE</string>
    <string name="issuer_icon" translatable="false">Issuer icon</string>
    <string name="confirm" translatable="false">confirm</string>

    <string name="search_card_retry" translatable="false">Retry</string>
    <string name="content_progress_icon" translatable="false">Progress Icon</string>

    <string name="config_merchant_name" translatable="false">Merchant Name</string>
    <string name="config_merchant_address" translatable="false">Merchant Address</string>
    <string name="config_currency" translatable="false">Currency</string>
    <string name="config_solve_clss_conflict" translatable="false">Solve Contactless Conflict</string>
    <string name="config_support_clss_lights" translatable="false">Support Physical Contactless Lights</string>
    <string name="config_param_contact" translatable="false">Contact</string>
    <string name="config_param_contact_emv" translatable="false">EMV Application</string>
    <string name="config_param_contactless" translatable="false">Contactless</string>
    <string name="config_param_contactless_amex" translatable="false">AMEX Kernel Param</string>
    <string name="config_param_contactless_dpas" translatable="false">DPAS Kernel Param</string>
    <string name="config_param_contactless_eft" translatable="false">EFT Kernel Param</string>
    <string name="config_param_contactless_jcb" translatable="false">JCB Kernel Param</string>
    <string name="config_param_contactless_mc" translatable="false">MC Kernel Param</string>
    <string name="config_param_contactless_mir" translatable="false">MIR Kernel Param</string>
    <string name="config_param_contactless_paywave" translatable="false">PayWave Kernel Param</string>
    <string name="config_param_contactless_pboc" translatable="false">PBOC Kernel Param</string>
    <string name="config_param_contactless_pure" translatable="false">Pure Kernel Param</string>
    <string name="config_param_contactless_rupay" translatable="false">RuPay Kernel Param</string>
    <string name="config_param_program_id" translatable="false">Program ID</string>
    <string name="config_param_aid" translatable="false">AID</string>
    <string name="config_param_app_name" translatable="false">APP Name</string>
    <string name="config_param_sel_flag" translatable="false">Support partial name selection</string>
    <string name="config_param_priority" translatable="false">Priority</string>
    <string name="config_param_online_pin" translatable="false">Online PIN</string>
    <string name="config_param_target_percent" translatable="false">Target percent</string>
    <string name="config_param_max_target_percent" translatable="false">Max target percent</string>
    <string name="config_param_check_floor_limit" translatable="false">Check floor limit</string>
    <string name="config_param_floor_limit" translatable="false">Floor limit</string>
    <string name="config_param_trans_limit" translatable="false">Trans limit</string>
    <string name="config_param_qps_limit" translatable="false">QPS limit</string>
    <string name="config_param_cvm_limit" translatable="false">CVM limit</string>
    <string name="config_param_tac_default" translatable="false">TAC Default</string>
    <string name="config_param_tac_denial" translatable="false">TAC Denial</string>
    <string name="config_param_tac_online" translatable="false">TAC Online</string>
    <string name="config_param_ddol" translatable="false">DDOL</string>
    <string name="config_param_tdol" translatable="false">TDOL</string>
    <string name="config_param_term_cap" translatable="false">Terminal Capability</string>
    <string name="config_param_term_cap_short" translatable="false">Term Cap</string>
    <string name="config_param_term_add_cap" translatable="false">Terminal Additional Capability</string>
    <string name="config_param_term_add_cap_short" translatable="false">Term Add Cap</string>
    <string name="config_param_term_trans_cap" translatable="false">Terminal Transaction Capability</string>
    <string name="config_param_term_trans_cap_short" translatable="false">Term Trans Cap</string>
    <string name="config_param_ttq" translatable="false">TTQ</string>
    <string name="config_param_term_tpm_cap" translatable="false">Terminal TPM Capability</string>
    <string name="config_param_term_interchange" translatable="false">Terminal Interchange Profile</string>
    <string name="config_param_term_interchange_short" translatable="false">Term Interchange</string>
    <string name="config_param_risk_manage" translatable="false">Risk Management Data</string>
    <string name="config_param_kernel_config" translatable="false">Kernel Config</string>
    <string name="config_param_cvm_cap_cvm" translatable="false">CVM Capability - CVM Required</string>
    <string name="config_param_cvm_cap_cvm_short" translatable="false">CVM Cap - CVM</string>
    <string name="config_param_cvm_cap_no_cvm" translatable="false">CVM Capability - No CVM Required</string>
    <string name="config_param_cvm_cap_no_cvm_short" translatable="false">CVM Cap - No CVM</string>
    <string name="config_param_sec_cap" translatable="false">Security Capability</string>
    <string name="config_param_sec_cap_short" translatable="false">Security Cap</string>
    <string name="config_param_mag_cvm_cap_cvm" translatable="false">Mag-stripe CVM Capability – CVM Required</string>
    <string name="config_param_mag_cvm_cap_cvm_short" translatable="false">MAG CVM Cap - CVM</string>
    <string name="config_param_mag_cvm_cap_no_cvm" translatable="false">Mag-stripe CVM Capability – No CVM
        Required</string>
    <string name="config_param_mag_cvm_cap_no_cvm_short" translatable="false">MAG CVM Cap - No CVM</string>
    <string name="config_param_clss_kernel_cap" translatable="false">Contactless Kernel Capability</string>
    <string name="config_param_clss_kernel_cap_short" translatable="false">Clss Kernel Cap</string>
    <string name="config_param_trans_type" translatable="false">Trans Type: %1$s</string>
    <string name="config_about_kernel_version" translatable="false">EMV Kernel Version</string>

    <string name="enter_pin" translatable="false">Enter PIN:</string>
    <string name="choose_application" translatable="false">Choose your application:</string>
    <string name="payment_successful" translatable="false">Payment successful</string>
    <string name="your_payment_was_successful" translatable="false">Your payment was successful</string>
    <string name="payment_error" translatable="false">Error</string>
    <string name="there_payment_error" translatable="false">There was a error, please try again</string>
</resources>