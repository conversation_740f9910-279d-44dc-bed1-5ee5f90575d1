/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.contactless;

import android.app.Activity;
import android.os.ConditionVariable;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.pax.bizentity.entity.Issuer;
import com.pax.bizlib.trans.Device;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.dal.ICardReaderHelper;
import com.pax.dal.entity.EReaderType;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.contactless.IContactlessCallback;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.pax.emvservice.export.IEmvContactlessService;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.dialog.RemoveCardDialog;
import com.visionpay.opt.pax.emv.IEmvProcessCallback;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.online.OnlineTaskFactory;
import com.visionpay.opt.pax.emv.pin.IPinTask;
import com.visionpay.opt.pax.emv.pin.OfflinePinTask;
import com.visionpay.opt.pax.emv.pin.OnlinePinTask;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EIssuer;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.utils.DialogUtils;
import com.visionpay.opt.pax.utils.EmvAidUtils;

import java.util.List;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/3/23
 */
@WorkerThread
public class ContactlessProcessCallback implements IContactlessCallback, IEmvProcessCallback {
    private static final String TAG = "ContactlessProcessCallback";

    private final IEmvContactlessService emv;
    private final ConditionVariable cv;
    private ContactlessService.RemoveCardCallback removeCardCallback;
    private IEmvService.ConfirmCallback confirmCallback;
    private IEmvService.ErrorCallback errorCallback;
    private List<CvmType> cvmTypeList;

    public ContactlessProcessCallback(@NonNull IEmvContactlessService emv, @NonNull ConditionVariable cv) {
        this.emv = emv;
        this.cv = cv;
    }

    @Override
    public void onReadCardOk() {
        // do nothing
    }

    @Override
    public void onRemoveCard() {
        try {
            DialogUtils.dismiss();
            final boolean[] isShown = { false };
            RemoveCardDialog dialog = new RemoveCardDialog();
            Device.removeCard(result -> {
                if (!isShown[0]) {
                    Activity activity = ActivityStack.getInstance().top();
                    if (activity instanceof FragmentActivity) {
                        FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
                        dialog.show(manager, "Remove Card");
                        isShown[0] = true;
                    }
                    if (removeCardCallback != null) {
                        removeCardCallback.onRemove();
                    }
                }
            });
            dialog.dismissAllowingStateLoss();
        } catch (Exception e) {
            LogUtils.e(TAG, "Remove card error", e);
        }
    }

    @Override
    public boolean needSeePhone() {
        boolean isLastNeedSeePhone = emv.getIsLastNeedSeePhone();
        //reset to default value
        emv.setIsLastNeedSeePhone(false);
        return isLastNeedSeePhone;
    }

    @Override
    public int confirmCard() {
        LogUtils.d(TAG, "============ Confirm Card Start ============");

        String aid = ConvertUtils.bcd2Str(emv.getTlv(0x84));
        int seqNumber = ConvertUtils.bcd2Int(emv.getTlv(0x5F34));
        String transType = ConvertUtils.bcd2Str(emv.getTlv(TagsTable.TRANS_TYPE));
        int type = 0;
        try {
            type = Integer.parseInt(transType);
        } catch (Exception e) {
            LogUtils.e(TAG, "Parse error", e);
        }
        List<EmvAidInfo> infoList = EmvAidUtils.getContactlessAidInfo(emv.getKernelType(), aid, type);
        String path = EmvAidUtils.getEditContactlessAidPath(emv.getKernelType(), aid);

        Issuer matchedIssuer = emv.getMatchedIssuerByPan();

        if (confirmCallback != null) {
            EIssuer info = EIssuer.parse(emv.getKernelType());
            if (info != null) {
                confirmCallback.onConfirm(emv.getPan(),
                        info.getIssuerIconRes(),
                        info.getDisplayName(),
                        getCardHolderName(),
                        infoList, path, aid, seqNumber + "",
                        emv.getExpireDate(),
                        new String(emv.getTlv(TagsTable.APP_LABEL)),
                        ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            } else {
                String issuerName = "Unknown";
                if (matchedIssuer != null) {
                    issuerName = matchedIssuer.getName();
                }
                confirmCallback.onConfirm(emv.getPan(),
                        R.drawable.ic_issuer_other, issuerName,
                        getCardHolderName(), infoList, path, aid, seqNumber + "",
                        emv.getExpireDate(),
                        new String(emv.getTlv(TagsTable.APP_LABEL)),
                        ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            }
        }
        LogUtils.d(TAG, "============ Confirm Card End ============");
        return 0;
    }

    @NonNull
    private String getCardHolderName() {
        String cardHolderName = emv.getCardholderName();
        if (cardHolderName == null || cardHolderName.isEmpty() || cardHolderName.trim().isEmpty()) {
            return "";
        }
        return new String(ConvertUtils.strToBcdPaddingLeft(cardHolderName));
    }

    @Override
    public int onCardHolderPwd(boolean isOnlinePin, boolean supportPINByPass, int leftTimes,
            byte[] pinData) {
        LogUtils.d(TAG, "============ PIN Input Start ============");
        DialogUtils.dismiss();

        IPinTask pinTask;
        if (isOnlinePin) {
            pinTask = new OnlinePinTask((title, reason) -> errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_online_pin)));
            }
        } else {
            pinTask = new OfflinePinTask(leftTimes, (title, reason) ->
                    errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_offline_pin)));
            }
        }

        int ret = pinTask.start(emv, supportPINByPass, false);
        LogUtils.d(TAG, "Pin Task ret: " + ret);
        LogUtils.d(TAG, "============ PIN Input End ============");
        return ret;
    }

    @Override
    public int showEnterTip() {
        // do nothing
        return 0;
    }

    @Override
    public OnlineResultWrapper startOnlineProcess(long amount, String terminalId, int detectResult, String reference, ISendingOnlineCallback sendinfOnlineCallback) {
        DialogUtils.dismiss();
        return OnlineTaskFactory.GetTask(emv, sendinfOnlineCallback).start(emv, amount, terminalId, detectResult, reference);
    }

    @Override
    public void onDetect2ndTap() {
        cv.close();

        Activity activity = ActivityStack.getInstance().top();
        if (activity instanceof FragmentActivity) {
            DialogUtils.createProcessingDialog((FragmentActivity) activity,
                    "Please Tap Card",
                    "Contactless kernel need second tap card");
        }

        App.getApp().runInBackground(() -> {
            try {
                ICardReaderHelper helper = App.getDal().getCardReaderHelper();
                helper.polling(EReaderType.PICC, 60 * 1000);
                helper.stopPolling();
            } catch (Exception e) {
                LogUtils.d(TAG, "Detect Card Failed", e);
            } finally {
                cv.open();
            }
        });

        cv.block(60 * 1000);
        DialogUtils.dismiss();
        LogUtils.d(TAG, "Detect 2nd Tap Done.");
    }

    public ContactlessProcessCallback setRemoveCardCallback(ContactlessService.RemoveCardCallback removeCardCallback) {
        this.removeCardCallback = removeCardCallback;
        return this;
    }

    @NonNull
    public ContactlessProcessCallback setConfirmCallback(IEmvService.ConfirmCallback confirmCallback) {
        this.confirmCallback = confirmCallback;
        return this;
    }

    @NonNull
    public ContactlessProcessCallback setErrorCallback(IEmvService.ErrorCallback errorCallback) {
        this.errorCallback = errorCallback;
        return this;
    }

    @NonNull
    @Override
    public ContactlessProcessCallback setCvmTypeList(List<CvmType> cvmTypeList) {
        this.cvmTypeList = cvmTypeList;
        return this;
    }
}
