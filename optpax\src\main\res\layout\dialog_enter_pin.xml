<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/23                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dialog_padding_horizontal"
    android:paddingVertical="@dimen/dialog_padding_vertical"
    android:background="@android:color/transparent">

    <TextView
        android:id="@+id/enter_pin_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/main_text"
        android:textSize="@dimen/dialog_title_text"
        android:textStyle="bold"
        tools:text="Enter Online PIN" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/enter_pin_text_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dialog_content_spacing_vertical"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        app:counterEnabled="true"
        app:boxCornerRadiusTopStart="@dimen/dialog_input_box_corner_radius"
        app:boxCornerRadiusTopEnd="@dimen/dialog_input_box_corner_radius"
        app:boxCornerRadiusBottomStart="@dimen/dialog_input_box_corner_radius"
        app:boxCornerRadiusBottomEnd="@dimen/dialog_input_box_corner_radius">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/enter_pin_text_field"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_normal_size"
            android:gravity="center"
            android:letterSpacing="0.2"
            android:lines="1"
            android:cursorVisible="false"
            tools:text="●●●●●●●●●●●●"/>

    </com.google.android.material.textfield.TextInputLayout>

</LinearLayout>