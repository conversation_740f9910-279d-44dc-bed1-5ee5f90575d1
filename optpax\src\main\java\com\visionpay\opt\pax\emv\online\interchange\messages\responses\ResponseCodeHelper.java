package com.visionpay.opt.pax.emv.online.interchange.messages.responses;

public class ResponseCodeHelper {
    public static String getGatwayResponse(String HostResponse){
        switch (HostResponse){
            case "00":
                return "Transaction Approved";
            case "01":
                return "Refer to Issuer";
            case "02":
                return "Refer to Issuer, special";
            case "03":
                return "No Merchant";
            case "04":
                return "Pick Up Card";
            case "05":
                return "Do Not Honour";
            case "06":
                return "Error";
            case "07":
                return "Pick Up Card, Special";
            case "08":
                return "Honour With Identification";
            case "09":
                return "Request In Progress";
            case "10":
                return "Approved For Partial Amount - not expected";
            case "11":
                return "Approved, VIP";
            case "12":
                return "Invalid Transaction";
            case "13":
                return "Invalid Amount";
            case "14":
                return "Invalid Card Number";
            case "15":
                return "No Issuer";
            case "16":
                return "Approved, Update Track 3";
            case "19":
                return "Re-enter Last Transaction";
            case "21":
                return "No Action Taken";
            case "22":
                return "Suspected Malfunction";
            case "23":
                return "Unacceptable Transaction Fee";
            case "25":
                return "Unable to Locate Record On File";
            case "30":
                return "Format Error";
            case "31":
                return "Bank Not Supported By Switch";
            case "33":
                return "Expired Card, Capture";
            case "34":
                return "Suspected Fraud, Retain Card";
            case "35":
                return "Card Acceptor, Contact Acquirer, Retain Card";
            case "36":
                return "Restricted Card, Retain Card";
            case "37":
                return "Contact Acquirer Security Department, Retain Card";
            case "38":
                return "PIN Tries Exceeded, Capture";
            case "39":
                return "No Credit Account";
            case "40":
                return "Function Not Supported";
            case "41":
                return "Lost Card";
            case "42":
                return "No Universal Account";
            case "43":
                return "Stolen Card";
            case "44":
                return "No Investment Account";
            case "51":
                return "Insufficient Funds";
            case "52":
                return "No Cheque Account";
            case "53":
                return "No Savings Account";
            case "54":
                return "Expired Card";
            case "55":
                return "Incorrect PIN";
            case "56":
                return "No Card Record";
            case "57":
                return "Function Not Permitted to Cardholder";
            case "58":
                return "Function Not Permitted to Terminal";
            case "59":
                return "Suspected Fraud";
            case "60":
                return "Acceptor Contact Acquirer";
            case "61":
                return "Exceeds Withdrawal Limit";
            case "62":
                return "Restricted Card";
            case "63":
                return "Security Violation";
            case "64":
                return "Original Amount Incorrect";
            case "66":
                return "Acceptor Contact Acquirer, Security";
            case "67":
                return "Capture Card";
            case "75":
                return "PIN Tries Exceeded";
            case "82":
                return "CVV Validation Error";
            case "90":
                return "Cut-off In Progress";
            case "91":
                return "Host or Switch Unavailable/Signed Off/Timed Out/Unavailable";
            case "92":
                return "Unable To Route Transaction";
            case "93":
                return "Cannot Complete, Violation of Law";
            case "94":
                return "Duplicate Transaction";
            case "96":
                return "System Error";
            case "98":
                return "MAC Error";
            default:
                return "";
        }
    }
}
