<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/trans_error_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/card_margin_horizontal"
    android:layout_marginEnd="@dimen/card_margin_horizontal"
    android:layout_marginTop="@dimen/card_spacing"
    app:cardElevation="0dp"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:cardBackgroundColor="@color/error_card_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/card_padding_vertical"
        android:paddingHorizontal="@dimen/card_padding_horizontal">

        <ImageView
            android:id="@+id/trans_error_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_cancel_circle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:tint="@color/error_card_content" />

        <TextView
            android:id="@+id/trans_error_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/card_content_spacing_horizontal"
            android:textColor="@color/error_card_content"
            android:textSize="@dimen/card_title_size"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintTop_toTopOf="@id/trans_error_icon"
            app:layout_constraintStart_toEndOf="@id/trans_error_icon"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Card Communication error"/>

        <TextView
            android:id="@+id/trans_error_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/card_content_spacing_horizontal"
            android:paddingBottom="@dimen/card_content_divider_spacing"
            android:textColor="@color/error_card_content"
            android:textSize="@dimen/text_normal_size"
            app:layout_constraintTop_toBottomOf="@id/trans_error_title"
            app:layout_constraintStart_toEndOf="@id/trans_error_icon"
            app:layout_constraintEnd_toEndOf="parent"
            tools:text="Card Communication error"/>

        <View
            android:id="@+id/trans_error_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_width"
            android:background="@color/error_card_divider"
            app:layout_constraintTop_toBottomOf="@id/trans_error_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/trans_error_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:text="@string/ok"
            android:textColor="@color/error_card_content"
            android:minWidth="@dimen/text_button_min_width"
            app:rippleColor="@color/error_card_ripple"
            app:iconTint="@color/error_card_content"
            app:layout_constraintTop_toBottomOf="@id/trans_error_divider"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>