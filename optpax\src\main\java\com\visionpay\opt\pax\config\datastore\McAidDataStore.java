/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/13                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.datastore;

import androidx.annotation.Nullable;
import com.pax.bizentity.db.helper.PaypassAidDbHelper;
import com.pax.bizentity.entity.clss.paypass.PayPassAidBean;

/**
 * Contactless MC (PayPass) AID DataStore.
 */
public class McAidDataStore extends BaseEmvParamDataStore<McAidDataStore> {
    public static final String APP_NAME = "APP_NAME";
    public static final String TRANS_LIMIT = "TRANS_LIMIT";
    public static final String FLOOR_LIMIT = "FLOOR_LIMIT";
    public static final String CVM_LIMIT = "CVM_LIMIT";
    public static final String TAC_DENIAL = "TAC_DENIAL";
    public static final String TAC_ONLINE = "TAC_ONLINE";
    public static final String TAC_DEFAULT = "TAC_DEFAULT";
    public static final String KERNEL_CONFIG = "KERNEL_CONFIG";
    public static final String CVM_CAP = "CVM_CAP";
    public static final String NO_CVM_CAP = "NO_CVM_CAP";
    public static final String SEC_CAP = "SEC_CAP";
    public static final String MAG_CVM_CAP = "MAG_CVM_CAP";
    public static final String MAG_NO_CVM_CAP = "MAG_NO_CVM_CAP";
    public static final String ADD_TERM_CAP = "ADD_TERM_CAP";
    public static final String RISK_MANAGE = "RISK_MANAGE";

    @Nullable
    private final PayPassAidBean aid;

    public McAidDataStore(@Nullable PayPassAidBean aid) {
        this.aid = aid;
    }

    @Override
    public void putString(String key, @Nullable String value) {
        if (aid == null) {
            return;
        }
        switch (key) {
            case TAC_DEFAULT:
                aid.setTacDefault(value);
                break;
            case TAC_DENIAL:
                aid.setTacDenial(value);
                break;
            case TAC_ONLINE:
                aid.setTacOnline(value);
                break;
            case KERNEL_CONFIG:
                aid.setKernelConfig(value);
                break;
            case CVM_CAP:
                aid.setCvmRequired(value);
                break;
            case NO_CVM_CAP:
                aid.setNoCvmRequired(value);
                break;
            case SEC_CAP:
                aid.setSecurityCapability(value);
                break;
            case MAG_CVM_CAP:
                aid.setMagCvm(value);
                break;
            case MAG_NO_CVM_CAP:
                aid.setMagNoCvm(value);
                break;
            case ADD_TERM_CAP:
                aid.setTerminalAdditionalCapability(value);
                break;
            case RISK_MANAGE:
                aid.setRiskManageData(value);
                break;
            default: return;
        }
        PaypassAidDbHelper.getInstance().update(aid);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Override
    public void putLong(String key, long value) {
        if (aid == null) {
            return;
        }
        switch (key) {
            case TRANS_LIMIT:
                aid.setTransLimit(value);
                break;
            case FLOOR_LIMIT:
                aid.setFloorLimit(value);
                break;
            case CVM_LIMIT:
                aid.setCvmLimit(value);
                break;
            default: return;
        }
        PaypassAidDbHelper.getInstance().update(aid);
        if (refreshCachedCallback != null) {
            refreshCachedCallback.needRefresh();
        }
    }

    @Nullable
    @Override
    public String getString(String key, @Nullable String defValue) {
        if (aid == null) {
            return defValue;
        }
        switch (key) {
            case APP_NAME: return aid.getAppName();
            case TAC_DEFAULT: return aid.getTacDefault();
            case TAC_DENIAL: return aid.getTacDenial();
            case TAC_ONLINE: return aid.getTacOnline();
            case KERNEL_CONFIG: return aid.getKernelConfig();
            case CVM_CAP: return aid.getCvmRequired();
            case NO_CVM_CAP: return aid.getNoCvmRequired();
            case SEC_CAP: return aid.getSecurityCapability();
            case MAG_CVM_CAP: return aid.getMagCvm();
            case MAG_NO_CVM_CAP: return aid.getMagNoCvm();
            case ADD_TERM_CAP: return aid.getTerminalAdditionalCapability();
            case RISK_MANAGE: return aid.getRiskManageData();
            default: return defValue;
        }
    }

    @Override
    public long getLong(String key, long defValue) {
        if (aid == null) {
            return defValue;
        }
        switch (key) {
            case TRANS_LIMIT: return aid.getTransLimit();
            case FLOOR_LIMIT: return aid.getFloorLimit();
            case CVM_LIMIT: return aid.getCvmLimit();
            default: return defValue;
        }
    }
}
