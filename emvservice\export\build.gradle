apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        release {
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        stagingRelease {
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        autoTest{

        }
        debug{

        }
    }
}
dependencies {
    api project(':bizentity')
    api project(':emvbase')
    api fileTree(include: ['*.jar'], dir: 'libs')
    testImplementation "junit:junit:$rootProject.junit"
    testImplementation "org.mockito:mockito-all:$rootProject.mockito"
    testImplementation "org.hamcrest:hamcrest-all:$rootProject.hamcrest"
    testImplementation "androidx.arch.core:core-testing:$rootProject.coreTesting"
    androidTestImplementation "androidx.test:runner:$rootProject.runner"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    androidTestImplementation "junit:junit:$rootProject.junit"
}
