package com.visionpay.opt.pax.viewmodel;

import android.app.Activity;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.pax.bizentity.entity.ETransType;
import com.pax.bizentity.entity.SearchMode;
import com.pax.bizentity.entity.TransData;
import com.pax.bizlib.card.CardReaderHelper;
import com.pax.bizlib.trans.Device;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LazyInit;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonui.clss.ClssLight;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.IEmvContactService;
import com.pax.emvservice.export.IEmvContactlessService;
import com.pax.emvservice.export.IEmvMagService;
import com.pax.emvservice.export.IEmvMifareService;
import com.pax.emvservice.export.transaction.ITransactionService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.activity.AdminActivity;
import com.visionpay.opt.pax.dialog.RemoveCardDialog;
import com.visionpay.opt.pax.emv.EmvPreProcessService;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.contact.ContactService;
import com.visionpay.opt.pax.emv.contactless.ContactlessService;
import com.visionpay.opt.pax.emv.magnetic.MagService;
import com.visionpay.opt.pax.emv.mifare.MifareService;
import com.visionpay.opt.pax.entity.AuthorizationType;
import com.visionpay.opt.pax.entity.CardVerificationMethod;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.entity.EmvTag;
import com.visionpay.opt.pax.entity.ErrorEvent;
import com.visionpay.opt.pax.entity.IM30State;
import com.visionpay.opt.pax.entity.POSTransaction;
import com.visionpay.opt.pax.entity.TransStatus;
import com.visionpay.opt.pax.entity.TransactionStatus;
import com.visionpay.opt.pax.entity.VisionPayTransaction;
import com.visionpay.opt.pax.fragment.TransFragmentManager;
import com.visionpay.opt.pax.message.BroadcastMesseger;
import com.visionpay.opt.pax.utils.DialogUtils;

import java.util.Date;
import java.util.List;

/**
 * Trans View Model
 *
 * 主要负责启动寻卡、关闭寻卡、启动 EMV、维护状态。
 * TransViewModel 作为与寻卡类、EMV 流程类沟通的桥梁，用来显示并更新 Activity 的状态。根据 EMV 流程的进度，
 * TransViewModel 需要适时执行 TransFragmentManager 中的方法以及更新 TransStatus，以实现“卡片”显示和隐藏的控制
 * 以及“卡片”上显示的数据的更新。
 * 这样做的好处是，ViewModel 可以在设备经历旋转、切换深色模式等情况下不被销毁，因此也就可以保证界面上显示的内容不会
 * 因为这些情况而出现错乱。
 */
public class ReadCardViewModel extends ViewModel {
    private static final String TAG = "ReadCardViewModel";
    private static final byte defaultSearchCardType = SearchMode.INSERT | SearchMode.INTERNAL_WAVE | SearchMode.SWIPE | SearchMode.INTERNAL_MIFARE;
    private static final String TIME_PATTERN_TRANS = "yyyyMMddHHmmss";

    private final LazyInit<IEmvContactService> emv = LazyInit.by(() ->
            Router.getService(IEmvContactService.class, EmvServiceConstant.EMVSERVICE_CONTACT));
    private final LazyInit<IEmvMagService> mag = LazyInit.by(() ->
            Router.getService(IEmvMagService.class, EmvServiceConstant.EMVSERVICE_MAGNETIC));
    private final LazyInit<IEmvContactlessService> clss = LazyInit.by(() ->
            Router.getService(IEmvContactlessService.class, EmvServiceConstant.EMVSERVICE_CONTACTLESS));
    private final LazyInit<IEmvMifareService> mifare = LazyInit.by(() ->
            Router.getService(IEmvMifareService.class, EmvServiceConstant.EMVSERVICE_MIFARE));

    private final LazyInit<ContactService> emvService = LazyInit.by(ContactService::new);
    private final LazyInit<MagService> magService = LazyInit.by(MagService::new);
    private final LazyInit<ContactlessService> clssService = LazyInit.by(ContactlessService::new);
    private final LazyInit<MifareService> mifareService = LazyInit.by(MifareService::new);

    private TransStatus transStatus;
    private final MutableLiveData<Boolean> retryEnabled = new MutableLiveData<>(false);

    private final LazyInit<CardReaderHelper> cardReaderHelper =
            LazyInit.by(() -> new CardReaderHelper(new CardReaderCallback()));
    private boolean isEmvProcessing = false;
    private String procCode;
    private VisionPayTransaction visionPayTransaction;
    private POSTransaction posTransaction;

    private RemoveCardCallback removeCardCallback = new RemoveCardCallback();
    private ConfirmCallback confirmCallback = new ConfirmCallback();
    private RetryCallback retryCallback = new RetryCallback();
    private ErrorCallback errorCallback = new ErrorCallback();
    private CompleteCallback completeCallback = new CompleteCallback();
    private SendingOnlineCallback sendingOnlineCallback = new SendingOnlineCallback();
    private MessageCallback messageCallback;
    private ErrorEventCallback errorEventCallback;
    private CardDetectCallback cardDetectCallback;
    private CompleteReaderCallback completeReaderCallback;
    private ConfirmReaderCallback confirmReaderCallback;

    private String lastMessage;
    private long lastTime;

    public ReadCardViewModel(){
    }

    private byte getSearchCardType() {
        return transStatus.getCurrentSearchMode();
    }

    private void updateStatus(TransStatus status) {
        this.transStatus = status;
        //TransFragmentManager.getInstance().updateStatus(status);
    }

    private TransStatus getStatus() {
        return transStatus;
    }

    public LiveData<Boolean> listenRetryEnable() {
        return retryEnabled;
    }

    public boolean isEmvProcessing() {
        return isEmvProcessing;
    }

    public void initTrans(@NonNull ETransType transType, VisionPayTransaction visionPayTransaction, POSTransaction posTransaction) {
        LogUtils.d(TAG, "Init trans");
        this.procCode = transType.getProcCode();
        this.visionPayTransaction = visionPayTransaction;
        this.posTransaction = posTransaction;

        byte searchCardMode = new EmvPreProcessService(
                this.procCode,
                visionPayTransaction.getTransactionAmount(),
                Device.getTime(TIME_PATTERN_TRANS),
                0,
                defaultSearchCardType,
                ConvertUtils.strToBcdPaddingLeft(String.valueOf(visionPayTransaction.getTransactionCurrency())),
                visionPayTransaction.getIm30TerminalId()
        ).start();
        LogUtils.d(TAG, "Search Card Mode: " + searchCardMode);

        updateStatus(TransStatus.newInstance(searchCardMode));
    }

    public void startSearchCard() {
        LogUtils.d(TAG, "Start Search Card");
        cardReaderHelper.get().stopPolling();
        cardReaderHelper.get().polling(getSearchCardType());
        retryEnabled.postValue(false);
        sendMessageToCLient(IM30State.AwaitingCardRead);
    }

    public void stopSearchCard() {
        LogUtils.d(TAG, "Stop Search Card");
        cardReaderHelper.get().stopPolling();
        updateStatus(getStatus().stopSearchCard()
                .setIccStatusPrompt("Stop")
                .setPiccStatusPrompt("Stop"));
    }

    public void setMessageCallback(@Nullable MessageCallback messageCallback) {
        this.messageCallback = messageCallback;
    }

    public void setErrorEventCallback(@Nullable ErrorEventCallback errorEventCallback) {
        this.errorEventCallback = errorEventCallback;
    }

    public void setCardDetectCallback(@Nullable CardDetectCallback cardDetectCallback) {
        this.cardDetectCallback = cardDetectCallback;
    }

    public void setCompleteReaderCallback(@Nullable CompleteReaderCallback completeReaderCallback) {
        this.completeReaderCallback = completeReaderCallback;
    }

    public void setConfirmReaderCallback(@Nullable ConfirmReaderCallback confirmReaderCallback) {
        this.confirmReaderCallback = confirmReaderCallback;
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        stopSearchCard();
        removeCardCallback = null;
        confirmCallback = null;
        retryCallback = null;
        errorCallback = null;
        completeCallback = null;
        messageCallback = null;
        errorEventCallback = null;
        cardDetectCallback = null;
        //App.getApp().runInBackground(() -> {
        //    PosDeviceUtils.enableHomeRecentKey(true);
        //    PosDeviceUtils.enableStatusBar(true);
        //});
    }

    private void sendMessageToCLient(IM30State im30State) {
        sendMessageToCLient(im30State, null,
                "", "", "", null, "",
                "", "", "",
                "", "", "", -1, "", null, null);
    }

    private void sendMessageToCLient(IM30State im30State, String cardType, String cardSignature, String cardSequenceNumber,
                                     String cardExpiryDate, String applicationId, String applicationLabel,
                                     int applicationTransactionCounter, boolean isBankCard) {
        sendMessageToCLient(im30State, null,
                cardType, cardSignature, cardSequenceNumber, null, "",
                "", "", "", cardExpiryDate, applicationId, applicationLabel, applicationTransactionCounter, "", isBankCard, null);
    }

    private void sendMessageToCLient(IM30State im30State, TransactionStatus transactionStatus) {
        sendMessageToCLient(im30State, transactionStatus,
                "", "", "",
                null, "",
                "", "", "",
                "", "", "", -1, "", null, null);
    }

    private void sendMessageToCLient(IM30State im30State, TransactionStatus transactionStatus,
                                     CardVerificationMethod cardVerificationMethod, String merchantId,
                                     String STAN, String gatewayResponse, String gatewayResponseCode, String authorizationId, Long internalReference) {
        sendMessageToCLient(im30State, transactionStatus,
                "", "", "",
                cardVerificationMethod, merchantId,
                STAN, gatewayResponse, gatewayResponseCode,
                "", "", "", -1, authorizationId, null, internalReference);
    }

    private void sendMessageToCLient(IM30State im30State, TransactionStatus transactionStatus,
                                     String cardType, String cardSignature, String cardSequenceNumber,
                                     CardVerificationMethod cardVerificationMethod, String merchantId,
                                     String STAN, String gatewayResponse, String gatewayResponseCode,
                                     String cardExpiryDate, String applicationId, String applicationLabel,
                                     int applicationTransactionCounter, String authorizationId,
                                     Boolean isBankCard, Long internalReference) {
        visionPayTransaction.setIm30State(im30State);
        visionPayTransaction.setCompletedUTCDateTime(new Date());
        if(transactionStatus != null)
            visionPayTransaction.setTransactionStatus(transactionStatus);
        if(!cardType.isEmpty())
            visionPayTransaction.setCardType(cardType);
        if(!cardSignature.isEmpty())
            visionPayTransaction.setCardSignature(cardSignature);
        if(!cardSequenceNumber.isEmpty())
            visionPayTransaction.setCardSequenceNumber(cardSequenceNumber);
        if(cardVerificationMethod != null)
            visionPayTransaction.setCardVerificationMethod(cardVerificationMethod);
        if(!merchantId.isEmpty())
            visionPayTransaction.setMerchantId(merchantId);
        if(!STAN.isEmpty())
            visionPayTransaction.setStan(STAN);
        if(!gatewayResponse.isEmpty())
            visionPayTransaction.setGatewayResponse(gatewayResponse);
        if(!gatewayResponseCode.isEmpty())
            visionPayTransaction.setGatewayResponseCode(gatewayResponseCode);
        if(!cardExpiryDate.isEmpty())
            visionPayTransaction.setCardExpiryDate(cardExpiryDate);
        if(!applicationId.isEmpty())
            visionPayTransaction.setApplicationId(applicationId);
        if(!applicationLabel.isEmpty())
            visionPayTransaction.setApplicationLabel(applicationLabel);
        if(applicationTransactionCounter != -1)
            visionPayTransaction.setApplicationTransactionCounter(applicationTransactionCounter);
        if(!authorizationId.isEmpty())
            visionPayTransaction.setAuthorizationId(authorizationId);
        if(isBankCard != null)
            visionPayTransaction.setAuthorizationType(isBankCard ? AuthorizationType.BankCard : AuthorizationType.WhiteCard);
        if(internalReference != null)
            visionPayTransaction.setIm30Reference(internalReference + "");
        BroadcastMesseger.getInstance().send(visionPayTransaction);
    }

    private class CardReaderCallback implements CardReaderHelper.Callback {
        //private String lastMessage = null;
        //private long lastTime = 0;

        @Override
        public void onIccDisable() {
            byte type = getSearchCardType();
            if (SearchMode.isSupportIcc(type)) {
                // Disable Icc
                updateStatus(getStatus().disableIcc());
            }
        }

        @Override
        public void onPiccDisable() {
            byte type = getSearchCardType();
            if (SearchMode.isWave(type)) {
                // Disable Picc
                updateStatus(getStatus().disablePicc());
            }
        }

        @Override
        public void onError(String message) {
            // Display SnackBar or Toast
            if (lastMessage != null && lastMessage.equals(message) && (System.currentTimeMillis() - lastTime < 2000)) {
                return;
            }
            lastMessage = message;
            lastTime = System.currentTimeMillis();
            if (messageCallback != null) {
                messageCallback.onSend(message);
                //sendMessageToCLient(IM30State.CardReadFailed, TransactionStatus.Failed);
            }
        }

        @Override
        public void onIccDetected() {
            //Activity activity = ActivityStack.getInstance().top();
            //if (activity instanceof FragmentActivity) {
            //    DialogUtils.createProcessingDialog((FragmentActivity) activity, "EMV Processing",
            //            "Contact EMV service running, please wait...");
            //}
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.INSERT);
            updateStatus(getStatus().insertProcessing());
            if(posTransaction.getExternalDeviceToken() != null)
                emv.get().setTlv(0x100001, posTransaction.getExternalDeviceToken().getBytes());
            emvService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .setRemoveCardCallback(removeCardCallback)
                    .setSendinfOnlineCallback(sendingOnlineCallback)
                    .start(emv.get(), visionPayTransaction.getTransactionAmount(), visionPayTransaction.getIm30TerminalId(), SearchMode.INSERT, visionPayTransaction.getExternalReference());
            isEmvProcessing = true;
        }

        @Override
        public void onPiccDetected(byte[] serialInfo) {
            //Activity activity = ActivityStack.getInstance().top();
            //if (activity instanceof FragmentActivity) {
            //    DialogUtils.createProcessingDialog((FragmentActivity) activity, "EMV Processing",
            //            "Contactless EMV service running, please wait...");
            //}
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.WAVE);
            updateStatus(getStatus().clssProcessing());
            if(posTransaction.getExternalDeviceToken() != null)
                clss.get().setTlv(0x100001, posTransaction.getExternalDeviceToken().getBytes());
            clssService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .setRemoveCardCallback(removeCardCallback)
                    .setRetryCallback(retryCallback)
                    .setSendinfOnlineCallback(sendingOnlineCallback)
                    .start(clss.get(), visionPayTransaction.getTransactionAmount(), visionPayTransaction.getIm30TerminalId(), SearchMode.WAVE, visionPayTransaction.getExternalReference());
            isEmvProcessing = true;
        }

        @Override
        public void onMagDetected(String trackData1, String trackData2, String trackData3) {
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.SWIPE);
            updateStatus(getStatus().insertMagProcessing());
            mag.get().setTrackData(trackData1, trackData2, trackData3);
            if(posTransaction.getExternalDeviceToken() != null)
                mag.get().setTlv(0x100001, posTransaction.getExternalDeviceToken().getBytes());
            magService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .setSendinfOnlineCallback(sendingOnlineCallback)
                    .start(mag.get(), visionPayTransaction.getTransactionAmount(), visionPayTransaction.getIm30TerminalId(), SearchMode.SWIPE, visionPayTransaction.getExternalReference());
            isEmvProcessing = true;
        }

        @Override
        public void onMifareDetected(byte[] serialInfo){
            messageCallback.onSend("Card detected. Reading card...");
            cardDetectCallback.onDetect(SearchMode.WAVE);
            updateStatus(getStatus().clssProcessing());
            mifare.get().setTlv(0x100000, serialInfo);
            if(posTransaction.getExternalDeviceToken() != null)
                mifare.get().setTlv(0x100001, posTransaction.getExternalDeviceToken().getBytes());
            mifareService.get().setConfirmCallback(confirmCallback)
                    .setErrorCallback(errorCallback)
                    .setCompleteCallback(completeCallback)
                    .setRemoveCardCallback(removeCardCallback)
                    .setSendinfOnlineCallback(sendingOnlineCallback)
                    .start(mifare.get(), visionPayTransaction.getTransactionAmount(), visionPayTransaction.getIm30TerminalId(), SearchMode.WAVE, visionPayTransaction.getExternalReference());
            isEmvProcessing = true;
        }
    }

    private class SendingOnlineCallback implements ISendingOnlineCallback {

        @Override
        public void onStart() {
            sendMessageToCLient(IM30State.SendingOnline);
        }

        @Override
        public void onComplete(boolean approved, boolean hasPin, String merchantId, String STAN, String gatewayReposnse, String gatewayResponseCode, String authorizationId) {
            ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
            TransData transData = transactionService.getByReference(visionPayTransaction.getExternalReference());
            sendMessageToCLient(IM30State.SendingOnlineComplete, approved ? TransactionStatus.Approved : TransactionStatus.Declined,
                    hasPin ? CardVerificationMethod.PIN : CardVerificationMethod.None, merchantId, STAN, gatewayReposnse, gatewayResponseCode, authorizationId, transData.getId());
        }

        @Override
        public void onError() {
            sendMessageToCLient(IM30State.SendingOnlineFailed, TransactionStatus.Failed);
        }

        @Override
        public void onTimeOut() {
            sendMessageToCLient(IM30State.Timeout, TransactionStatus.Failed);
        }
    }

    private class RemoveCardCallback implements ContactlessService.RemoveCardCallback {
        @Override
        public void onRemove() {
            updateStatus(getStatus().clssDone());
        }
    }

    private class ConfirmCallback implements IEmvService.ConfirmCallback {
        @Override
        public void onConfirm(
                @NonNull String pan,
                int issuerIconId,
                @NonNull String issuerName,
                @NonNull String cardHolderName,
                @Nullable List<EmvAidInfo> infoList,
                @Nullable String editAidPath,
                @Nullable String editAidParam,
                @Nullable String cardSequenceNumber,
                @Nullable String cardExpiryDate,
                @Nullable String applicationLabel,
                @Nullable int applicationTransactionCounter,
                @Nullable boolean isBankCard) {
            String enterMode;
            if (getStatus().isContactService()) {
                enterMode = "Contact";
            } else if (getStatus().isMagService()) {
                enterMode = "Magnetic";
            } else {
                enterMode = "Contactless";
            }
            //TransFragmentManager.getInstance()
            //        .hide(TransFragmentManager.SEARCH_CARD_PROMPT)
            //        .show(TransFragmentManager.CONFIRM_AID);
            updateStatus(getStatus()
                    .setPan(formatPan(pan))
                    .setIssuerIconId(issuerIconId)
                    .setIssuerName(issuerName)
                    .setCardHolderName(cardHolderName)
                    .setEnterMode(enterMode)
                    .setAidInfoList(infoList)
                    .setEditAidPath(editAidPath)
                    .setEditAidParam(editAidParam));
            confirmReaderCallback.onConfirm(issuerIconId, issuerName);
            sendMessageToCLient(IM30State.CardReadSuccess, issuerName, pan, cardSequenceNumber,
                    cardExpiryDate, editAidParam, applicationLabel, applicationTransactionCounter, isBankCard);
        }

        private String formatPan(String str) {
            StringBuilder result = new StringBuilder();
            for (int i = 1; i <= str.length(); i++) {
                result.append(str.charAt(i - 1));
                if (i == str.length()) {
                    break;
                }
                if (i % 4 == 0) {
                    result.append(" ");
                }
            }
            return result.toString();
        }
    }

    private class RetryCallback implements ContactlessService.RetryCallback {
        @Override
        public void onRetry(byte searchMode, String contactPrompt, String clssPrompt,
                boolean needRemoveCard) {
            DialogUtils.dismiss();
            if (needRemoveCard) {
                try {
                    final boolean[] isShown = { false };
                    RemoveCardDialog dialog = new RemoveCardDialog();
                    Device.removeCard(result -> {
                        if (!isShown[0]) {
                            Activity activity = ActivityStack.getInstance().top();
                            if (activity instanceof FragmentActivity) {
                                FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
                                dialog.show(manager, "Remove Card");
                                isShown[0] = true;
                            }
                        }
                    });
                    dialog.dismissAllowingStateLoss();
                } catch (Exception e) {
                    LogUtils.e(TAG, "Remove card error", e);
                }
            }
            TransFragmentManager.getInstance()
                    .hideAll()
                    .show(TransFragmentManager.SEARCH_CARD_PROMPT);
            updateStatus(TransStatus.newInstance(getSearchCardType())
                    .setCurrentSearchMode(searchMode)
                    .setLight1Status(SearchMode.isWave(searchMode) ? ClssLight.BLINK : ClssLight.OFF)
                    .setIccStatusPrompt(contactPrompt)
                    .setPiccStatusPrompt(clssPrompt));
            startSearchCard();
        }
    }

    private class ErrorCallback implements IEmvService.ErrorCallback {
        @Override
        public void onError(@NonNull String title, String reason) {
            updateStatus(getStatus()
                    .stopSearchCard()
                    .setIccStatusPrompt("Stop")
                    .setPiccStatusPrompt("Stop"));
            // Show error reason
            boolean continueReading = false;
            if (errorEventCallback != null) {
                continueReading = errorEventCallback.onSend(new ErrorEvent()
                        .setTitle(title)
                        .setInfo(reason)
                        .setButtonContent("FINISH")
                        .setButtonClickListener(v -> ActivityStack.getInstance().popTo(AdminActivity.class)));
            }

            isEmvProcessing = false;

            DialogUtils.dismiss();
            if (messageCallback != null) {
                messageCallback.onSend("EMV Process Failed.");
            }
            retryEnabled.postValue(true);
            if(!continueReading)
                sendMessageToCLient(IM30State.CardReadFailed, TransactionStatus.Failed);
        }
    }

    private class CompleteCallback implements IEmvService.CompleteCallback {
        @Override
        public void onComplete(boolean isContact, @NonNull TransResultEnum result,
                @NonNull List<CvmType> cvmTypeList,
                @NonNull List<EmvTag> emvTagList) {
            // Show EMV tag
            TransFragmentManager.getInstance().show(TransFragmentManager.EMV_TAG);
            updateStatus(getStatus()
                    .stopSearchCard()
                    .setLight3Status(isContact ? ClssLight.OFF : ClssLight.ON)
                    .setContactService(isContact)
                    .setTransResult(getTransResult(result))
                    .setCvmTypeList(cvmTypeList)
                    .setEmvTagList(emvTagList));

            isEmvProcessing = false;

            DialogUtils.dismiss();
            if (messageCallback != null) {
                messageCallback.onSend("EMV Process Complete.");
            }
            retryEnabled.postValue(true);
            sendMessageToCLient(IM30State.PreAuthorizationComplete);
            completeReaderCallback.onComplete(visionPayTransaction.getTransactionStatus() == TransactionStatus.Approved);
        }

        private String getTransResult(TransResultEnum resultEnum) {
            switch (resultEnum) {
                case RESULT_FALLBACK:
                    return "Fallback";
                case RESULT_TRY_AGAIN:
                    return "Try Again";
                case RESULT_REQ_ONLINE:
                    return "Request Online";
                case RESULT_ONLINE_DENIED:
                    return "Online Denied";
                case RESULT_ONLINE_FAILED:
                    return "Online Failed";
                case RESULT_CLSS_SEE_PHONE:
                    return "See Phone";
                case RESULT_OFFLINE_DENIED:
                    return "Offline Denied";
                case RESULT_ONLINE_APPROVED:
                    return "Online Approved";
                case RESULT_SIMPLE_FLOW_END:
                    return "Simple Flow End";
                case RESULT_OFFLINE_APPROVED:
                    return "Offline Approved";
                case RESULT_ONLINE_CARD_DENIED:
                    return "Online Approved Card Denied";
                case RESULT_CLSS_TRY_ANOTHER_INTERFACE:
                    return "Try Another Interface";
                case RESULT_ONLINE_FAILED_CARD_APPROVED:
                    return "Online Failed Card Approved";
            }
            return resultEnum.name();
        }
    }

    public interface MessageCallback {
        void onSend(String message);
    }

    public interface ErrorEventCallback {
        boolean onSend(ErrorEvent event);
    }

    public interface CardDetectCallback {
        void onDetect(byte searchMode);
    }

    public interface CompleteReaderCallback {
        void onComplete(boolean isApproved);
    }

    public interface ConfirmReaderCallback {
        void onConfirm(int issuerIconId,
                       @NonNull String issuerName);
    }
}
