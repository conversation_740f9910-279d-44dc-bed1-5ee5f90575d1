<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/21                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <com.pax.commonui.clss.ClssLight
        android:id="@+id/light1"
        android:layout_width="0dp"
        android:layout_height="@dimen/clss_light_height"
        android:src="@drawable/clss_light_blue_off"
        android:padding="@dimen/clss_light_padding"
        android:layout_weight="1"
        android:scaleType="fitCenter"
        android:contentDescription=""
        app:offSrc="@drawable/clss_light_blue_off"
        app:onSrc="@drawable/clss_light_blue_on"/>

    <com.pax.commonui.clss.ClssLight
        android:id="@+id/light2"
        android:layout_width="0dp"
        android:layout_height="@dimen/clss_light_height"
        android:src="@drawable/clss_light_yellow_off"
        android:padding="@dimen/clss_light_padding"
        android:layout_weight="1"
        android:scaleType="fitCenter"
        android:contentDescription=""
        app:offSrc="@drawable/clss_light_yellow_off"
        app:onSrc="@drawable/clss_light_yellow_on"/>

    <com.pax.commonui.clss.ClssLight
        android:id="@+id/light3"
        android:layout_width="0dp"
        android:layout_height="@dimen/clss_light_height"
        android:src="@drawable/clss_light_green_off"
        android:padding="@dimen/clss_light_padding"
        android:layout_weight="1"
        android:scaleType="fitCenter"
        android:contentDescription=""
        app:offSrc="@drawable/clss_light_green_off"
        app:onSrc="@drawable/clss_light_green_on"/>

    <com.pax.commonui.clss.ClssLight
        android:id="@+id/light4"
        android:layout_width="0dp"
        android:layout_height="@dimen/clss_light_height"
        android:src="@drawable/clss_light_red_off"
        android:padding="@dimen/clss_light_padding"
        android:layout_weight="1"
        android:scaleType="fitCenter"
        android:contentDescription=""
        app:offSrc="@drawable/clss_light_red_off"
        app:onSrc="@drawable/clss_light_red_on"/>

</LinearLayout>
