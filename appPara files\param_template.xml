<?xml version="1.0" encoding="gb2312"?>
<Schema>
 <Groups>
	<Group> 
      <ID>sys_config</ID>
      <Title>Configs</Title> 
      <Order>1</Order> 
      <Description/>
    </Group> 
    <Group> 
      <ID>sys_emv</ID>
      <Title>EMV</Title> 
      <Order>2</Order> 
      <Description/>
    </Group> 
	<Group> 
      <ID>sys_security</ID>
      <Title>Security</Title> 
      <Order>3</Order> 
      <Description/>
    </Group>
 </Groups>
 <!-- Files Definition --> 
 <Files>
    <File> 
      <ID>sys_config</ID>
      <FileName>config.p</FileName>
      <Description/> 
    </File>
</Files>
<!-- Parameter Definition --> 
<Parameters>
<!-- Sub Applications --> 
  <Header>
    <Title>Merchant</Title> 
    <DisplayStyle>foldable</DisplayStyle> 
    <DefaultStyle>open</DefaultStyle> 
    <Display>true</Display>
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>EDC_MERCHANT_NAME_EN</PID>
      <Title>Merchant Name</Title> 
      <Length>1000</Length>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Merchant Name</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter> 
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>EDC_MERCHANT_ADDRESS</PID>
      <Title>Merchant Address</Title> 
      <Length>1000</Length>
      <Required>false</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Merchant address</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter> 
  </Header>
  
  <Header>
    <Title>Terminal</Title> 
    <DisplayStyle>foldable</DisplayStyle> 
    <DefaultStyle>open</DefaultStyle> 
    <Display>true</Display>
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>EDC_CATID</PID>
      <Title>Terminal ID (TID)</Title> 
      <Length>8</Length>
	  <MinLength>8</MinLength>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Terminal ID, must be unique for each terminal and provided by Fiserv. It must be registered in the Interchange "Terminals" table</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
	  <InputType>select</InputType>
      <PID>EDC_TYPE</PID>
      <Title>Terminal Type</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
	  <Select>{"M":"Main","S":"Secondary"}</Select>
      <Defaultvalue>M</Defaultvalue>
      <Description>Specifies whether the application will be main or secondary (will be called by another application on the same device)</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter> 
    <Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>String</DataType> 
      <PID>EDC_CLIENT</PID>
      <Title>Name Client</Title> 
      <Length>100</Length>
      <Required>false</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue></Defaultvalue>
      <Description>Must be updated with the name of the company that is using the application, currently there is only difference if we use the value "PitStop"</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>COMPLETION_SCREEN_DELAY</PID>
      <Title>Completion Screen Delay</Title> 
      <Length>100</Length>
	  <MinLength>1</MinLength>
      <Required>false</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>3000</Defaultvalue>
      <Description>waiting time in milliseconds used in the success or error screen after trying to transition, after this time the application will automatically return to the initial screen</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>AUTO_CANCEL_MAG_DELAY</PID>
      <Title>Auto-cancel Magnetic Delay</Title> 
      <Length>100</Length>
	  <MinLength>1</MinLength>
      <Required>false</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>60000</Defaultvalue>
      <Description>waiting time in milliseconds used in the magnetic card reading screen for automatic cancellation, after this time the application will automatically return to the initial screen</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>AUTO_CANCEL_PAY_DELAY</PID>
      <Title>Auto-cancel Payment Delay</Title> 
      <Length>100</Length>
	  <MinLength>1</MinLength>
      <Required>false</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>60000</Defaultvalue>
      <Description>waiting time in milliseconds used in the card reading screen for payment for automatic cancellation, after this time the application will automatically return to the initial screen</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>	
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>AUTO_CANCEL_RECEIPT_DELAY</PID>
      <Title>Auto-cancel Receipt Delay</Title> 
      <Length>100</Length>
	  <MinLength>1</MinLength>
      <Required>false</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>60000</Defaultvalue>
      <Description>waiting time in milliseconds used in the card reading screen for receipt for automatic cancellation, after this time the application will automatically return to the initial screen</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>	
	<Parameter>
      <Type>single</Type>
      <InputType>text</InputType>
      <DataType>Number</DataType> 
      <PID>ATTEMPTS_READ_CARD</PID>
      <Title>Number Attempts for Read Card</Title> 
      <Length>2</Length>
	  <MinLength>1</MinLength>
      <Required>true</Required> 
      <Readonly>false</Readonly> 
      <Defaultvalue>3</Defaultvalue>
      <Description>Number attempts for read card</Description> 
      <Display>true</Display> 
      <GroupID>sys_config</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>
  </Header>	
  <Header>
    <Title>Interchange</Title> 
    <DisplayStyle>foldable</DisplayStyle> 
    <DefaultStyle>open</DefaultStyle> 
    <Display>true</Display>
	<Parameter>
      <Type>single</Type>
      <InputType>upload</InputType>
      <PID>INTERCHANGE_HOSTS</PID>
      <Title>Interchange hosts</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly>
      <Description>Json with list of interchange servers that will be used in the transition attempt.</Description> 
      <Display>true</Display> 
      <GroupID>sys_emv</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>	
  </Header>	
  <Header>
    <Title>Security</Title> 
    <DisplayStyle>foldable</DisplayStyle> 
    <DefaultStyle>open</DefaultStyle> 
    <Display>true</Display>
	<Parameter>
      <Type>single</Type>
	  <InputType>select</InputType>
      <PID>ENCRYPTION_ENVIRONMENT</PID>
      <Title>Encryption Environment</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly> 
	  <Select>{"Test":"Test","Production":"Production"}</Select>
      <Defaultvalue>Production</Defaultvalue>
      <Description>Specifies the encryption model used in test environment or production environment</Description> 
      <Display>true</Display> 
      <GroupID>sys_security</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter> 
	<Parameter>
      <Type>single</Type>
      <InputType>upload</InputType>
      <PID>CACERT</PID>
      <Title>Cacert</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly>
      <Description>Base64 encoded "X.509" certificates used to communicate with the Interchange</Description> 
      <Display>true</Display> 
      <GroupID>sys_security</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>
	<Parameter>
      <Type>single</Type>
      <InputType>upload</InputType>
      <PID>ENCRYPTION_KEY</PID>
      <Title>Encryption Key</Title> 
      <Required>true</Required> 
      <Readonly>false</Readonly>
      <Description>DES encryption key used for Interchange login</Description> 
      <Display>true</Display> 
      <GroupID>sys_security</GroupID> 
      <FileID>sys_config</FileID>
    </Parameter>	
  </Header>
</Parameters>
</Schema>