/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                Author	               Action
 * 20210516 	        xieYb                  Create
 * ===========================================================================================
 *
 */
package com.pax.commonui.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.text.TextUtils;
import android.view.KeyEvent;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.application.BaseApplication;
import com.pax.commonui.dialog.CustomAlertDialog.OnCustomClickListener;
import com.pax.poslib.utils.PosDeviceUtils;

/**
 * The type Dialog utils.
 */
public class DialogUtils {

    private DialogUtils() {
        //do nothing
    }

    /**
     * 提示错误信息
     *
     * @param context  the context
     * @param title    the title
     * @param msg      the msg
     * @param listener the listener
     * @param timeout  the timeout
     */
    public static void showErrMessage(final Context context, final String title, final String msg,
                                      final OnDismissListener listener, final int timeout) {
        if (context == null) {
            return;
        }
        BaseApplication.getAppContext().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.ERROR_TYPE, true, timeout);
                if (context == ActivityStack.getInstance().top()) {
                    dialog.setTitleText(title);
                    dialog.setContentText(msg);
                    dialog.setCanceledOnTouchOutside(true);
                    dialog.show();
                    dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                        @Override
                        public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                            return keyCode == KeyEvent.KEYCODE_BACK;
                        }
                    });
                    dialog.setOnDismissListener(listener);
                    BaseApplication.getAppContext().runInBackground(PosDeviceUtils::beepErr);
                } else {
                    dialog.dismiss();
                }
            }
        });
    }

    /**
     * Show processing message custom alert dialog.
     *
     * @param context the context
     * @param title   the title
     * @param timeout the timeout
     * @return the custom alert dialog
     */
    public static CustomAlertDialog showProcessingMessage(final Context context, final String title, final int timeout) {
        if (context == null) {
            return null;
        }
        final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.PROGRESS_TYPE);

        BaseApplication.getAppContext().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                dialog.showContentText(false);
                dialog.setTitleText(title);
                dialog.setCanceledOnTouchOutside(true);
                dialog.setTimeout(timeout);
                dialog.show();
                dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {

                    @Override
                    public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                        return keyCode == KeyEvent.KEYCODE_BACK;
                    }
                });
            }
        });
        return dialog;
    }

    public static void showMessage(final Context context, final String title, final String msg,
                                   final OnDismissListener listener, final int timeout) {
        if (context == null) {
            return;
        }
        BaseApplication.getAppContext().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.SUCCESS_TYPE, true, timeout);
                if(!TextUtils.isEmpty(title)){
                    dialog.setTitleText(title);
                }
                if(!TextUtils.isEmpty(msg)){
                    dialog.setContentText(msg);
                }
                dialog.setCanceledOnTouchOutside(true);
                dialog.show();
                dialog.setOnKeyListener(new DialogInterface.OnKeyListener() {

                    @Override
                    public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                        return keyCode == KeyEvent.KEYCODE_BACK;
                    }
                });
                dialog.setOnDismissListener(listener);
                BaseApplication.getAppContext().runInBackground(PosDeviceUtils::beepOk);
            }
        });
    }

    /**
     * 退出当前应用
     *
     * @param context the context
     */
    public static void showExitAppDialog(final Context context,final String content) {
        showConfirmDialog(context, content, null, alertDialog -> {
            alertDialog.dismiss();
            PosDeviceUtils.enableStatusBar(true);
            PosDeviceUtils.enableHomeRecentKey(true);
            //new DefaultUriRequest(context, "app_Payment")
            //        .putExtra("EXIT",true)
            //        .setIntentFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
            //        .start();
            //android.os.Process.killProcess(android.os.Process.myPid());
            ActivityStack.getInstance().popAll(); //finish
        });
    }

    /**
     * Show confirm dialog.
     *
     * @param context              the context
     * @param content              the content
     * @param cancelClickListener  the cancel click listener
     * @param confirmClickListener the confirm click listener
     */
    public static void showConfirmDialog(final Context context, final String content,
                                         final OnCustomClickListener cancelClickListener, final OnCustomClickListener confirmClickListener) {
        final CustomAlertDialog dialog = new CustomAlertDialog(context, CustomAlertDialog.NORMAL_TYPE);

        final OnCustomClickListener clickListener = new OnCustomClickListener() {
            @Override
            public void onClick(CustomAlertDialog alertDialog) {
                alertDialog.dismiss();
            }
        };

        dialog.setCancelClickListener(cancelClickListener == null ? clickListener : cancelClickListener);
        dialog.setConfirmClickListener(confirmClickListener == null ? clickListener : confirmClickListener);
        dialog.show();
        dialog.setNormalText(content);
        dialog.showCancelButton(true);
        dialog.showConfirmButton(true);
    }

}
