<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/02/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<resources>
    <dimen name="text_normal_size">14sp</dimen>
    <dimen name="text_medium_size">16sp</dimen>
    <dimen name="text_large_size">20sp</dimen>

    <dimen name="divider_width">1dp</dimen>

    <dimen name="keyboard_button_inset">6dp</dimen>
    <dimen name="keyboard_button_padding">4dp</dimen>

    <dimen name="toolbar_content_to_screen_edge">@dimen/page_content_to_screen_edge</dimen>
    <dimen name="page_content_to_toolbar">16dp</dimen>
    <dimen name="page_content_to_screen_edge">16dp</dimen>
    <dimen name="page_content_to_screen_edge_half">8dp</dimen>
    <dimen name="page_content_horizontal_spacing">0dp</dimen>
    <dimen name="scrollable_page_content_to_screen_bottom">120dp</dimen>

    <dimen name="title_content_spacing_vertical_small">2dp</dimen>

    <dimen name="list_margin_vertical">8dp</dimen>
    <dimen name="list_padding_vertical">8dp</dimen>

    <dimen name="card_spacing">16dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_stroke_width">1dp</dimen>
    <dimen name="card_margin_horizontal">16dp</dimen>
    <dimen name="card_padding_vertical">12dp</dimen>
    <dimen name="card_padding_horizontal">16dp</dimen>
    <dimen name="card_title_size">18sp</dimen>
    <dimen name="card_content_spacing_vertical">12dp</dimen>
    <dimen name="card_content_spacing_vertical_large">24dp</dimen>
    <dimen name="card_content_spacing_horizontal">12dp</dimen>
    <dimen name="card_content_divider_spacing">12dp</dimen>

    <dimen name="icon_with_background_width">68dp</dimen>
    <dimen name="icon_with_background_height">48dp</dimen>
    <dimen name="icon_with_background_padding_vertical">8dp</dimen>
    <dimen name="icon_with_background_padding_horizontal">12dp</dimen>

    <dimen name="dialog_corner_radius">16dp</dimen>
    <dimen name="dialog_title_text">20sp</dimen>
    <dimen name="dialog_input_box_corner_radius">8dp</dimen>
    <dimen name="dialog_content_text">@dimen/text_normal_size</dimen>
    <dimen name="dialog_content_spacing_vertical">12dp</dimen>
    <dimen name="dialog_padding_vertical_large">32dp</dimen>
    <dimen name="dialog_padding_vertical">16dp</dimen>
    <dimen name="dialog_padding_vertical_small">8dp</dimen>
    <dimen name="dialog_padding_horizontal">24dp</dimen>
    <dimen name="dialog_icon_size">48dp</dimen>
    <dimen name="dialog_icon_title_spacing_vertical">12dp</dimen>

    <dimen name="toggle_button_inset_vertical">2dp</dimen>
    <dimen name="toggle_button_stroke_width">2dp</dimen>
    <dimen name="toggle_button_corner_radius">12dp</dimen>
    <dimen name="toggle_button_padding_horizontal">8dp</dimen>
    <dimen name="toggle_button_padding_vertical">2dp</dimen>

    <dimen name="text_button_min_width">64dp</dimen>
</resources>