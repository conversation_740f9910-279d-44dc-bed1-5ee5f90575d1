package com.visionpay.opt.pax.activity;

import android.app.PendingIntent;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.common.DefaultUriRequest;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.entity.IM30Transaction;
import com.visionpay.opt.pax.entity.POSTransaction;
import com.visionpay.opt.pax.entity.ProcessType;
import com.visionpay.opt.pax.message.BroadcastMesseger;
import com.visionpay.opt.pax.message.BroadcastMessegerListener;
import com.visionpay.opt.pax.thread.OnlineThread;
import com.visionpay.opt.pax.utils.ProcessTypeDeserializer;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MainActivity extends BaseActivity implements BroadcastMessegerListener {

    //private boolean wakeUp = false;
    ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_main);

        App.getApp().pendingIntent = PendingIntent.getActivity(
                getApplication().getBaseContext(),
                0,
                new Intent(getIntent()),
                0);

        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        String client = configParamService.getString(ConfigKeyConstant.EDC_CLIENT, "");
        if(client.toLowerCase(Locale.ROOT).equals("pitstop")) {
            ImageView imgLogo = findViewById(R.id.imgLogo);
            imgLogo.setImageResource(R.drawable.im30_home_screen);
            View cardIcons = findViewById(R.id.card_icons);
            cardIcons.setVisibility(View.GONE);
        }
    }

    @Override
    public void onResume(){
        super.onResume();
        hide();
        //if(!wakeUp) {
        BroadcastMesseger.getInstance().start(this);
            //TODO debug
            /*OnCommMessageReceive("{ " +
                    "\"externalDeviceToken\":\"1234\", " +
                    "\"externalReference\":\"1234\", " +
                    "\"transactionAmount\": 1000, " +
                    "\"transactionCurrency\": 36, " +
                    "\"processType\": 0 }");*/
        //}
        //else
        //    wakeUp = false;
    }

    /*private void wakeUp(){
        PowerManager pm = (PowerManager)getSystemService(POWER_SERVICE);

        if(!pm.isScreenOn()) {
            @SuppressLint("InvalidWakeLockTag")
            PowerManager.WakeLock wl = pm.newWakeLock(PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.SCREEN_DIM_WAKE_LOCK, "SimpleTimer");
            wl.acquire();
            wl.release();

            KeyguardManager keyguardManager = (KeyguardManager) getSystemService(KEYGUARD_SERVICE);
            KeyguardManager.KeyguardLock keyguardLock = keyguardManager.newKeyguardLock("");
            keyguardLock.disableKeyguard();

            wakeUp = true;
        }
    }*/

    @Override
    public void OnMessageReceive(String message) {
        //wakeUp();

        Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                .registerTypeAdapter(ProcessType.class, new ProcessTypeDeserializer()).create();

        List<POSTransaction> POSTransactionList = null;
        try {
            message = processMessage(message);
            Type listType = new TypeToken<List<POSTransaction>>(){}.getType();
            POSTransactionList = processTransactions(gson.fromJson(message, listType));
            for (POSTransaction POSTransaction : POSTransactionList) {
                if (POSTransaction.checkValues()) {

                    //Router.startUri(new DefaultUriRequest(this, EmvRouterConst.SEARCH_CARD)
                    //        .putExtra(BundleFieldConst.AMOUNT, POSTransaction.getTransactionAmount())
                    //        .putExtra(BundleFieldConst.TRANS_TYPE, ETransType.SALE.name()));
                    if (POSTransaction.getProcessType() == ProcessType.Payment) {
                        Router.startUri(new DefaultUriRequest(this, EmvRouterConst.READ_CARD)
                                .putExtra(BundleFieldConst.POSTRANSACTION, POSTransaction));
                        BroadcastMesseger.getInstance().stop();
                    } else if (POSTransaction.getProcessType() == ProcessType.Magnetic) {
                        Router.startUri(new DefaultUriRequest(this, EmvRouterConst.MAG_READ_CARD)
                                .putExtra(BundleFieldConst.POSTRANSACTION, POSTransaction));
                        BroadcastMesseger.getInstance().stop();
                    } else if (POSTransaction.getProcessType() == ProcessType.Capture || POSTransaction.getProcessType() == ProcessType.Reversal) {
                        //Router.startUri(new DefaultUriRequest(this, EmvRouterConst.TRANS_PROCESS)
                        //        .putExtra(BundleFieldConst.POSTRANSACTION, POSTransaction));
                        //UartComm.getInstance().stop();
                        executorService.execute(new OnlineThread(POSTransaction));
                    } else if (POSTransaction.getProcessType() == ProcessType.Receipt) {
                        Router.startUri(new DefaultUriRequest(this, EmvRouterConst.RECEIPT_READ_CARD)
                                .putExtra(BundleFieldConst.POSTRANSACTION, POSTransaction));
                        BroadcastMesseger.getInstance().stop();
                    } else if (POSTransaction.getProcessType() == ProcessType.Cancel) {

                    } else {
                        IM30Transaction im30t = new IM30Transaction(POSTransaction, "Incorrect Process Type.");
                        BroadcastMesseger.getInstance().send(gson.toJson(im30t));
                    }
                } else {
                    IM30Transaction im30t = new IM30Transaction(POSTransaction, "Incorrect values.");
                    BroadcastMesseger.getInstance().send(gson.toJson(im30t));
                }
            }
        }catch (Exception ex){
            IM30Transaction im30t = new IM30Transaction(POSTransactionList.get(0), ex.getMessage());
            BroadcastMesseger.getInstance().send(gson.toJson(im30t));
        }
    }

    private String processMessage(String message){
        message = message.replaceAll("\\p{Cntrl}", "").replace("}{", "},{");
        return "[" + message + "]";
    }

    private List<POSTransaction> processTransactions(List<POSTransaction> list){
        boolean added = false;

        List<POSTransaction> uniqueList = new ArrayList<>();

        for (POSTransaction objet : list) {
            if ((objet.getProcessType() == ProcessType.Payment
                    || objet.getProcessType() == ProcessType.Magnetic
                    || objet.getProcessType() == ProcessType.Receipt) && !added) {
                uniqueList.add(objet);
                added = true;
            } else if ((objet.getProcessType() == ProcessType.Payment
                    || objet.getProcessType() == ProcessType.Magnetic
                    || objet.getProcessType() == ProcessType.Receipt) && added) {

            } else{
                uniqueList.add(objet);
            }
        }

        Collections.sort(uniqueList, new Comparator<POSTransaction>() {
            @Override
            public int compare(POSTransaction o1, POSTransaction o2) {
                return Integer.compare(o2.getProcessType().getValue(), o1.getProcessType().getValue());
            }
        });
        return uniqueList;
    }
}