<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/02/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="AppTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/main</item>
        <item name="colorPrimaryVariant">@color/main_dark</item>
        <item name="colorOnPrimary">@color/main_text</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/main_accent</item>
        <item name="colorSecondaryVariant">@color/main_accent_dark</item>
        <item name="colorOnSecondary">@color/main_text</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">@color/main_background</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@null</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:navigationBarColor">@color/main_background</item>
        <item name="android:navigationBarDividerColor">@color/common_divider</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>

    <style name="MainBgStatusBarColor" parent="AppTheme">
        <item name="android:statusBarColor" tools:targetApi="l">@color/main_background</item>
    </style>

    <style name="MainBgStatusBarColorAndWhiteNavBar" parent="MainBgStatusBarColor">
        <item name="android:navigationBarColor">@color/main_background</item>
    </style>

    <style name="TransBgTheme" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/splash_icon</item>
        <item name="android:statusBarColor">@color/main_background</item>
        <item name="android:navigationBarColor">@color/main_background</item>
    </style>
</resources>