<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/02/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/main</item>
        <item name="colorPrimaryVariant">@color/main_dark</item>
        <item name="colorOnPrimary">@color/main_text</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/main_accent</item>
        <item name="colorSecondaryVariant">@color/main_accent_dark</item>
        <item name="colorOnSecondary">@color/main_text</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@null</item>
    </style>

    <style name="MainBgStatusBarColor" parent="AppTheme">
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
    </style>

    <style name="MainBgStatusBarColorAndWhiteNavBar" parent="MainBgStatusBarColor">
        <item name="android:navigationBarColor">?attr/colorPrimaryVariant</item>
    </style>

    <style name="TransBgTheme" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@drawable/splash_icon</item>
    </style>

    <style name="ConfigUI.Toolbar.SubTitleText" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
        <item name="android:textSize">12sp</item>
    </style>

    <style name="ThemeOverlay.OPTPAX.FullscreenContainer" parent="">
        <item name="fullscreenBackgroundColor">@color/white</item>
        <item name="fullscreenTextColor">@color/main</item>
    </style>

    <style name="Theme.OPTPAX.Fullscreen" parent="AppTheme">
        <item name="android:actionBarStyle">@style/Widget.Theme.OPTPAX.ActionBar.Fullscreen</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
    </style>

    <style name="Widget.Theme.OPTPAX.ActionBar.Fullscreen" parent="Widget.AppCompat.ActionBar">
        <item name="android:background">@color/black</item>
    </style>

    <style name="PinPadNumber" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/btn_bg_pinpad_number</item>
        <item name="android:textColor">@color/main</item>
        <item name="android:textSize">40dp</item>
    </style>

    <style name="PinPadClear" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/btn_bg_pinpad_clear</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20dp</item>
    </style>

    <style name="PinPadEnter" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/btn_bg_pinpad_enter</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">20dp</item>
    </style>

    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>

    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>
</resources>