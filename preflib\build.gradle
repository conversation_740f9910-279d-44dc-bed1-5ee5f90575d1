plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        release {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        stagingRelease {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        autoTest{
            buildConfigField('boolean','RELEASE','false')
        }
        debug{
            buildConfig<PERSON>ield('boolean','RELEASE','false')
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    flatDir {
        dirs '../poslib/libs', '../commonui/libs'
    }
    mavenCentral()
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api "androidx.preference:preference:$rootProject.preference"
    api "com.google.android.material:material:$rootProject.material"

    api project(':commonlib')
    implementation project(path: ':commonui')

    annotationProcessor "io.github.meituan-dianping:compiler:$rootProject.routerCompiler"

    testImplementation "junit:junit:$rootProject.junit"
    testImplementation "org.mockito:mockito-all:$rootProject.mockito"
    testImplementation "org.hamcrest:hamcrest-all:$rootProject.hamcrest"
    testImplementation "androidx.arch.core:core-testing:$rootProject.coreTesting"
    androidTestImplementation "androidx.test:runner:$rootProject.runner"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    androidTestImplementation "junit:junit:$rootProject.junit"
}