/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/07                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.consts;

/**
 * Config UI Preference Key Constants
 */
public class ConfigUIKeyConst {

    public static final String EMV_CONTACT_CATEGORY = "EMV_CONTACT_CATEGORY";
    public static final String EMV_CONTACT_AID_LIST = "EMV_CONTACT_AID_LIST";

    public static final String EMV_CONTACTLESS_CATEGORY = "EMV_CONTACTLESS_CATEGORY";
    public static final String EMV_CONTACTLESS_AMEX_LIST = "EMV_CONTACTLESS_AMEX_LIST";
    public static final String EMV_CONTACTLESS_AMEX_AID_CATEGORY = "EMV_CONTACTLESS_AMEX_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_AMEX_DRL_CATEGORY = "EMV_CONTACTLESS_AMEX_DRL_CATEGORY";
    public static final String EMV_CONTACTLESS_DPAS_LIST = "EMV_CONTACTLESS_DPAS_LIST";
    public static final String EMV_CONTACTLESS_DPAS_AID_CATEGORY = "EMV_CONTACTLESS_DPAS_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_EFT_LIST = "EMV_CONTACTLESS_EFT_LIST";
    public static final String EMV_CONTACTLESS_EFT_AID_CATEGORY = "EMV_CONTACTLESS_EFT_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_JCB_LIST = "EMV_CONTACTLESS_JCB_LIST";
    public static final String EMV_CONTACTLESS_JCB_AID_CATEGORY = "EMV_CONTACTLESS_JCB_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_MC_LIST = "EMV_CONTACTLESS_MC_LIST";
    public static final String EMV_CONTACTLESS_MC_AID_CATEGORY = "EMV_CONTACTLESS_MC_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_MIR_LIST = "EMV_CONTACTLESS_MIR_LIST";
    public static final String EMV_CONTACTLESS_MIR_AID_CATEGORY = "EMV_CONTACTLESS_MIR_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_PAYWAVE_LIST = "EMV_CONTACTLESS_PAYWAVE_LIST";
    public static final String EMV_CONTACTLESS_PAYWAVE_AID_CATEGORY = "EMV_CONTACTLESS_PAYWAVE_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_PAYWAVE_PROGRAM_ID_CATEGORY = "EMV_CONTACTLESS_PAYWAVE_PROGRAM_ID_CATEGORY";
    public static final String EMV_CONTACTLESS_PAYWAVE_LIMIT_CATEGORY = "EMV_CONTACTLESS_PAYWAVE_LIMIT_CATEGORY";
    public static final String EMV_CONTACTLESS_PBOC_LIST = "EMV_CONTACTLESS_PBOC_LIST";
    public static final String EMV_CONTACTLESS_PBOC_AID_CATEGORY = "EMV_CONTACTLESS_PBOC_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_PURE_LIST = "EMV_CONTACtLESS_PURE_LIST";
    public static final String EMV_CONTACTLESS_PURE_AID_CATEGORY = "EMV_CONTACTLESS_PURE_AID_CATEGORY";
    public static final String EMV_CONTACTLESS_RUPAY_LIST = "EMV_CONTACTLESS_RUPAY_LIST";
    public static final String EMV_CONTACTLESS_RUPAY_AID_CATEGORY = "EMV_CONTACTLESS_RUPAY_AID_CATEGORY";

    public static final String ABOUT_VERSION = "ABOUT_VERSION";

    private ConfigUIKeyConst() {
        // do nothing
    }
}
