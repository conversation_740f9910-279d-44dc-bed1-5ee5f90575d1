package com.visionpay.opt.pax.emv.online.interchange.messages;

import androidx.annotation.NonNull;

import com.pax.commonlib.utils.ConvertUtils;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageSubtype;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageType;
import com.visionpay.opt.pax.emv.online.interchange.enums.MessageDirection;
import com.visionpay.opt.pax.emv.online.interchange.enums.MessageSource;

import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

public class Message extends BaseMessage {
    protected byte[] mMessage;
    public InterchangeMessageType mMessageType;
    public InterchangeMessageSubtype mMessageSubType;
    public long mRefNo;
    public Date mDate;
    String mTerminalID;

    public Message(InterchangeMessageType type, InterchangeMessageSubtype subType, long refNo, String terminalID){
        mSource = MessageSource.INTERCHANGE;
        mDirection = MessageDirection.OUT;
        mMessageType = type;
        mMessageSubType = subType;
        mRefNo = refNo;
        mTerminalID = terminalID;
        while (mTerminalID.length() < 10)
            mTerminalID = mTerminalID + " ";
    }

    public Message(byte[] buffer){
        mSource = MessageSource.INTERCHANGE;
        mDirection = MessageDirection.IN;
        if (buffer.length > 2){
            mMessage = new byte[buffer.length - 2];
            System.arraycopy(buffer,2, mMessage, 0, buffer.length - 2);
        }
    }

    public boolean parse(){
        if (this.mMessage.length > 0) {
            int i = 0;
            internalParse(i);
            return true;
        }
        else {
            return false;
        }
    }

    @NonNull
    @Override
    public String toString(){
        byte[] buffer;
        if (this.mDirection == MessageDirection.OUT){
            buffer = getMessage();
        }
        else{
            buffer = this.mMessage;
        }

        /*char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();
        char[] hexChars = new char[buffer.length * 2];
        for (int j = 0; j < buffer.length; j++) {
            int v = buffer[j] & 0xFF;
            hexChars[j * 2] = HEX_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }

        return super.toString() + "\t" + new String(hexChars);*/
        return super.toString() + "\t" + ConvertUtils.bcd2Str(buffer);
    }

    public byte[] getMessage(){
        byte[] message = internalGetMessage();
        int length = message.length + 2;
        byte[] ret = new byte[length];
        System.arraycopy(message,0, ret, 2, message.length);
        ret[1] = (byte)((length >> 0) & 0xFF);
        ret[0] = (byte)((length >> 8) & 0xFF);
        return ret;
    }

    protected byte[] internalGetMessage(){

        Calendar calendar = Calendar.getInstance();

        ByteArrayOutputStream message = new ByteArrayOutputStream();
        message.write(Message.this.mMessageType.value);
        message.write(Message.this.mMessageSubType.value);
        message.write((byte) ((Message.this.mRefNo >> 56) & 0xFF));
        message.write((byte) ((Message.this.mRefNo >> 48) & 0xFF));
        message.write((byte) ((Message.this.mRefNo >> 40) & 0xFF));
        message.write((byte) ((Message.this.mRefNo >> 32) & 0xFF));
        message.write((byte) ((Message.this.mRefNo >> 24) & 0xFF));
        message.write((byte) ((Message.this.mRefNo >> 16) & 0xFF));
        message.write((byte) ((Message.this.mRefNo >> 8) & 0xFF));
        message.write((byte) ((Message.this.mRefNo) & 0xFF));

        message.write(calendar.get(Calendar.YEAR) % 100);
        message.write((calendar.get(Calendar.MONTH) + 1));
        message.write((calendar.get(Calendar.DAY_OF_MONTH)));
        message.write((calendar.get(Calendar.HOUR_OF_DAY)));
        message.write((calendar.get(Calendar.MINUTE)));
        message.write((calendar.get(Calendar.SECOND)));

        message.write(this.mTerminalID.getBytes(), 0, this.mTerminalID.length());

        return message.toByteArray();
    }

    protected int internalParse(int i){
        mMessageType = InterchangeMessageType.get(mMessage[i++]);
        mMessageSubType = InterchangeMessageSubtype.get(mMessage[i++]);
        //mRefNo = ((long) mMessage[i++] << 56);
        //mRefNo += ((long) mMessage[i++] << 48);
        //mRefNo += ((long) mMessage[i++] << 40);
        //mRefNo += ((long) mMessage[i++] << 32);
        //mRefNo += (mMessage[i++] << 24);
        //mRefNo += (mMessage[i++] << 16);
        //mRefNo += (mMessage[i++] << 8);
        //mRefNo += (mMessage[i++]);
        mRefNo = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 8));
        i += 8;

        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, mMessage[i++]);
        cal.set(Calendar.MONTH, mMessage[i++] - 1);
        cal.set(Calendar.DAY_OF_MONTH, mMessage[i++]);
        cal.set(Calendar.HOUR_OF_DAY, mMessage[i++]);
        cal.set(Calendar.MINUTE, mMessage[i++]);
        cal.set(Calendar.SECOND, mMessage[i++]);
        mDate = cal.getTime();
        return i;
    }
}
