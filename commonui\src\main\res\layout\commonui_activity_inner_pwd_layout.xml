<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentBottom="true"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:id="@+id/prompt_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/commonui_space_vertical_small"
        android:layout_marginStart="@dimen/commonui_space_horizontal"
        android:layout_marginEnd="@dimen/commonui_space_horizontal"
        android:gravity="center"
        android:textColor="@color/commonui_primary_text_light"
        android:textSize="@dimen/commonui_font_size_prompt" />

    <TextView
        android:id="@+id/pwd_input_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/commonui_space_horizontal"
        android:layout_marginEnd="@dimen/commonui_space_horizontal"
        android:layout_marginTop="@dimen/commonui_space_vertical_small"
        android:background="@drawable/commonui_edit_frame"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:textColor="@color/commonui_primary_text_light"
        android:textSize="@dimen/commonui_font_edit_text" />

    <EditText
        android:id="@+id/pwd_input_et"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/commonui_space_horizontal"
        android:layout_marginEnd="@dimen/commonui_space_horizontal"
        android:layout_marginTop="@dimen/commonui_space_vertical_small"
        android:layout_marginBottom="@dimen/commonui_space_vertical_small"
        android:background="@drawable/commonui_edit_frame"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:inputType="numberPassword"
        android:textColor="@color/commonui_primary_text_light"
        android:textSize="@dimen/commonui_font_edit_text"
        android:visibility="visible"
        android:hint="" />

    <TextView
        android:id="@+id/prompt_no_pwd"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginBottom="@dimen/commonui_space_vertical_small"
        android:textColor="@color/commonui_secondary_text_light"
        android:textSize="@dimen/commonui_font_size_hint" />

    <com.pax.commonui.keyboard.CustomKeyboardView
        style="@style/commonuiCustomKeyboardView"
        android:id="@+id/pwd_keyboard"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</LinearLayout>