/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/26                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.factory.impl;

import android.content.Context;
import android.os.Bundle;
import android.text.InputType;
import android.text.method.DigitsKeyListener;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.preference.EditTextPreference;
import androidx.preference.PreferenceDialogFragmentCompat;
import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.commonlib.utils.KeyboardUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonui.text.EnterAmountTextWatcher;
import com.pax.preflib.R;
import com.pax.preflib.builder.callback.InputValidateCallback;

/**
 * DialogFragment corresponding to AmountEditTextDialogPreference
 */
public class AmountEditTextDialog extends PreferenceDialogFragmentCompat {
    private static final String TAG = "AmountEditTextDialog";
    private static final String SAVE_STATE_AMOUNT = "AmountEditTextDialog.amount";

    private AppCompatEditText amountEditText;
    private EditTextPreference.OnBindEditTextListener onBindEditTextListener;
    private InputValidateCallback<Long> validateCallback;

    private long amount = 0;

    public static AmountEditTextDialog newInstance(String key) {
        AmountEditTextDialog dialog = new AmountEditTextDialog();
        Bundle bundle = new Bundle(1);
        bundle.putString(ARG_KEY, key);
        dialog.setArguments(bundle);
        return dialog;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (savedInstanceState == null) {
            amount = getAmountEditTextDialogPreference().getAmount();
        } else {
            amount = savedInstanceState.getLong(SAVE_STATE_AMOUNT, 0);
        }
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putLong(SAVE_STATE_AMOUNT, amount);
    }

    @Override
    protected View onCreateDialogView(Context context) {
        return LayoutInflater.from(context).inflate(R.layout.pref_dialog_input_amt, null);
    }

    @Override
    protected void onBindDialogView(View view) {
        super.onBindDialogView(view);

        amountEditText = view.findViewById(R.id.amountEditText);

        EnterAmountTextWatcher watcherFloorLimit = new EnterAmountTextWatcher();
        watcherFloorLimit.setMaxValue(99999999L);
        watcherFloorLimit.setOnTipListener(new EnterAmountTextWatcher.OnTipListener() {
            @Override
            public void onUpdateTipListener(long baseAmount, long tipAmount) {
                amount = tipAmount;
            }

            @Override
            public boolean onVerifyTipListener(long baseAmount, long tipAmount) {
                if (validateCallback != null) {
                    return validateCallback.onChanged(tipAmount);
                }
                return true;
            }

            @Override
            public boolean onBackwardListener(long baseAmount, long tipAmount) {
                if (validateCallback != null) {
                    return validateCallback.onChanged(tipAmount);
                }
                return true;
            }
        });
        watcherFloorLimit.setAmount(0, amount);
        amountEditText.addTextChangedListener(watcherFloorLimit);

        amountEditText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_NONE || actionId == EditorInfo.IME_ACTION_DONE) {
                onDialogClosed(true);
                dismiss();
            }
            return false;
        });
        amountEditText.setKeyListener(DigitsKeyListener.getInstance("1234567890"));
        amountEditText.setInputType(InputType.TYPE_CLASS_TEXT|InputType.TYPE_TEXT_FLAG_NO_SUGGESTIONS);
        amountEditText.setRawInputType(InputType.TYPE_CLASS_NUMBER|InputType.TYPE_NUMBER_FLAG_DECIMAL);
        amountEditText.setText(CurrencyConverter.convert(amount));
        amountEditText.requestFocus();
        amountEditText.setOnClickListener(v -> {
            if (amountEditText.getText() != null) {
                amountEditText.setSelection(amountEditText.getText().length());
            }
        });
        amountEditText.setLongClickable(false);
        amountEditText.setTextIsSelectable(false);

        if (onBindEditTextListener != null) {
            onBindEditTextListener.onBindEditText(amountEditText);
        }

        amountEditText.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                LogUtils.d(TAG, "onViewAttachedToWindow");
                BaseApplication.getAppContext().runOnUiThreadDelay(() -> KeyboardUtils.showSystemKeyboard(amountEditText), 100);
                if (amountEditText.getText() != null) {
                    amountEditText.setSelection(amountEditText.getText().length());
                }
            }

            @Override
            public void onViewDetachedFromWindow(View v) {
                // do nothing
            }
        });
    }

    private AmountEditTextDialogPreference getAmountEditTextDialogPreference() {
        return (AmountEditTextDialogPreference) getPreference();
    }

    @Override
    public void onDialogClosed(boolean positiveResult) {
        LogUtils.d(TAG, "onDialogClosed");
        KeyboardUtils.hideSystemKeyboard(amountEditText);
        if (positiveResult) {
            final AmountEditTextDialogPreference preference = getAmountEditTextDialogPreference();
            if (preference != null && preference.callChangeListener(amount)) {
                // This value can be saved 这个值可以保存
                if (amountEditText != null) {
                    preference.setAmount(amount);
                }
            }
        }
    }

    /**
     * Set OnBindEditTextListener for EditText
     *
     * @param onBindEditTextListener OnBindEditTextListener
     * @return This dialog
     */
    public AmountEditTextDialog setOnBindEditTextListener(EditTextPreference.OnBindEditTextListener onBindEditTextListener) {
        this.onBindEditTextListener = onBindEditTextListener;
        return this;
    }

    /**
     * Set InputValidateCallback for EnterAmountTextWatcher
     *
     * @param validateCallback InputValidateCallback
     * @return This dialog
     */
    public AmountEditTextDialog setValidateCallback(InputValidateCallback<Long> validateCallback) {
        this.validateCallback = validateCallback;
        return this;
    }
}
