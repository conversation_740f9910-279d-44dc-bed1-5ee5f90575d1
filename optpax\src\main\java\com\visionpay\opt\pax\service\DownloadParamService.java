package com.visionpay.opt.pax.service;
import android.app.IntentService;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;

import com.pax.commonlib.apppara.AppParaLoader;
import com.pax.market.android.app.sdk.StoreSdk;
import com.pax.market.android.app.sdk.util.NotificationUtils;
import com.pax.market.api.sdk.java.base.constant.ResultCode;
import com.pax.market.api.sdk.java.base.dto.DownloadResultObject;
import com.pax.market.api.sdk.java.base.exception.NotInitException;
import com.visionpay.opt.pax.BuildConfig;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.DownloadParamConst;
import com.visionpay.opt.pax.utils.SPUtil;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DownloadParamService extends IntentService {

    private static final String TAG = DownloadParamService.class.getSimpleName();
    public static String saveFilePath;
    private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    private SPUtil spUtil;

    public DownloadParamService() {
        super("DownloadParamService");
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    protected void onHandleIntent(@Nullable Intent intent) {
        spUtil = new SPUtil();
        //todo Specifies the download path for the parameter file, you can replace the path to your app's internal storage for security.
        saveFilePath = AppParaLoader.getPath();//getFilesDir() + "/YourPath/";

        //show downloading info in main page
        updateUI(DownloadParamConst.DOWNLOAD_STATUS_START);

        //todo Call SDK API to download parameter files into your specific directory,
        DownloadResultObject downloadResult = null;
        try {
            Log.i(TAG, "call sdk API to download parameter");
            downloadResult = StoreSdk.getInstance().paramApi().downloadParamToPath(getApplication().getPackageName(), BuildConfig.VERSION_CODE, saveFilePath);
            Log.i(TAG, downloadResult.toString());
        } catch (NotInitException e) {
            Log.e(TAG, "e:" + e);
        }

//                businesscode==0, means download successful, if not equal to 0, please check the return message when need.
        if (downloadResult != null ) {
            if (downloadResult.getBusinessCode() == ResultCode.SUCCESS.getCode()) {
                Log.i(TAG, "download successful.");
            } else {
                //todo check the Error Code and Error Message for fail reason
                Log.e(TAG, "ErrorCode: " + downloadResult.getBusinessCode() + "ErrorMessage: " + downloadResult.getMessage());
                //update download fail info in main page for Demo
                spUtil.setString(DownloadParamConst.PUSH_RESULT_BANNER_TITLE, DownloadParamConst.DOWNLOAD_FAILED);
                spUtil.setString(DownloadParamConst.PUSH_RESULT_BANNER_TEXT, "Your push parameters file task failed at " + sdf.format(new Date()) + ", please check error log.");
                updateUI(DownloadParamConst.DOWNLOAD_STATUS_FAILED);
            }
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        NotificationUtils.showForeGround(this, R.mipmap.ic_launcher_round, "Downloading params");
        return super.onStartCommand(intent, flags, startId);
    }

    /**
     * notify MainActivity to display downloaded files, just for demo display
     */
    private void updateUI(int stateCode) {
        Intent intent = new Intent(DownloadParamConst.UPDATE_VIEW_ACTION);
        intent.putExtra(DownloadParamConst.DOWNLOAD_RESULT_CODE, stateCode);
        sendBroadcast(intent);
    }

}
