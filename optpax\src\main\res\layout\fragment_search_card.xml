<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/trans_prompt_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/card_margin_horizontal"
    android:layout_marginEnd="@dimen/card_margin_horizontal"
    android:layout_marginTop="@dimen/card_spacing"
    app:cardElevation="0dp"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:strokeWidth="@dimen/card_stroke_width"
    app:strokeColor="@color/common_divider"
    app:cardBackgroundColor="@color/main_background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/card_padding_vertical"
        android:paddingHorizontal="@dimen/card_padding_horizontal">

        <TextView
            android:id="@+id/trans_prompt_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/prompt_insert_wave_card"
            android:textColor="@color/main_text"
            android:textSize="@dimen/card_title_size"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.pax.commonui.clss.ClssLightsView
            android:id="@+id/trans_clss_light"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/card_content_spacing_vertical_large"
            android:layout_marginStart="@dimen/page_content_to_screen_edge"
            android:layout_marginEnd="@dimen/page_content_to_screen_edge"
            app:layout_constraintTop_toBottomOf="@id/trans_prompt_text" />

        <ImageView
            android:id="@+id/trans_prompt_insert_icon"
            android:layout_width="@dimen/icon_with_background_width"
            android:layout_height="@dimen/icon_with_background_height"
            android:layout_marginTop="@dimen/card_content_spacing_vertical_large"
            android:paddingVertical="@dimen/icon_with_background_padding_vertical"
            android:paddingHorizontal="@dimen/icon_with_background_padding_horizontal"
            android:src="@drawable/ic_insert_card"
            android:background="@drawable/bg_search_card_icon"
            app:layout_constraintTop_toBottomOf="@id/trans_clss_light"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/trans_prompt_insert_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/card_content_spacing_horizontal"
            android:text="@string/insert_card_title"
            android:textSize="@dimen/text_medium_size"
            android:textColor="@color/main_text"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintStart_toEndOf="@id/trans_prompt_insert_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/trans_prompt_insert_icon" />

        <TextView
            android:id="@+id/trans_prompt_insert_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/card_content_spacing_horizontal"
            android:layout_marginTop="@dimen/title_content_spacing_vertical_small"
            android:textSize="@dimen/text_normal_size"
            android:textColor="@color/main_text"
            app:layout_constraintStart_toEndOf="@id/trans_prompt_insert_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/trans_prompt_insert_title"
            tools:text="Disabled, because this device not support" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/trans_prompt_insert_barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="trans_prompt_insert_icon,trans_prompt_insert_info" />

        <ImageView
            android:id="@+id/trans_prompt_tap_icon"
            android:layout_width="@dimen/icon_with_background_width"
            android:layout_height="@dimen/icon_with_background_height"
            android:layout_marginTop="@dimen/card_content_spacing_vertical"
            android:paddingVertical="@dimen/icon_with_background_padding_vertical"
            android:paddingHorizontal="@dimen/icon_with_background_padding_horizontal"
            android:src="@drawable/ic_tap_card"
            android:background="@drawable/bg_search_card_icon"
            app:layout_constraintTop_toBottomOf="@id/trans_prompt_insert_barrier"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/trans_prompt_tap_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/card_content_spacing_horizontal"
            android:text="@string/tap_card_title"
            android:textSize="@dimen/text_medium_size"
            android:textColor="@color/main_text"
            android:fontFamily="sans-serif-medium"
            app:layout_constraintStart_toEndOf="@id/trans_prompt_tap_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/trans_prompt_tap_icon" />

        <TextView
            android:id="@+id/trans_prompt_tap_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/card_content_spacing_horizontal"
            android:layout_marginTop="@dimen/title_content_spacing_vertical_small"
            android:textSize="@dimen/text_normal_size"
            android:textColor="@color/main_text"
            app:layout_constraintStart_toEndOf="@id/trans_prompt_tap_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/trans_prompt_tap_title"
            tools:text="Disabled, because this device not support" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>