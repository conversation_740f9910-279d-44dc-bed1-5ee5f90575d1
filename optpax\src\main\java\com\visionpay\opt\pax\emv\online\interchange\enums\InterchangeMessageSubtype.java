package com.visionpay.opt.pax.emv.online.interchange.enums;

public enum InterchangeMessageSubtype {
    SMT_LOGON1((byte)0x01),
    SMT_LOGON2((byte)0x02),
    SMT_INIT1((byte)0x01),
    SMT_PREAUTH((byte)0x02),
    SMT_CAPTURE((byte)0x03),
    SMT_REVERSE((byte)0x04);

    public final byte value;

    InterchangeMessageSubtype(byte i) {
        this.value = i;
    }

    public static InterchangeMessageSubtype get(int value)
    {
        InterchangeMessageSubtype[] As = InterchangeMessageSubtype.values();
        for(int i = 0; i < As.length; i++)
        {
            if(As[i].value == value)
                return As[i];
        }
        return null;
    }
}
