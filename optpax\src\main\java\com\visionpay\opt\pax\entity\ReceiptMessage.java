package com.visionpay.opt.pax.entity;

import androidx.annotation.Keep;

import com.visionpay.opt.pax.utils.DateUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

@Keep
public class ReceiptMessage {
    private Date createdUTCDateTime;
    private Date completedUTCDateTime;
    private ReadState receiptState;
    private String cardSignature;

    public ReceiptMessage(){
        createdUTCDateTime = DateUtils.gettUtcTime(new Date());
        completedUTCDateTime = DateUtils.gettUtcTime(new Date());
        receiptState = ReadState.AwaitingCardRead;
        cardSignature = "";
    }
    public ReadState getReceiptState() {
        return receiptState;
    }

    public void setReceiptState(ReadState magneticState) {
        this.receiptState = magneticState;
        completedUTCDateTime = DateUtils.gettUtcTime(new Date());
    }

    public Date getCreatedUTCDateTime() {
        return createdUTCDateTime;
    }

    public void setCreatedUTCDateTime(Date createdUTCDateTime) {
        this.createdUTCDateTime = createdUTCDateTime;
    }

    public Date getCompletedUTCDateTime() {
        return completedUTCDateTime;
    }

    public void setCompletedUTCDateTime(Date completedUTCDateTime) {
        this.completedUTCDateTime = completedUTCDateTime;
    }

    public String getCardSignature() {
        return cardSignature;
    }

    public void setCardSignature(String cardSignature) {
        this.cardSignature = cardSignature;
    }
}

