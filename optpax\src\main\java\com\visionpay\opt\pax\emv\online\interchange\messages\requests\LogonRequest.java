package com.visionpay.opt.pax.emv.online.interchange.messages.requests;

import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageSubtype;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageType;
import com.visionpay.opt.pax.emv.online.interchange.messages.Message;

public class LogonRequest extends Message {
    private final byte[] RND;

    @Override
    protected byte[] internalGetMessage(){
        byte[] message = super.internalGetMessage();
        byte[] ret = new byte[message.length + RND.length];
        System.arraycopy(message,0, ret, 0, message.length);
        System.arraycopy(RND,0, ret, message.length, RND.length);
        return ret;
    }

    public LogonRequest(String CATID, long refNo, byte[] RND, InterchangeMessageSubtype stage) {
        super(InterchangeMessageType.IMT_LOGON, stage, refNo, CATID);
        this.RND = RND;
    }
}