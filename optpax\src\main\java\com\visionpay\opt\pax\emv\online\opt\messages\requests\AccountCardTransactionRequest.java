package com.visionpay.opt.pax.emv.online.opt.messages.requests;

import androidx.annotation.Keep;

@Keep
public class AccountCardTransactionRequest {
    private String terminalID;
    private long transactionReferece;
    private long stan;
    private long amount;

    public String getTerminalID() {
        return terminalID;
    }

    public void setTerminalID(String terminalID) {
        this.terminalID = terminalID;
    }

    public long getTransactionReferece() {
        return transactionReferece;
    }

    public void setTransactionReferece(long transactionReferece) {
        this.transactionReferece = transactionReferece;
    }

    public long getSTAN() {
        return stan;
    }

    public void setSTAN(long stan) {
        this.stan = stan;
    }

    public long getAmount() {
        return amount;
    }

    public void setAmount(long amount) {
        this.amount = amount;
    }
}
