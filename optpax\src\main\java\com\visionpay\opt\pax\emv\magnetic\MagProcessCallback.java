package com.visionpay.opt.pax.emv.magnetic;

import android.os.ConditionVariable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.pax.bizentity.entity.Issuer;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.pax.emvbase.process.magnetic.IMagCallback;
import com.pax.emvservice.export.IEmvMagService;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.emv.IEmvProcessCallback;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.online.OnlineTaskFactory;
import com.visionpay.opt.pax.emv.pin.IPinTask;
import com.visionpay.opt.pax.emv.pin.OfflinePinTask;
import com.visionpay.opt.pax.emv.pin.OnlinePinTask;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EIssuer;
import com.visionpay.opt.pax.utils.DialogUtils;

import java.util.List;

@WorkerThread
public class MagProcessCallback implements IMagCallback, IEmvProcessCallback {
    private static final String TAG = "MagProcessCallback";

    private final IEmvMagService emv;
    private final ConditionVariable cv;
    private IEmvService.ConfirmCallback confirmCallback;
    private IEmvService.ErrorCallback errorCallback;
    private List<CvmType> cvmTypeList;

    public MagProcessCallback(@NonNull IEmvMagService emv, @NonNull ConditionVariable cv) {
        this.emv = emv;
        this.cv = cv;
    }

    @Override
    public int onCardHolderPwd(boolean isOnlinePin, boolean supportPINByPass, int leftTimes,
            byte[] pinData) {
        LogUtils.d(TAG, "============ PIN Input Start ============");
        DialogUtils.dismiss();

        IPinTask pinTask;
        if (isOnlinePin) {
            pinTask = new OnlinePinTask((title, reason) -> errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_online_pin)));
            }
        } else {
            pinTask = new OfflinePinTask(leftTimes, (title, reason) ->
                    errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_offline_pin)));
            }
        }

        int ret = pinTask.start(emv, supportPINByPass, emv.getTlv(0x100001).length != 0);
        LogUtils.d(TAG, "Pin Task ret: " + ret);
        LogUtils.d(TAG, "============ PIN Input End ============");
        return ret;
    }

    @Override
    public int showConfirmCard() {
        LogUtils.d(TAG, "============ Confirm Card Start ============");

        Issuer matchedIssuer = emv.getMatchedIssuerByPan();

        if (confirmCallback != null) {
            if (matchedIssuer != null) {
                String name = matchedIssuer.getName();
                EIssuer info = EIssuer.parse(name);
                if (info != null) {
                    confirmCallback.onConfirm(emv.getPan(),
                            info.getIssuerIconRes(),
                            info.getDisplayName(),
                            getCardHolderName(), null, "", "", "", emv.getExpireDate(), "", 0,
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                } else {
                    confirmCallback.onConfirm(emv.getPan(),
                            R.drawable.ic_issuer_other, name,
                            getCardHolderName(), null, "", "", "", emv.getExpireDate(), "", 0,
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                }
            } else {
                confirmCallback.onConfirm(emv.getPan(), R.drawable.ic_issuer_other,
                        "Unknown",
                        getCardHolderName(), null, "", "", "", emv.getExpireDate(), "", 0,
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            }
        }
        LogUtils.d(TAG, "============ Confirm Card End ============");
        return 0;
    }

    @NonNull
    private String getCardHolderName() {
        String cardHolderName = emv.getCardholderName();
        if (cardHolderName == null || cardHolderName.isEmpty() || cardHolderName.trim().isEmpty()) {
            return "";
        }
        return new String(ConvertUtils.strToBcdPaddingLeft(cardHolderName));
    }

    @Override
    public OnlineResultWrapper startOnlineProcess(long amount, String terminalId, int detectResult, String reference, ISendingOnlineCallback callback) {
        DialogUtils.dismiss();
        return OnlineTaskFactory.GetTask(emv, callback).start(emv, amount, terminalId, detectResult, reference);
    }

    @NonNull
    @Override
    public MagProcessCallback setConfirmCallback(@Nullable IEmvService.ConfirmCallback callback) {
        this.confirmCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public MagProcessCallback setErrorCallback(@Nullable IEmvService.ErrorCallback callback) {
        this.errorCallback = callback;
        return this;
    }

    @NonNull
    public MagProcessCallback setCvmTypeList(List<CvmType> cvmTypeList) {
        this.cvmTypeList = cvmTypeList;
        return this;
    }
}
