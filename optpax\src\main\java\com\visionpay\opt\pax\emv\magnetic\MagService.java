package com.visionpay.opt.pax.emv.magnetic;
import android.os.ConditionVariable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.entity.EPiccType;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.utils.EmvDebugger;
import com.pax.emvservice.export.IEmvMagService;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvTag;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
/**
 * Contact EMV Service.
 */
public class MagService implements IEmvService<IEmvMagService> {
    private static final String TAG = "MagService";
    private final ConditionVariable cv = new ConditionVariable();
    private final List<String> emvTagContentList = new ArrayList<>();
    private final List<EmvTag> emvTagList = new ArrayList<>();
    private ConfirmCallback confirmCallback;
    private ErrorCallback errorCallback;
    private CompleteCallback completeCallback;
    private ISendingOnlineCallback sendinfOnlineCallback;

    @NonNull
    @Override
    public MagService start(@NonNull IEmvMagService emv, long amount, String terminalId, int detectResult, String reference) {
        LogUtils.d(TAG, "============ Start Contact EMV Service ============");
        App.getApp().runInBackground(() -> {
            emvTagContentList.clear();
            emvTagList.clear();
            EmvDebugger.setDebuggable(true);
            EmvDebugger.setCustomDebugger(new EmvTagDebugger());
            List<CvmType> cvmTypeList = new LinkedList<>();
            try {
                //DeviceManager.getInstance().setIDevice(EmvDeviceImpl.getInstance());
                int ret = emv.startTransProcess(amount, terminalId, detectResult, reference, new MagProcessCallback(emv, cv)
                        .setConfirmCallback(confirmCallback)
                        .setErrorCallback(errorCallback)
                        .setCvmTypeList(cvmTypeList), sendinfOnlineCallback);
                LogUtils.d(TAG, "Emv ret: " + ret);
            } catch (Exception e) {
                LogUtils.e(TAG, "Emv service error", e);
                if (errorCallback != null) {
                    errorCallback.onError("Unexpected error",
                            "EMV service throw a "
                                    + e.getClass().getSimpleName()
                                    + ". Please check log.");
                }
            } finally {
                LogUtils.d(TAG, "============ Stop Search Card ============");
                closeMag();
                closeIcc();
                closeInternalPicc();
                closeExternalPicc();

                LogUtils.d(TAG, "============ Start Check Result ============");

                try {
                    emv.checkContactResult(new MagResultListener(
                            emv,
                            cvmTypeList,
                            emvTagList,
                            errorCallback,
                            completeCallback));
                } catch (Exception e) {
                    LogUtils.e(TAG, "Emv service error", e);
                }

                EmvDebugger.setCustomDebugger(null);
            }
        });
        return this;
    }

    private class EmvTagDebugger implements EmvDebugger.Debugger {
        @Override
        public void d(@Nullable String logTag, @Nullable String methodName, @NonNull String emvTag,
                @Nullable String tagValue) {
            if (tagValue == null || tagValue.isEmpty()) {
                return;
            }
            // tagValue: 00000000000000000......
            if (tagValue.matches("^0+$") && tagValue.length() > 20) {
                return;
            }
            if (!emvTagContentList.contains(emvTag)) {
                emvTagList.add(new EmvTag(emvTag, tagValue));
                emvTagContentList.add(emvTag);
            }
            LogUtils.d(logTag, methodName + " - tag: " + emvTag + ", value: " + tagValue);
        }
    }

    @NonNull
    @Override
    public MagService setConfirmCallback(@Nullable ConfirmCallback callback) {
        this.confirmCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public MagService setErrorCallback(@Nullable ErrorCallback callback) {
        this.errorCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public MagService setCompleteCallback(@Nullable CompleteCallback callback) {
        this.completeCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public MagService setSendinfOnlineCallback(@Nullable ISendingOnlineCallback callback) {
        this.sendinfOnlineCallback = callback;
        return this;
    }

    private void closeMag() {
        try {
            App.getDal().getMag().close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close mag failed", e);
        }
    }

    private void closeIcc() {
        try {
            App.getDal().getIcc().close((byte) 0);
        } catch (Exception e) {
            LogUtils.e(TAG, "close icc failed", e);
        }
    }

    private void closeInternalPicc() {
        try {
            App.getDal().getPicc(EPiccType.INTERNAL).close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close internal picc failed", e);
        }
    }

    private void closeExternalPicc() {
        try {
            App.getDal().getPicc(EPiccType.EXTERNAL).close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close external picc failed", e);
        }
    }
}
