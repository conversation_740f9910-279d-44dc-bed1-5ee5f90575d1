/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.list;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.entity.EmvTag;
import com.visionpay.opt.pax.utils.EmvTagUtils;

import java.util.List;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/19
 */
public class EmvTagAdapter extends RecyclerView.Adapter<EmvTagViewHolder> {
    private List<EmvTag> emvTagList;
    private OnItemClickListener onItemClickListener;

    public EmvTagAdapter(List<EmvTag> emvTagList) {
        this.emvTagList = emvTagList;
    }

    public EmvTagAdapter setEmvTagList(List<EmvTag> emvTagList) {
        this.emvTagList = emvTagList;
        return this;
    }

    public EmvTagAdapter setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
        return this;
    }

    @NonNull
    @Override
    public EmvTagViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_emv_tag_content, parent, false);
        return new EmvTagViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EmvTagViewHolder holder, int position) {
        EmvTag item = emvTagList.get(position);
        holder.tagTextView.setText(EmvTagUtils.parseTagName(item.getTag()));
        holder.valueTextView.setText(EmvTagUtils.parseTagValue(item.getTag(), item.getValue()));
        if (onItemClickListener != null) {
            holder.linearLayout.setOnClickListener(v -> {
                onItemClickListener.onClick(item, v);
            });
        }
    }

    @Override
    public int getItemCount() {
        return emvTagList.size();
    }

    public interface OnItemClickListener {
        void onClick(EmvTag item, View view);
    }
}
