/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.entity;

import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;
import com.pax.emvbase.constant.EmvConstant;
import com.visionpay.opt.pax.R;

/**
 * Issuer list.
 */
public enum EIssuer {
    VISA("VISA", "VISA", R.drawable.ic_issuer_visa),
    MASTER("MASTER", "MasterCard", R.drawable.ic_issuer_mc),
    AMEX("AMEX", "American Express", R.drawable.ic_issuer_amex),
    UNION_PAY("UnionPay", "UnionPay", R.drawable.ic_issuer_unionpay),
    DISCOVER("DinersClub", "Discover", R.drawable.ic_issuer_discover),
    JCB("JCB", "JCB", R.drawable.ic_issuer_jcb),
    RUPAY("RuPay", "RuPay", R.drawable.ic_issuer_rupay),
    SHELLCARD("ShellCard", "Shell Card", R.drawable.ic_issuer_shell),
    VIVACARD("VivaCard", "Viva Aviation Card ", R.drawable.ic_issuer_shell),
    COMPACCARD("CompacCard", "CompacCard", R.drawable.ic_issuer_fleet),
    UNITEDCARD("UnitedCard", "United Card", R.drawable.ic_issuer_fleet);

    String issuerName;
    String displayName;
    int issuerIconRes;

    EIssuer(String issuerName, String displayName, @DrawableRes int issuerIconRes) {
        this.issuerName = issuerName;
        this.displayName = displayName;
        this.issuerIconRes = issuerIconRes;
    }

    public String getIssuerName() {
        return issuerName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public int getIssuerIconRes() {
        return issuerIconRes;
    }

    /**
     * Parse Issuer by issuer name.
     *
     * @param issuerName Issuer name
     * @return Issuer
     */
    @Nullable
    public static EIssuer parse(String issuerName) {
        for (EIssuer issuer : EIssuer.values()) {
            if (issuerName.equals(issuer.getIssuerName())) {
                return issuer;
            }
        }
        return null;
    }

    /**
     * Parse Issuer by contactless kernel type.
     *
     * @param kernelType Contactless kernel type
     * @return Issuer
     */
    @Nullable
    public static EIssuer parse(int kernelType) {
        switch (kernelType) {
            case EmvConstant.KernType.KERNTYPE_VIS:
                return VISA;
            case EmvConstant.KernType.KERNTYPE_MC:
                return MASTER;
            case EmvConstant.KernType.KERNTYPE_AE:
                return AMEX;
            case EmvConstant.KernType.KERNTYPE_PBOC:
                return UNION_PAY;
            case EmvConstant.KernType.KERNTYPE_ZIP:
                return DISCOVER;
            case EmvConstant.KernType.KERNTYPE_JCB:
                return JCB;
            case EmvConstant.KernType.KERNTYPE_RUPAY:
                return RUPAY;
            default:
                return null;
        }
    }
}
