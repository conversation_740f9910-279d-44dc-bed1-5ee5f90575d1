/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/01                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.utils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.emvservice.export.IEmvBase;
import java.nio.ByteBuffer;
import java.util.Arrays;

/**
 * EMV Tag Utils
 */
public class EmvTagUtils {
    private static final String TAG = "EmvTagUtils";

    private EmvTagUtils() {
        // do nothing
    }

    public static void getCommonTags(@NonNull IEmvBase emv) {
        int[] tags = new int[]{
                0x9F12,     // APP Name
                0x9F0D,     // IAC Default
                0x9F0E,     // IAC Denial
                0x9F0F,     // IAC Online
                0xDF8120,   // TAC Default
                0xDF8121,   // TAC Denial
                0xDF8122,   // TAC Online
                0x95,       // TVR
                0x9B,       // TSI
                0x9F02,     // Amount
                0x9F03,     // Amount Other
                0x9C,       // Trans Type
                0x5F28,     // Issuer Country Code
                0x5F2A,     // Currency Code
                0x5F36,     // Currency Exponent
                0x5F30,     // Service Code
                0x5F34,     // PAN Seq Number
                0x4F,       // CAPK RID
                0x8F,       // CAPK ID
                0x84,       // DF Name
                0x8E,       // CVM List
                0x9F34,     // CVM Result
                0x8A,       // ARC
                0x71,       // Issuer Script Template 1
                0x72,       // Issuer Script Template 2
        };
        for (int tag : tags) {
            emv.getTlv(tag);    // Ignore result, because EmvDebugger will record the tag and value
        }
    }

    /**
     * The Backend might return
     * I. 71, 72,  combine 71 and 72, say T1L1V1T2L2V2, in which T1 is TAG71 and T2 is TAG 72
     * II. 71 only, return TLV, in wich T is TAG 71
     * III. 72 only, return TLV, in wich T is TAG 72
     * IV. no script, return new byte[0]
     *
     * @param f71 value of 71
     * @param f72 value of 72
     * @return combine
     */
    public static byte[] combine7172(@Nullable byte[] f71, @Nullable byte[] f72) {
        boolean f71Empty = (null == f71 || f71.length <= 0);
        boolean f72Empty = (null == f72 || f72.length <= 0);

        if (f71Empty && f72Empty) {
            return new byte[0];
        }

        if (f71Empty) {
            return createTLVByTV((byte) 0x72, f72);
        }

        if (f72Empty) {
            return createTLVByTV((byte) 0x71, f71);
        }

        return mergeByteArrays(createTLVByTV((byte) 0x71, f71), createTLVByTV((byte) 0x72, f72));
    }

    private static byte[] createTLVByTV(byte tag, @Nullable byte[] value) {
        if (null == value || value.length <= 0) {
            return new byte[0];
        }
        ByteBuffer bb = ByteBuffer.allocate(value.length + 3);
        bb.put(tag);
        if (value.length > 127) {//need two bytes to indicate length
            bb.put((byte) 0x81);
        }
        bb.put((byte) value.length);
        bb.put(value, 0, value.length);

        int len = bb.position();
        bb.position(0);

        byte[] tlv = new byte[len];
        bb.get(tlv, 0, len);

        return tlv;
    }

    private static byte[] mergeByteArrays(@Nullable byte[] byteArr1, @Nullable byte[] byteArr2) {
        if (null == byteArr1 || byteArr1.length <= 0) {
            return byteArr2;
        }
        if (null == byteArr2 || byteArr2.length <= 0) {
            return byteArr1;
        }
        byte[] result = Arrays.copyOf(byteArr1, byteArr1.length + byteArr2.length);
        System.arraycopy(byteArr2, 0, result, byteArr1.length, byteArr2.length);
        return result;
    }

    @NonNull
    public static String parseTagName(@NonNull String tag) {
        String name = parseTagNameInternal(tag);
        if (!name.isEmpty()) {
            return name + " (0x" + tag + ")";
        }
        return "0x" + tag;
    }

    @NonNull
    private static String parseTagNameInternal(String tag) {
        switch (tag.toUpperCase().replace("0X", "")) {
            case "4F": return "CAPK RID";
            case "50": return "APP Label";
            case "57": case "9F6B": return "Track 2";
            case "5A": return "PAN";
            case "6F": return "FCI Template";
            case "71": return "Issuer Script Template 1";
            case "72": return "Issuer Script Template 2";
            case "82": return "AIP";
            case "84": return "DF Name";
            case "89": return "Authorisation Code";
            case "8A": return "ARC";
            case "8C": return "CDOL1";
            case "8D": return "CDOL2";
            case "8E": return "CVM List";
            case "8F": return "CAPK ID";
            case "95": return "TVR";
            case "97": return "TDOL";
            case "9A": return "Trans Date";
            case "9B": return "TSI";
            case "9C": return "Trans Type";
            case "5F20": return "Cardholder Name";
            case "5F24": return "Expire Date";
            case "5F28": return "Issuer Country Code";
            case "5F2A": return "Currency Code";
            case "5F30": return "Service Code";
            case "5F34": return "PAN Seq Number";
            case "5F36": return "Currency Exponent";
            case "9F01": return "Acquirer ID";
            case "9F02": return "Amount";
            case "9F03": return "Amount Other";
            case "9F08": case "9F09": return "App Version";
            case "9F0D": return "IAC Default";
            case "9F0E": return "IAC Denial";
            case "9F0F": return "IAC Online";
            case "9F10": return "Issuer Application Data";
            case "9F11": return "Issuer Code Table Index";
            case "9F12": return "APP Name";
            case "9F15": return "Merchant Category Code";
            case "9F16": return "Merchant ID";
            case "9F1A": return "Country Code";
            case "9F1C": return "Terminal ID";
            case "9F1D": return "Terminal Risk Management Data";
            case "9F1E": return "IFD Serial Number";
            case "9F21": return "Trans Time";
            case "9F26": return "TC";
            case "9F27": return "CID";
            case "9F33": return "Terminal Capability";
            case "9F34": return "CVM Result";
            case "9F35": return "Terminal Type";
            case "9F36": return "ATC";
            case "9F37": return "Unpredictable Number";
            case "9F38": return "PDOL";
            case "9F41": return "Trans No";
            case "9F4E": return "Merchant Name Location";
            case "9F66": return "TTQ";
            case "DF8120": return "TAC Default";
            case "DF8121": return "TAC Denial";
            case "DF8122": return "TAC Online";
            case "DF8123": return "Floor Limit";
            case "DF8124": return "Trans Limit";
            case "DF8126": return "CVM Limit";
            default: return "";
        }
    }

    public static String parseTagValue(String tag, String originValue) {
        if (tag == null || tag.isEmpty()) {
            return "(Empty)";
        }
        if (originValue == null || originValue.isEmpty()) {
            return "(Empty)";
        }
        if (originValue.matches("^0+$") && originValue.length() > 20) {
            return "(Empty)";
        }
        switch (tag.toUpperCase().replace("0X", "")) {
            case "50":
            case "5F20":
            case "9F12":
            case "9F4E":
                return new String(ConvertUtils.strToBcdPaddingLeft(originValue));
            default:
                return originValue;
        }
    }
}
