/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.core.widget.NestedScrollView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.lifecycle.ViewModelProvider;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton;
import com.google.android.material.snackbar.Snackbar;
import com.pax.bizentity.entity.ETransType;
import com.pax.commonlib.application.BaseActivity;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.fragment.TransErrorFragment;
import com.visionpay.opt.pax.fragment.TransFragmentManager;
import com.visionpay.opt.pax.viewmodel.TransViewModel;
import com.sankuai.waimai.router.annotation.RouterUri;

import java.util.List;

/**
 * Search Card Activity
 *
 * 由于 TransActivity 包含了比较多的内容，并且随着 EMV 流程的执行，UI 显示的内容也不断的变化，因此没有将所有的 UI
 * 都集中在一个 Activity 中，而是按照“卡片”为单位，拆分成了多个 Fragment，并让各个 Fragment 根据 Status 自行更
 * 新其 UI 的显示。
 * 也就是说，Activity 只负责创建 Fragment，并将更新的 Status 通知到各个 Fragment，并不直接去“设置”各个控件的状
 * 态。这样的好处是 Activity 的逻辑变得十分简单，维护起来也更容易——或者说，Activity 本身就会变成一个非常稳定的存在，
 * 因为它只作为一个容器，承载 Fragment 的 UI；而 Fragment 同样也很简单，只是从 Status 取出相应的字段来展示。
 * 另外，有关寻卡提示、AID 信息、EMV 结果等 Fragment 的控制，也没有放在 Activity 里，而是由 TransFragmentManager
 * 来进行管理的。TransViewModel 通过监听 EMV 流程的回调来执行 TransFragmentManager 的相应方法，从而实现 Fragment
 * 的更新。
 */
@RouterUri(path = EmvRouterConst.SEARCH_CARD)
public class TransActivity extends BaseActivity {
    private static final String TAG = "SearchCardActivity";
    private static final String SAVED_AMOUNT = "amount";
    private static final String SAVED_TRANS_TYPE = "trans_type";

    private boolean isRestore = false;
    private long amount;
    private String transType;

    private TransViewModel viewModel;
    private TextView amountTextView;
    private TextView transTypeTextView;

    private NestedScrollView scrollView;
    private ExtendedFloatingActionButton fab;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        isRestore = (savedInstanceState != null);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_trans;
    }

    private int getContainerId() {
        return R.id.trans_content;
    }

    @Override
    protected void initViews() {
        amountTextView = findViewById(R.id.trans_amount_content);
        amountTextView.setText(CurrencyConverter.convert(amount));

        transTypeTextView = findViewById(R.id.trans_type_content);
        transTypeTextView.setText(transType);

        MaterialToolbar toolbar = findViewById(R.id.trans_toolbar);
        setSupportActionBar(toolbar);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeAsUpIndicator(R.drawable.ic_back);
        }

        scrollView = findViewById(R.id.trans_scroll_view);
        fab = findViewById(R.id.trans_retry_button);

        scrollView.setOnScrollChangeListener((NestedScrollView.OnScrollChangeListener) (v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
            if (scrollY != (v.getChildAt(0).getMeasuredHeight() - v.getMeasuredHeight())) {
                fab.shrink();
            } else {
                fab.extend();
            }
        });
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
        }
        return true;
    }

    @Override
    public void onBackPressed() {
        if (viewModel != null && viewModel.isEmvProcessing()) {
            // Cannot terminate emv process
            return;
        }
        super.onBackPressed();
    }

    @Override
    protected void setListeners() {
        viewModel.setMessageCallback(message -> Snackbar.make(fab, message, Snackbar.LENGTH_SHORT).show());

        viewModel.setErrorEventCallback(event -> {
            if (event != null) {
                TransErrorFragment fragment = new TransErrorFragment()
                        .setTitle(event.getTitle())
                        .setInfo(event.getInfo())
                        .setButtonText(event.getButtonContent())
                        .setButtonIconRes(event.getButtonIconId())
                        .setButtonClickListener(event.getButtonClickListener());
                getSupportFragmentManager().beginTransaction()
                        .add(getContainerId(), fragment, "Error Info")
                        .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
                        .commit();
            }
        });

        viewModel.setRemoveErrorCardCallback(() -> {
            LogUtils.d(TAG, "need remove error card");
            List<Fragment> fragmentList = getSupportFragmentManager().getFragments();
            if (!fragmentList.isEmpty()) {
                FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
                for (Fragment fragment : fragmentList) {
                    if (fragment instanceof TransErrorFragment) {
                        transaction.remove(fragment);
                    }
                }
                transaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)
                        .commitAllowingStateLoss();
            }
        });

        viewModel.listenRetryEnable().observe(this, enabled -> {
            fab.setVisibility(enabled ? View.VISIBLE : View.GONE);
            if (enabled) {
                fab.shrink();
            }
        });
        fab.setOnClickListener(v -> {
            viewModel.retry();
            scrollView.fullScroll(NestedScrollView.FOCUS_UP);
        });

        if (!isRestore) {
            viewModel.startSearchCard();
        }

        isRestore = false;  // Reset flag
    }

    @Override
    protected void loadParam() {
        viewModel = new ViewModelProvider(this).get(TransViewModel.class);

        TransFragmentManager.getInstance().init(getSupportFragmentManager(), getContainerId());

        if (!isRestore) {
            Intent intent = getIntent();
            if (intent != null) {
                transType = intent.getStringExtra(BundleFieldConst.TRANS_TYPE);
                ETransType type = ConvertUtils.enumValue(ETransType.class, transType);
                amount = intent.getLongExtra(BundleFieldConst.AMOUNT, 0L);
                if (type != null && amount != 0) {
                    viewModel.initTrans(type, amount);
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        TransFragmentManager.getInstance().clear();
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

        outState.putLong(SAVED_AMOUNT, amount);
        outState.putString(SAVED_TRANS_TYPE, transType);
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        amount = savedInstanceState.getLong(SAVED_AMOUNT, 0);
        amountTextView.setText(CurrencyConverter.convert(amount));

        transType = savedInstanceState.getString(SAVED_TRANS_TYPE, ETransType.SALE.name());
        transTypeTextView.setText(transType);
    }
}