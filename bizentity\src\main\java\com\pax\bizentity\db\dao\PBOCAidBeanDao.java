package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.clss.pboc.PBOCAidBean;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "pboc_aid".
*/
public class PBOCAidBeanDao extends AbstractDao<PBOCAidBean, Long> {

    public static final String TABLENAME = "pboc_aid";

    /**
     * Properties of entity PBOCAidBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "pboc_id");
        public final static Property AppName = new Property(1, String.class, "appName", false, "APP_NAME");
        public final static Property Aid = new Property(2, String.class, "aid", false, "AID");
        public final static Property SelFlag = new Property(3, byte.class, "selFlag", false, "SEL_FLAG");
        public final static Property TransLimit = new Property(4, long.class, "transLimit", false, "TRANS_LIMIT");
        public final static Property TransLimitFlag = new Property(5, byte.class, "transLimitFlag", false, "TRANS_LIMIT_FLAG");
        public final static Property FloorLimit = new Property(6, long.class, "floorLimit", false, "FLOOR_LIMIT");
        public final static Property FloorLimitFlag = new Property(7, byte.class, "floorLimitFlag", false, "FLOOR_LIMIT_FLAG");
        public final static Property CvmLimit = new Property(8, long.class, "cvmLimit", false, "CVM_LIMIT");
        public final static Property CvmLimitFlag = new Property(9, byte.class, "cvmLimitFlag", false, "CVM_LIMIT_FLAG");
        public final static Property QpsLimit = new Property(10, long.class, "qpsLimit", false, "QPS_LIMIT");
        public final static Property AcquirerId = new Property(11, String.class, "acquirerId", false, "ACQUIRER_ID");
        public final static Property Version = new Property(12, String.class, "version", false, "VERSION");
        public final static Property TerminalType = new Property(13, String.class, "terminalType", false, "TERMINAL_TYPE");
        public final static Property TerminalCapability = new Property(14, String.class, "terminalCapability", false, "TERMINAL_CAPABILITY");
        public final static Property TerminalAdditionalCapability = new Property(15, String.class, "terminalAdditionalCapability", false, "TERMINAL_ADDITIONAL_CAPABILITY");
        public final static Property QuicsFlag = new Property(16, byte.class, "quicsFlag", false, "QUICS_FLAG");
        public final static Property TornMaxLifeTime = new Property(17, long.class, "tornMaxLifeTime", false, "TORN_MAX_LIFE_TIME");
        public final static Property TornLogMaxNum = new Property(18, long.class, "tornLogMaxNum", false, "TORN_LOG_MAX_NUM");
        public final static Property TornSupport = new Property(19, byte.class, "tornSupport", false, "TORN_SUPPORT");
        public final static Property TornRFU = new Property(20, String.class, "tornRFU", false, "TORN_RFU");
        public final static Property AucRFU = new Property(21, String.class, "aucRFU", false, "AUC_RFU");
        public final static Property Ttq = new Property(22, String.class, "ttq", false, "TTQ");
        public final static Property AidType = new Property(23, byte.class, "aidType", false, "AID_TYPE");
    }


    public PBOCAidBeanDao(DaoConfig config) {
        super(config);
    }
    
    public PBOCAidBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"pboc_aid\" (" + //
                "\"pboc_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"APP_NAME\" TEXT," + // 1: appName
                "\"AID\" TEXT," + // 2: aid
                "\"SEL_FLAG\" INTEGER NOT NULL ," + // 3: selFlag
                "\"TRANS_LIMIT\" INTEGER NOT NULL ," + // 4: transLimit
                "\"TRANS_LIMIT_FLAG\" INTEGER NOT NULL ," + // 5: transLimitFlag
                "\"FLOOR_LIMIT\" INTEGER NOT NULL ," + // 6: floorLimit
                "\"FLOOR_LIMIT_FLAG\" INTEGER NOT NULL ," + // 7: floorLimitFlag
                "\"CVM_LIMIT\" INTEGER NOT NULL ," + // 8: cvmLimit
                "\"CVM_LIMIT_FLAG\" INTEGER NOT NULL ," + // 9: cvmLimitFlag
                "\"QPS_LIMIT\" INTEGER NOT NULL ," + // 10: qpsLimit
                "\"ACQUIRER_ID\" TEXT," + // 11: acquirerId
                "\"VERSION\" TEXT," + // 12: version
                "\"TERMINAL_TYPE\" TEXT," + // 13: terminalType
                "\"TERMINAL_CAPABILITY\" TEXT," + // 14: terminalCapability
                "\"TERMINAL_ADDITIONAL_CAPABILITY\" TEXT," + // 15: terminalAdditionalCapability
                "\"QUICS_FLAG\" INTEGER NOT NULL ," + // 16: quicsFlag
                "\"TORN_MAX_LIFE_TIME\" INTEGER NOT NULL ," + // 17: tornMaxLifeTime
                "\"TORN_LOG_MAX_NUM\" INTEGER NOT NULL ," + // 18: tornLogMaxNum
                "\"TORN_SUPPORT\" INTEGER NOT NULL ," + // 19: tornSupport
                "\"TORN_RFU\" TEXT," + // 20: tornRFU
                "\"AUC_RFU\" TEXT," + // 21: aucRFU
                "\"TTQ\" TEXT," + // 22: ttq
                "\"AID_TYPE\" INTEGER NOT NULL );"); // 23: aidType
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"pboc_aid\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, PBOCAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
        stmt.bindLong(11, entity.getQpsLimit());
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(12, acquirerId);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(13, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(14, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(15, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(16, terminalAdditionalCapability);
        }
        stmt.bindLong(17, entity.getQuicsFlag());
        stmt.bindLong(18, entity.getTornMaxLifeTime());
        stmt.bindLong(19, entity.getTornLogMaxNum());
        stmt.bindLong(20, entity.getTornSupport());
 
        String tornRFU = entity.getTornRFU();
        if (tornRFU != null) {
            stmt.bindString(21, tornRFU);
        }
 
        String aucRFU = entity.getAucRFU();
        if (aucRFU != null) {
            stmt.bindString(22, aucRFU);
        }
 
        String ttq = entity.getTtq();
        if (ttq != null) {
            stmt.bindString(23, ttq);
        }
        stmt.bindLong(24, entity.getAidType());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, PBOCAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
        stmt.bindLong(11, entity.getQpsLimit());
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(12, acquirerId);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(13, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(14, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(15, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(16, terminalAdditionalCapability);
        }
        stmt.bindLong(17, entity.getQuicsFlag());
        stmt.bindLong(18, entity.getTornMaxLifeTime());
        stmt.bindLong(19, entity.getTornLogMaxNum());
        stmt.bindLong(20, entity.getTornSupport());
 
        String tornRFU = entity.getTornRFU();
        if (tornRFU != null) {
            stmt.bindString(21, tornRFU);
        }
 
        String aucRFU = entity.getAucRFU();
        if (aucRFU != null) {
            stmt.bindString(22, aucRFU);
        }
 
        String ttq = entity.getTtq();
        if (ttq != null) {
            stmt.bindString(23, ttq);
        }
        stmt.bindLong(24, entity.getAidType());
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public PBOCAidBean readEntity(Cursor cursor, int offset) {
        PBOCAidBean entity = new PBOCAidBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // appName
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // aid
            (byte) cursor.getShort(offset + 3), // selFlag
            cursor.getLong(offset + 4), // transLimit
            (byte) cursor.getShort(offset + 5), // transLimitFlag
            cursor.getLong(offset + 6), // floorLimit
            (byte) cursor.getShort(offset + 7), // floorLimitFlag
            cursor.getLong(offset + 8), // cvmLimit
            (byte) cursor.getShort(offset + 9), // cvmLimitFlag
            cursor.getLong(offset + 10), // qpsLimit
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // acquirerId
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // version
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // terminalType
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // terminalCapability
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // terminalAdditionalCapability
            (byte) cursor.getShort(offset + 16), // quicsFlag
            cursor.getLong(offset + 17), // tornMaxLifeTime
            cursor.getLong(offset + 18), // tornLogMaxNum
            (byte) cursor.getShort(offset + 19), // tornSupport
            cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20), // tornRFU
            cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21), // aucRFU
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // ttq
            (byte) cursor.getShort(offset + 23) // aidType
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, PBOCAidBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAppName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAid(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSelFlag((byte) cursor.getShort(offset + 3));
        entity.setTransLimit(cursor.getLong(offset + 4));
        entity.setTransLimitFlag((byte) cursor.getShort(offset + 5));
        entity.setFloorLimit(cursor.getLong(offset + 6));
        entity.setFloorLimitFlag((byte) cursor.getShort(offset + 7));
        entity.setCvmLimit(cursor.getLong(offset + 8));
        entity.setCvmLimitFlag((byte) cursor.getShort(offset + 9));
        entity.setQpsLimit(cursor.getLong(offset + 10));
        entity.setAcquirerId(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setVersion(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setTerminalType(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setTerminalCapability(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setTerminalAdditionalCapability(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setQuicsFlag((byte) cursor.getShort(offset + 16));
        entity.setTornMaxLifeTime(cursor.getLong(offset + 17));
        entity.setTornLogMaxNum(cursor.getLong(offset + 18));
        entity.setTornSupport((byte) cursor.getShort(offset + 19));
        entity.setTornRFU(cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20));
        entity.setAucRFU(cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21));
        entity.setTtq(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setAidType((byte) cursor.getShort(offset + 23));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(PBOCAidBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(PBOCAidBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(PBOCAidBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
