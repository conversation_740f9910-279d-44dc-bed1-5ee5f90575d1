package com.visionpay.opt.pax.emv.mifare;

import android.app.Activity;
import android.os.ConditionVariable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.pax.bizentity.entity.Issuer;
import com.pax.bizlib.trans.Device;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.pax.emvbase.process.mifare.IMifareCallback;
import com.pax.emvservice.export.IEmvMifareService;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.dialog.RemoveCardDialog;
import com.visionpay.opt.pax.emv.IEmvProcessCallback;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.contactless.ContactlessService;
import com.visionpay.opt.pax.emv.online.OnlineTaskFactory;
import com.visionpay.opt.pax.emv.pin.IPinTask;
import com.visionpay.opt.pax.emv.pin.OfflinePinTask;
import com.visionpay.opt.pax.emv.pin.OnlinePinTask;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EIssuer;
import com.visionpay.opt.pax.utils.DialogUtils;

import java.util.List;

@WorkerThread
public class MifareProcessCallback implements IMifareCallback, IEmvProcessCallback {
    private static final String TAG = "MifareProcessCallback";

    private final IEmvMifareService emv;
    private final ConditionVariable cv;
    private IEmvService.ConfirmCallback confirmCallback;
    private IEmvService.ErrorCallback errorCallback;
    private ContactlessService.RemoveCardCallback removeCardCallback;
    private List<CvmType> cvmTypeList;

    public MifareProcessCallback(@NonNull IEmvMifareService emv, @NonNull ConditionVariable cv) {
        this.emv = emv;
        this.cv = cv;
    }

    @Override
    public void onRemoveCard() {
        try {
            DialogUtils.dismiss();
            final boolean[] isShown = { false };
            RemoveCardDialog dialog = new RemoveCardDialog();
            Device.removeMifareCard(result -> {
                if (!isShown[0]) {
                    Activity activity = ActivityStack.getInstance().top();
                    if (activity instanceof FragmentActivity) {
                        FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
                        dialog.show(manager, "Remove Card");
                        isShown[0] = true;
                    }
                    if (removeCardCallback != null) {
                        removeCardCallback.onRemove();
                    }
                }
            });
            dialog.dismissAllowingStateLoss();
        } catch (Exception e) {
            LogUtils.e(TAG, "Remove card error", e);
        }
    }

    @Override
    public int onCardHolderPwd(boolean isOnlinePin, boolean supportPINByPass, int leftTimes,
            byte[] pinData) {
        LogUtils.d(TAG, "============ PIN Input Start ============");
        DialogUtils.dismiss();

        IPinTask pinTask;
        if (isOnlinePin) {
            pinTask = new OnlinePinTask((title, reason) -> errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_online_pin)));
            }
        } else {
            pinTask = new OfflinePinTask(leftTimes, (title, reason) ->
                    errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_offline_pin)));
            }
        }

        int ret = pinTask.start(emv, supportPINByPass, emv.getTlv(0x100001).length != 0);
        LogUtils.d(TAG, "Pin Task ret: " + ret);
        LogUtils.d(TAG, "============ PIN Input End ============");
        return ret;
    }

    @Override
    public int showConfirmCard() {
        LogUtils.d(TAG, "============ Confirm Card Start ============");

        Issuer matchedIssuer = emv.getMatchedIssuerByPan();

        if (confirmCallback != null) {
            if (matchedIssuer != null) {
                String name = matchedIssuer.getName();
                EIssuer info = EIssuer.parse(name);
                if (info != null) {
                    confirmCallback.onConfirm(emv.getPan(),
                            info.getIssuerIconRes(),
                            info.getDisplayName(),
                            getCardHolderName(), null, "", "", "", emv.getExpireDate(), "", 0,
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                } else {
                    confirmCallback.onConfirm(emv.getPan(),
                            R.drawable.ic_issuer_other, name,
                            getCardHolderName(), null, "", "", "", emv.getExpireDate(), "", 0,
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                }
            } else {
                confirmCallback.onConfirm(emv.getPan(), R.drawable.ic_issuer_other,
                        "Unknown",
                        getCardHolderName(), null, "", "", "", emv.getExpireDate(), "", 0,
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            }
        }
        LogUtils.d(TAG, "============ Confirm Card End ============");
        return 0;
    }

    @NonNull
    private String getCardHolderName() {
        String cardHolderName = emv.getCardholderName();
        if (cardHolderName == null || cardHolderName.isEmpty() || cardHolderName.trim().isEmpty()) {
            return "";
        }
        return new String(ConvertUtils.strToBcdPaddingLeft(cardHolderName));
    }

    @Override
    public OnlineResultWrapper startOnlineProcess(long amount, String terminalId, int detectResult, String reference, ISendingOnlineCallback callback) {
        DialogUtils.dismiss();
        return OnlineTaskFactory.GetTask(emv, callback).start(emv, amount, terminalId, detectResult, reference);
    }

    @NonNull
    @Override
    public MifareProcessCallback setConfirmCallback(@Nullable IEmvService.ConfirmCallback callback) {
        this.confirmCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public MifareProcessCallback setErrorCallback(@Nullable IEmvService.ErrorCallback callback) {
        this.errorCallback = callback;
        return this;
    }

    @NonNull
    public MifareProcessCallback setCvmTypeList(List<CvmType> cvmTypeList) {
        this.cvmTypeList = cvmTypeList;
        return this;
    }

    public MifareProcessCallback setRemoveCardCallback(ContactlessService.RemoveCardCallback removeCardCallback) {
        this.removeCardCallback = removeCardCallback;
        return this;
    }
}
