/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/07                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;

import com.visionpay.opt.pax.config.datastore.PrefDataStore;
import com.visionpay.opt.pax.consts.ConfigUIKeyConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.PrefCategoryBuilder;
import com.pax.preflib.builder.RouterPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

/**
 * EMV Param page
 *
 * Settings -> Param
 */
@RouterService(interfaces = EmvConfigBaseFragment.class, key = EmvRouterConst.CONFIG_PARAM)
public class ConfigParamFragment extends EmvConfigBaseFragment {
    @NonNull
    @Override
    public String getFragmentTitle() {
        return "Param";
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new PrefDataStore());
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // Contact
            screen.addPreference(PrefCategoryBuilder
                    .newInstance(context, ConfigUIKeyConst.EMV_CONTACT_CATEGORY, R.string.config_param_contact)
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACT_AID_LIST, R.string.config_param_contact_emv)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CONTACT)
                            .build())
                    .setIconSpaceReserved(false)
                    .build());

            // Contactless
            screen.addPreference(PrefCategoryBuilder
                    .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_CATEGORY,
                            R.string.config_param_contactless)
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_AMEX_LIST,
                                    R.string.config_param_contactless_amex)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_AMEX)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_DPAS_LIST,
                                    R.string.config_param_contactless_dpas)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_DPAS)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_EFT_LIST,
                                    R.string.config_param_contactless_eft)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_EFT)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_JCB_LIST,
                                    R.string.config_param_contactless_jcb)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_JCB)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_MC_LIST,
                                    R.string.config_param_contactless_mc)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_MC)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_MIR_LIST,
                                    R.string.config_param_contactless_mir)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_MIR)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_PAYWAVE_LIST,
                                    R.string.config_param_contactless_paywave)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_PBOC_LIST,
                                    R.string.config_param_contactless_pboc)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_PBOC)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_PURE_LIST,
                                    R.string.config_param_contactless_pure)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_PURE)
                            .build())
                    .addPreference(RouterPrefBuilder
                            .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_RUPAY_LIST,
                                    R.string.config_param_contactless_rupay)
                            .setPath(EmvRouterConst.CONFIG_PARAM_CLSS_RUPAY)
                            .build())
                    .setIconSpaceReserved(false)
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
