/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import android.content.Intent;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.preference.Preference;
import com.pax.preflib.builder.factory.PrefFactory;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.common.DefaultUriRequest;
import com.sankuai.waimai.router.core.UriRequest;

/**
 * Create a Preference that can start Activity after clicking.
 * <br>
 * 创建一个点击后可以启动 Activity 的 Preference
 */
public class RouterPrefBuilder extends BasePrefBuilder<Preference, RouterPrefBuilder> {
    private Integer iconId;
    private UriRequest request;
    private Intent intent;
    private String summary;

    private RouterPrefBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        super(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param titleId Title string resource id
     * @return New instance
     */
    public static RouterPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @StringRes int titleId) {
        return new RouterPrefBuilder(context, key, context.getString(titleId));
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param title Title
     * @return New instance
     */
    public static RouterPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @NonNull String title) {
        return new RouterPrefBuilder(context, key, title);
    }

    /**
     * Set preference icon resource id. If not set, no space will be reserved on the screen.
     *
     * @param iconId Icon resource id
     * @return This builder
     */
    public RouterPrefBuilder setIconId(@DrawableRes int iconId) {
        this.iconId = iconId;
        return this;
    }

    /**
     * Set router path. If you want use Intent to start activity, please use {@code setIntent()}.
     *
     * @param path Activity router path
     * @return This builder
     */
    public RouterPrefBuilder setPath(@Nullable String path) {
        if (path == null) {
            this.request = null;
        } else {
            this.request = new DefaultUriRequest(context, path);
        }
        return this;
    }

    public RouterPrefBuilder setRequest(@Nullable UriRequest request) {
        this.request = request;
        return this;
    }

    /**
     * Set intent. If you want use Router to start activity, please use {@code setPath()}.
     *
     * @param intent Activity intent
     * @return This builder
     */
    public RouterPrefBuilder setIntent(@Nullable Intent intent) {
        this.intent = intent;
        return this;
    }

    /**
     * Set summary.
     *
     * @param summary Summary
     * @return This builder
     */
    public RouterPrefBuilder setSummary(@Nullable String summary) {
        this.summary = summary;
        return this;
    }

    /**
     * Set summary by string resource id.
     *
     * @param summaryId Summary string resource id
     * @return This builder
     */
    public RouterPrefBuilder setSummary(@StringRes int summaryId) {
        return setSummary(context.getString(summaryId));
    }

    @NonNull
    @Override
    public Preference build() {
        Preference preference = PrefFactory.getInstance().createPreference(context);
        preference.setKey(key);
        preference.setTitle(title);
        if (iconId != null) {
            preference.setIcon(iconId);
        } else {
            preference.setIconSpaceReserved(false);
        }
        preference.setOnPreferenceClickListener(pref -> {
            if (onPreferenceClickListener != null) {
                boolean result = onPreferenceClickListener.onPreferenceClick(pref);
                if (result) {
                    return true;
                }
            }
            if (request != null) {
                Router.startUri(request);
            } else if (intent != null) {
                context.startActivity(intent);
            }
            return false;
        });
        if (summary != null) {
            preference.setSummary(summary);
        }
        return preference;
    }
}
