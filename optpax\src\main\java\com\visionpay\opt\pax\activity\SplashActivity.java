/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/02/22                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.activity;

import android.content.Intent;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;

import com.pax.commonlib.init.IModuleInit;
import com.pax.commonlib.utils.LazyInit;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.utils.ParamUtils;
public class SplashActivity extends AppCompatActivity {

    private final LazyInit<IConfigParamService> configParamService = LazyInit.by(() ->
            Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG));

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        App.getApp().runInBackground(() -> {
            IModuleInit config = Router.getService(IModuleInit.class, ConfigServiceConstant.INIT_CONFIG);
            config.loadConfig(ParamUtils.loadConfigParam());

            if (configParamService.get().getBoolean(ConfigKeyConstant.IS_FIRST_RUN, true)) {
                config.setCallback(() ->{
                    configParamService.get().putBoolean(ConfigKeyConstant.IS_FIRST_RUN, false);
                });
                config.init();
            }
            ParamUtils.emvModuleInit();
            ParamUtils.emvParamInit();
            jumpToMain();
        });
    }

    private void jumpToMain() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
}