package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.Issuer;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "issuer".
*/
public class IssuerDao extends AbstractDao<Issuer, Long> {

    public static final String TABLENAME = "issuer";

    /**
     * Properties of entity Issuer.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "issuer_id");
        public final static Property Name = new Property(1, String.class, "name", false, "issuer_name");
        public final static Property FloorLimit = new Property(2, long.class, "floorLimit", false, "FLOOR_LIMIT");
        public final static Property AdjustPercent = new Property(3, float.class, "adjustPercent", false, "ADJUST_PERCENT");
        public final static Property PanMaskPattern = new Property(4, String.class, "panMaskPattern", false, "PAN_MASK_PATTERN");
        public final static Property IsEnableAdjust = new Property(5, boolean.class, "isEnableAdjust", false, "IS_ENABLE_ADJUST");
        public final static Property IsEnableOffline = new Property(6, boolean.class, "isEnableOffline", false, "IS_ENABLE_OFFLINE");
        public final static Property IsAllowExpiry = new Property(7, boolean.class, "isAllowExpiry", false, "IS_ALLOW_EXPIRY");
        public final static Property IsAllowManualPan = new Property(8, boolean.class, "isAllowManualPan", false, "IS_ALLOW_MANUAL_PAN");
        public final static Property IsAllowCheckExpiry = new Property(9, boolean.class, "isAllowCheckExpiry", false, "IS_ALLOW_CHECK_EXPIRY");
        public final static Property IsAllowPrint = new Property(10, boolean.class, "isAllowPrint", false, "IS_ALLOW_PRINT");
        public final static Property IsAllowCheckPanMod10 = new Property(11, boolean.class, "isAllowCheckPanMod10", false, "IS_ALLOW_CHECK_PAN_MOD10");
        public final static Property IsRequirePIN = new Property(12, boolean.class, "isRequirePIN", false, "IS_REQUIRE_PIN");
        public final static Property IsRequireMaskExpiry = new Property(13, boolean.class, "isRequireMaskExpiry", false, "IS_REQUIRE_MASK_EXPIRY");
    }


    public IssuerDao(DaoConfig config) {
        super(config);
    }
    
    public IssuerDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"issuer\" (" + //
                "\"issuer_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"issuer_name\" TEXT UNIQUE ," + // 1: name
                "\"FLOOR_LIMIT\" INTEGER NOT NULL ," + // 2: floorLimit
                "\"ADJUST_PERCENT\" REAL NOT NULL ," + // 3: adjustPercent
                "\"PAN_MASK_PATTERN\" TEXT," + // 4: panMaskPattern
                "\"IS_ENABLE_ADJUST\" INTEGER NOT NULL ," + // 5: isEnableAdjust
                "\"IS_ENABLE_OFFLINE\" INTEGER NOT NULL ," + // 6: isEnableOffline
                "\"IS_ALLOW_EXPIRY\" INTEGER NOT NULL ," + // 7: isAllowExpiry
                "\"IS_ALLOW_MANUAL_PAN\" INTEGER NOT NULL ," + // 8: isAllowManualPan
                "\"IS_ALLOW_CHECK_EXPIRY\" INTEGER NOT NULL ," + // 9: isAllowCheckExpiry
                "\"IS_ALLOW_PRINT\" INTEGER NOT NULL ," + // 10: isAllowPrint
                "\"IS_ALLOW_CHECK_PAN_MOD10\" INTEGER NOT NULL ," + // 11: isAllowCheckPanMod10
                "\"IS_REQUIRE_PIN\" INTEGER NOT NULL ," + // 12: isRequirePIN
                "\"IS_REQUIRE_MASK_EXPIRY\" INTEGER NOT NULL );"); // 13: isRequireMaskExpiry
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"issuer\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, Issuer entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(2, name);
        }
        stmt.bindLong(3, entity.getFloorLimit());
        stmt.bindDouble(4, entity.getAdjustPercent());
 
        String panMaskPattern = entity.getPanMaskPattern();
        if (panMaskPattern != null) {
            stmt.bindString(5, panMaskPattern);
        }
        stmt.bindLong(6, entity.getIsEnableAdjust() ? 1L: 0L);
        stmt.bindLong(7, entity.getIsEnableOffline() ? 1L: 0L);
        stmt.bindLong(8, entity.getIsAllowExpiry() ? 1L: 0L);
        stmt.bindLong(9, entity.getIsAllowManualPan() ? 1L: 0L);
        stmt.bindLong(10, entity.getIsAllowCheckExpiry() ? 1L: 0L);
        stmt.bindLong(11, entity.getIsAllowPrint() ? 1L: 0L);
        stmt.bindLong(12, entity.getIsAllowCheckPanMod10() ? 1L: 0L);
        stmt.bindLong(13, entity.getIsRequirePIN() ? 1L: 0L);
        stmt.bindLong(14, entity.getIsRequireMaskExpiry() ? 1L: 0L);
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, Issuer entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(2, name);
        }
        stmt.bindLong(3, entity.getFloorLimit());
        stmt.bindDouble(4, entity.getAdjustPercent());
 
        String panMaskPattern = entity.getPanMaskPattern();
        if (panMaskPattern != null) {
            stmt.bindString(5, panMaskPattern);
        }
        stmt.bindLong(6, entity.getIsEnableAdjust() ? 1L: 0L);
        stmt.bindLong(7, entity.getIsEnableOffline() ? 1L: 0L);
        stmt.bindLong(8, entity.getIsAllowExpiry() ? 1L: 0L);
        stmt.bindLong(9, entity.getIsAllowManualPan() ? 1L: 0L);
        stmt.bindLong(10, entity.getIsAllowCheckExpiry() ? 1L: 0L);
        stmt.bindLong(11, entity.getIsAllowPrint() ? 1L: 0L);
        stmt.bindLong(12, entity.getIsAllowCheckPanMod10() ? 1L: 0L);
        stmt.bindLong(13, entity.getIsRequirePIN() ? 1L: 0L);
        stmt.bindLong(14, entity.getIsRequireMaskExpiry() ? 1L: 0L);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public Issuer readEntity(Cursor cursor, int offset) {
        Issuer entity = new Issuer( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // name
            cursor.getLong(offset + 2), // floorLimit
            cursor.getFloat(offset + 3), // adjustPercent
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // panMaskPattern
            cursor.getShort(offset + 5) != 0, // isEnableAdjust
            cursor.getShort(offset + 6) != 0, // isEnableOffline
            cursor.getShort(offset + 7) != 0, // isAllowExpiry
            cursor.getShort(offset + 8) != 0, // isAllowManualPan
            cursor.getShort(offset + 9) != 0, // isAllowCheckExpiry
            cursor.getShort(offset + 10) != 0, // isAllowPrint
            cursor.getShort(offset + 11) != 0, // isAllowCheckPanMod10
            cursor.getShort(offset + 12) != 0, // isRequirePIN
            cursor.getShort(offset + 13) != 0 // isRequireMaskExpiry
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, Issuer entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setFloorLimit(cursor.getLong(offset + 2));
        entity.setAdjustPercent(cursor.getFloat(offset + 3));
        entity.setPanMaskPattern(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setIsEnableAdjust(cursor.getShort(offset + 5) != 0);
        entity.setIsEnableOffline(cursor.getShort(offset + 6) != 0);
        entity.setIsAllowExpiry(cursor.getShort(offset + 7) != 0);
        entity.setIsAllowManualPan(cursor.getShort(offset + 8) != 0);
        entity.setIsAllowCheckExpiry(cursor.getShort(offset + 9) != 0);
        entity.setIsAllowPrint(cursor.getShort(offset + 10) != 0);
        entity.setIsAllowCheckPanMod10(cursor.getShort(offset + 11) != 0);
        entity.setIsRequirePIN(cursor.getShort(offset + 12) != 0);
        entity.setIsRequireMaskExpiry(cursor.getShort(offset + 13) != 0);
     }
    
    @Override
    protected final Long updateKeyAfterInsert(Issuer entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(Issuer entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(Issuer entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
