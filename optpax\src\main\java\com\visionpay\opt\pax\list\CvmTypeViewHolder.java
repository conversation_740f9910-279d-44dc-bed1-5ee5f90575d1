/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.list;

import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.visionpay.opt.pax.R;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/19
 */
public class CvmTypeViewHolder extends RecyclerView.ViewHolder {
    public ImageView cvmIcon;
    public TextView cvmText;

    public CvmTypeViewHolder(@NonNull View itemView) {
        super(itemView);

        cvmIcon = itemView.findViewById(R.id.cvm_type_icon);
        cvmText = itemView.findViewById(R.id.cvm_type_text);
    }
}
