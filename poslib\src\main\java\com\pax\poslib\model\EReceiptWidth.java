/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/01/10                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.poslib.model;

/**
 * Receipt width pixel
 */
public enum EReceiptWidth {
    WIDTH_57_MM(384),
    WIDTH_80_MM(576);

    private final int width;

    EReceiptWidth(int width) {
        this.width = width;
    }

    public int getWidth() {
        return width;
    }
}
