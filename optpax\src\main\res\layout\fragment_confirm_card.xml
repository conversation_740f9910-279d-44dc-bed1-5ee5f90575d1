<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/trans_confirm_card"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/card_margin_horizontal"
    android:layout_marginEnd="@dimen/card_margin_horizontal"
    android:layout_marginTop="@dimen/card_spacing"
    app:cardElevation="0dp"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:strokeWidth="@dimen/card_stroke_width"
    app:strokeColor="@color/common_divider"
    app:cardBackgroundColor="@color/main_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/card_padding_horizontal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/card_padding_vertical">

            <ImageView
                android:id="@+id/trans_confirm_issuer_icon"
                android:layout_width="@dimen/icon_with_background_width"
                android:layout_height="@dimen/icon_with_background_height"
                android:paddingVertical="@dimen/icon_with_background_padding_vertical"
                android:paddingHorizontal="@dimen/icon_with_background_padding_horizontal"
                android:background="@drawable/bg_issuer_icon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                tools:src="@drawable/ic_issuer_visa" />

            <TextView
                android:id="@+id/trans_confirm_pan"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/card_content_spacing_horizontal"
                android:textSize="@dimen/text_medium_size"
                android:textColor="@color/main_text"
                android:fontFamily="monospace"
                android:textStyle="bold"
                android:lines="1"
                app:layout_constraintStart_toEndOf="@id/trans_confirm_issuer_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="6210 9458 8813 0001" />

            <TextView
                android:id="@+id/trans_confirm_card_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/card_content_spacing_horizontal"
                android:textSize="@dimen/text_normal_size"
                android:textColor="@color/main_text"
                app:layout_constraintStart_toEndOf="@id/trans_confirm_issuer_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/trans_confirm_issuer_icon"
                tools:text="VISA · EMV Contact" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/trans_confirm_cardholder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_width"
                android:background="@color/common_divider"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/cardholder_name"
                android:textColor="@color/main_text"
                android:textSize="@dimen/text_normal_size" />

            <TextView
                android:id="@+id/trans_confirm_cardholder_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="12dp"
                android:gravity="center_horizontal"
                android:textColor="@color/main_text"
                android:textSize="@dimen/text_large_size"
                android:fontFamily="sans-serif-condensed"
                tools:text="PAX SZ" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <View
                android:id="@+id/trans_confirm_aid_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_width"
                android:background="@color/common_divider"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/trans_confirm_aid_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/list_margin_vertical"
                android:layout_marginBottom="@dimen/list_margin_vertical"
                android:overScrollMode="never"/>

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:id="@+id/trans_confirm_edit_aid_divider"
                android:layout_width="match_parent"
                android:layout_height="@dimen/divider_width"
                android:background="@color/common_divider"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/trans_confirm_edit_aid_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:text="@string/edit_aid"
                android:textColor="@color/main_highlight_button"
                android:minWidth="@dimen/text_button_min_width"
                app:icon="@drawable/ic_edit"
                app:iconTint="@color/main_highlight_button"
                app:layout_constraintTop_toBottomOf="@id/trans_confirm_edit_aid_divider"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>