package com.pax.preflib.builder.pref;

import android.content.Context;
import android.util.AttributeSet;
import androidx.annotation.NonNull;
import androidx.preference.DialogPreference;
import androidx.preference.EditTextPreference;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.preflib.builder.callback.InputValidateCallback;

/**
 * Custom amount EditText dialog Preference.
 * <br>
 * Compared with the EditTextPreference of Jetpack Preference, AmountEditTextDialogPreference is
 * specifically designed for entering the amount. It adapts to many different currency types, not
 * just simple currency symbols plus numbers. In addition, it also prohibits the cursor display
 * of EditText, and the input effect is consistent with CustomKeyboardEditText.
 * <br><br>
 * 相较于 Jetpack Preference 的 EditTextPreference 来说，AmountEditTextDialogPreference
 * 专门为了输入金额而设计。它适配了多种不同的货币类型，不仅仅是简单的货币符号加数字。另外，它还禁止了
 * EditText 的光标显示，输入效果和 CustomKeyboardEditText 保持一致。
 */
public abstract class BaseAmountDialogPreference extends DialogPreference {
    public BaseAmountDialogPreference(Context context, AttributeSet attrs,
            int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public BaseAmountDialogPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public BaseAmountDialogPreference(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public BaseAmountDialogPreference(Context context) {
        super(context);
    }

    /**
     * Set amount
     *
     * @param amount Amount
     */
    public abstract void setAmount(long amount);

    /**
     * Get amount
     *
     * @return Amount
     */
    public abstract long getAmount();

    /**
     * Set OnBindEditTextListener for EditText
     *
     * @param onBindEditTextListener OnBindEditTextListener
     */
    public abstract void setOnBindEditTextListener(EditTextPreference.OnBindEditTextListener onBindEditTextListener);

    /**
     * Set InputValidateCallback for EnterAmountTextWatcher
     *
     * @param inputValidateCallback InputValidateCallback
     */
    public abstract void setInputValidateCallback(InputValidateCallback<Long> inputValidateCallback);

    public static final class AmountSummaryProvider implements SummaryProvider<BaseAmountDialogPreference> {
        private static AmountSummaryProvider amountSummaryProvider;

        private AmountSummaryProvider() { }

        @NonNull
        public static AmountSummaryProvider getInstance() {
            if (amountSummaryProvider == null) {
                amountSummaryProvider = new AmountSummaryProvider();
            }
            return amountSummaryProvider;
        }

        @Override
        public CharSequence provideSummary(BaseAmountDialogPreference preference) {
            return CurrencyConverter.convert(preference.getAmount());
        }
    }
}
