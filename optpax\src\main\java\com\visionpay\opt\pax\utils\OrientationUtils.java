/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/24                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.utils;

import android.content.Context;
import android.content.res.Configuration;
import android.os.Build;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.WindowManager;
import android.view.WindowMetrics;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;

/**
 * Screen Orientation Utils.
 */
public class OrientationUtils {
    private static final String TAG = "OrientationUtils";

    private OrientationUtils() {
        // do nothing
    }

    /**
     * Whether current activity is landscape.
     *
     * @return Whether current activity is landscape or not.
     */
    public static boolean isLandScape() {
        int orientation = ActivityStack.getInstance().top()
                .getResources()
                .getConfiguration()
                .orientation;
        if (orientation == Configuration.ORIENTATION_PORTRAIT
                || orientation == Configuration.ORIENTATION_LANDSCAPE) {
            LogUtils.d(TAG, "orientation: " + orientation);
            return orientation == Configuration.ORIENTATION_LANDSCAPE;
        }
        int height = 0;
        int width = 0;
        WindowManager windowManager = (WindowManager) BaseApplication.getAppContext()
                .getSystemService(Context.WINDOW_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            WindowMetrics windowMetrics = windowManager.getCurrentWindowMetrics();
            height = windowMetrics.getBounds().height();
            width = windowMetrics.getBounds().width();
        } else {
            DisplayMetrics metrics = new DisplayMetrics();
            Display display = windowManager.getDefaultDisplay();
            display.getMetrics(metrics);
            height = metrics.heightPixels + ResourceUtil.getNavigationBarHeightPixels();
            width = metrics.widthPixels;
        }
        LogUtils.d(TAG, "height: " + height + ", width: " + width);
        return width > height;
    }
}
