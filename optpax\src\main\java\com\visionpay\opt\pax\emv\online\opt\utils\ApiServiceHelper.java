package com.visionpay.opt.pax.emv.online.opt.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.visionpay.opt.pax.emv.online.opt.messages.responses.BaseResponse;

import java.io.IOException;

import retrofit2.Call;
import retrofit2.Response;

public class ApiServiceHelper {
    public static <Res extends BaseResponse> Res executeAPI(Call<Res> resCall) throws ApiException {
        return executeAPI(resCall, true);
    }

    private static <Res extends BaseResponse> Res executeAPI(Call<Res> resCall, boolean reAttempt) throws ApiException {

        try {
            Response<Res> response = resCall.execute();

            Res responseBody;
            if(response.isSuccessful())
                responseBody = response.body();
            //else if((response.code() == 401 || response.code() == 407) && reAttempt){
            //    authenticateKiosk();
            //    return executeAPI(resCall.clone(), false);
            //}
            else {
                Gson gson = (new GsonBuilder()).create();
                String errorBody = response.errorBody().string();
                responseBody = (Res)gson.fromJson(errorBody, BaseResponse.class);
            }

            if(responseBody != null && !responseBody.isSuccess() && responseBody.getMessage() != null && !responseBody.getMessage().isEmpty()) {
                throw new ApiException(response, responseBody.getMessage());
            } else if(responseBody == null || !response.isSuccessful()){
                throw new ApiException(response, "Error performing authentication.");
            }

            return responseBody;
        } catch (IOException e) {
            throw new ApiException(e);
        }
    }
}
