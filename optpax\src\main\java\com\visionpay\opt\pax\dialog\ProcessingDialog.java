/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.dialog;

import android.app.Dialog;
import android.graphics.drawable.Animatable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.visionpay.opt.pax.R;

/**
 * Processing Dialog.
 */
public class ProcessingDialog extends BaseDialogFragment<ProcessingDialog> {
    private TextView titleTextView;
    private TextView infoTextView;

    private String title;
    private String info;

    @Override
    protected int getContentLayoutRes() {
        return R.layout.dialog_prompt;
    }

    @Override
    protected void onCreateDialog(@NonNull Dialog dialog, @NonNull View view,
            @Nullable Window window, @Nullable Bundle savedInstanceState) {
        dialog.setCancelable(false);

        // Icon
        ImageView iconImageView = view.findViewById(R.id.dialog_prompt_icon);
        Drawable drawable = iconImageView.getDrawable();
        if (drawable instanceof Animatable) {
            ((Animatable) drawable).start();
        }

        // Title
        titleTextView = view.findViewById(R.id.dialog_prompt_title);
        if (title != null && !title.isEmpty()) {
            titleTextView.setText(title);
        }

        // Info
        infoTextView = view.findViewById(R.id.dialog_prompt_info);
        if (info != null && !info.isEmpty()) {
            infoTextView.setText(info);
        }
    }

    /**
     * Set dialog title.
     *
     * Please execute set dialog title before {@code show()} this dialog.
     * @param title Dialog title.
     * @return This dialog.
     */
    public ProcessingDialog setTitle(String title) {
        this.title = title;
        if (titleTextView != null) {
            titleTextView.setText(title);
        }
        return this;
    }

    /**
     * Set dialog information.
     *
     * Please execute set dialog information before {@code show()} this dialog.
     * @param info Dialog information.
     * @return This dialog.
     */
    public ProcessingDialog setInfo(String info) {
        this.info = info;
        if (infoTextView != null) {
            infoTextView.setText(info);
        }
        return this;
    }
}
