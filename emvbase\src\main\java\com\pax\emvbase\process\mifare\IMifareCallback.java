package com.pax.emvbase.process.mifare;

import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.entity.OnlineResultWrapper;

/**
 * callback during contact process
 */
public interface IMifareCallback {
    /**
     * When need to enter pin
     * @param isOnlinePin is online pin
     * @param supportPINByPass  supportPINByPass
     * @param leftTimes left retry Times
     * @param pinData pinData
     * @return enter pin result
     */
    int onCardHolderPwd(boolean isOnlinePin, boolean supportPINByPass, int leftTimes, byte[] pinData);

    /**
     * finish card interaction
     */
    void onRemoveCard();

    /**
     * need show card confirm ui,include timeout condition,can use IEmvContactService to get card info
     * @return result
     */
    int showConfirmCard();

    /**
     * need show online process
     * @return OnlineResultWrapper
     */
    OnlineResultWrapper startOnlineProcess(long amount, String terminalId, int detectResult, String reference, ISendingOnlineCallback sendinfOnlineCallback);
}
