/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/01/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.factory.impl;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.preference.EditTextPreference;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceCategory;
import androidx.preference.SwitchPreference;
import com.pax.preflib.builder.factory.IPreferenceFactory;
import com.pax.preflib.builder.factory.PrefFactoryConst;
import com.pax.preflib.builder.pref.BaseAmountDialogPreference;
import com.pax.preflib.builder.pref.BaseSeekBarDialogPreference;
import com.sankuai.waimai.router.annotation.RouterService;

/**
 * Default Preference Factory.
 *
 * If you want to replace the default Preference implementation, you can provide a custom
 * Preference instance in this class. Alternatively, you can also implement the
 * IPreferenceFactory interface.
 */
@RouterService(interfaces = IPreferenceFactory.class, key = PrefFactoryConst.DEFAULT)
public class DefaultPrefFactory implements IPreferenceFactory {

    @NonNull
    @Override
    public BaseAmountDialogPreference createAmountEditTextDialogPreference(@NonNull Context context) {
        return new AmountEditTextDialogPreference(context);
    }

    @NonNull
    @Override
    public EditTextPreference createEditTextPreference(@NonNull Context context) {
        return new EditTextPreference(context);
    }

    @NonNull
    @Override
    public ListPreference createListPreference(@NonNull Context context) {
        return new ListPreference(context);
    }

    @NonNull
    @Override
    public Preference createPreference(@NonNull Context context) {
        return new Preference(context);
    }

    @NonNull
    @Override
    public BaseSeekBarDialogPreference createSeekBarDialogPreference(@NonNull Context context) {
        return new SeekBarDialogPreference(context);
    }

    @NonNull
    @Override
    public SwitchPreference createSwitchPreference(@NonNull Context context) {
        return new SwitchPreference(context);
    }

    @NonNull
    @Override
    public PreferenceCategory createPreferenceCategory(@NonNull Context context) {
        return new PreferenceCategory(context);
    }
}
