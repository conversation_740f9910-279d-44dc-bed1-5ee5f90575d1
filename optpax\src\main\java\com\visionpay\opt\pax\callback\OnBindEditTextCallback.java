/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.callback;

import android.view.View;
import android.widget.EditText;

/**
 * Bind EditText Callback.
 *
 * When dialog display EditText, this callback will be executed.
 */
public interface OnBindEditTextCallback {
    void onBind(View editText);
}
