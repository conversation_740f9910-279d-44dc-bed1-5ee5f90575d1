/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.Preference;
import com.pax.preflib.builder.callback.ApplyCallback;
import com.pax.preflib.utils.PrefBuilderManager;

/**
 * Base Preference builder class.
 *
 * @param <P> Preference class
 * @param <B> Builder class
 */
abstract class BasePrefBuilder<P extends Preference, B extends BasePrefBuilder<P, B>> {
    protected final Context context;
    protected final String key;
    protected final String title;
    protected Preference.OnPreferenceClickListener onPreferenceClickListener;

    protected BasePrefBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        this.context = context;
        this.key = key;
        this.title = title;
    }

    /**
     * Sets the key of a preference that this preference will depend on. If that preference is
     * not set or is off, this preference will be disabled.
     *
     * @param dependencyKey The key of the preference that this depends on
     */
    public B setDependency(@NonNull String dependencyKey) {
        PrefBuilderManager.registerDependency(key, dependencyKey);
        return (B) this;
    }

    /**
     * Sets the callback to be invoked when this Preference is clicked.
     *
     * @param listener The callback to be invoked.
     */
    public B setOnClickListener(@Nullable Preference.OnPreferenceClickListener listener) {
        this.onPreferenceClickListener = listener;
        return (B) this;
    }

    /**
     * Get Preference object.
     *
     * @return Preference object
     */
    @NonNull
    public abstract P build();

    /**
     * Get Preference object and do something after build.
     * <br>
     * Sometimes you might use PrefBuilder like this:
     * <pre>
     * Preference preference = XXXPrefBuilder
     *         .newInstance(context, "Key", "Title")
     *         .build();
     * preference.setXXX();
     * screen.addPreference(preference);
     * </pre>
     * This is because sometimes after the Preference is created, you need to use the Preference
     * method to perform some custom settings or logic. But this is very inelegant.
     * <br>
     * Therefore, it is recommended that you write like this in {@code buildAndApply()}:
     * <pre>
     * screen.addPreference(XXXPrefBuilder
     *         .newInstance(context, "Key", "Title")
     *         .buildAndApply(source -> source.setXXX()));
     * </pre>
     * In this way, you can reduce the declaration of a variable and look more comfortable.
     * <br><br>
     * 有时候你可能会这样使用PrefBuilder：
     * <pre>
     * Preference preference = XXXPrefBuilder
     *         .newInstance(context, "Key", "Title")
     *         .build();
     * preference.setXXX();
     * screen.addPreference(preference);
     * </pre>
     * 这是因为在创建了Preference以后，还要利用Preference的方法来执行一些自定义的设置或者逻辑。但是这样做不够优雅，
     * 对于强迫症来说非常不友好。
     * <br>
     * 所以，更推荐您在{@code buildAndApply()}中这样写：
     * <pre>
     * screen.addPreference(XXXPrefBuilder
     *         .newInstance(context, "Key", "Title")
     *         .buildAndApply(source -> source.setXXX()));
     * </pre>
     * 用这种方式可以减少声明一个变量，看起来也更舒服。
     * @param callback Apply callback
     * @return Preference object
     */
    @NonNull
    public final P buildAndApply(@NonNull ApplyCallback<P> callback) {
        P preference = build();
        callback.apply(preference);
        return preference;
    }
}
