/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/26                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import com.pax.commonlib.utils.LogUtils;
import com.pax.preflib.builder.callback.PrefChangedCallback;
import com.pax.preflib.builder.factory.PrefFactory;
import com.pax.preflib.builder.pref.BaseSeekBarDialogPreference;
import java.util.LinkedList;
import java.util.List;

/**
 * Create SeekBarDialogPreference builder.
 */
public class SeekBarDialogPrefBuilder extends BasePrefBuilder<BaseSeekBarDialogPreference, SeekBarDialogPrefBuilder>{
    private float min = 0f;
    private float max = 100f;
    private float increment = 1f;
    private boolean showDecimal = true;
    private String format;
    private final List<PrefChangedCallback<BaseSeekBarDialogPreference, Float>> prefChangedCallbackList = new LinkedList<>();

    protected SeekBarDialogPrefBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        super(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param titleId Title string resource id
     * @return New instance
     */
    public static SeekBarDialogPrefBuilder newInstance(@NonNull Context context,
            @NonNull String key, @StringRes int titleId) {
        return new SeekBarDialogPrefBuilder(context, key, context.getString(titleId));
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param title Title
     * @return New instance
     */
    public static SeekBarDialogPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @NonNull String title) {
        return new SeekBarDialogPrefBuilder(context, key, title);
    }

    /**
     * Set minimum value.
     *
     * @param min Minimum value
     * @return This builder
     */
    public SeekBarDialogPrefBuilder setMin(float min) {
        this.min = min;
        return this;
    }

    /**
     * Set maximum value.
     *
     * @param max Maximum value
     * @return This builder
     */
    public SeekBarDialogPrefBuilder setMax(float max) {
        this.max = max;
        return this;
    }

    /**
     * Set increment value.
     *
     * @param increment Increment value
     * @return This builder
     */
    public SeekBarDialogPrefBuilder setIncrement(float increment) {
        this.increment = increment;
        return this;
    }

    /**
     * Whether need to show decimal format value.
     *
     * @param showDecimal Whether need to show decimal format value
     * @return This builder
     */
    public SeekBarDialogPrefBuilder setShowDecimal(boolean showDecimal) {
        this.showDecimal = showDecimal;
        return this;
    }

    /**
     * Set String value format. If the display format is set by this method, the {@code
     * setShowDecimal()} method will be ignored.
     * <br>
     * The format set by this method will be used in the dialog box and in the summary.
     *
     * @param format String value format.
     * @return This builder
     */
    public SeekBarDialogPrefBuilder setFormat(@Nullable String format) {
        this.format = format;
        return this;
    }

    public SeekBarDialogPrefBuilder addPrefChangedCallback(@NonNull PrefChangedCallback<BaseSeekBarDialogPreference, Float> callback) {
        prefChangedCallbackList.add(callback);
        return this;
    }

    private boolean verifyNewValue(BaseSeekBarDialogPreference preference, float newValue) {
        for (PrefChangedCallback<BaseSeekBarDialogPreference, Float> callback :
                prefChangedCallbackList) {
            if (!callback.onChanged(preference, newValue)) {
                return false;
            }
        }
        return true;
    }

    @NonNull
    @Override
    public BaseSeekBarDialogPreference build() {
        BaseSeekBarDialogPreference preference = PrefFactory.getInstance()
                .createSeekBarDialogPreference(context);
        preference.setKey(key);
        preference.setTitle(title);
        preference.setDialogTitle(title);
        preference.setMin(min);
        preference.setMax(max);
        preference.setIncrement(increment);
        if (format != null) {
            preference.setFormat(format);
        } else {
            if (showDecimal) {
                preference.setFormat("%.2f");
            } else {
                preference.setFormat("%.0f");
            }
        }
        preference.setIconSpaceReserved(false);
        preference.setSummaryProvider(BaseSeekBarDialogPreference.SimpleSummaryProvider.getInstance());
        if (onPreferenceClickListener != null) {
            preference.setOnPreferenceClickListener(onPreferenceClickListener);
        }
        if (!prefChangedCallbackList.isEmpty()) {
            preference.setOnPreferenceChangeListener((pref, newValue) -> {
                if (newValue instanceof Float || newValue instanceof Double) {
                    return verifyNewValue(preference, (float) newValue);
                } else if (newValue instanceof Number) {
                    return verifyNewValue(preference, ((Number) newValue).floatValue());
                } else if (newValue != null) {
                    String s = newValue.toString();
                    if (TextUtils.isEmpty(s)) {
                        return false;
                    }
                    try {
                        float value = Float.parseFloat(s);
                        return verifyNewValue(preference, value);
                    } catch (Exception e) {
                        LogUtils.e(e);
                    }
                    return false;
                } else {
                    return false;
                }
            });
        }
        return preference;
    }
}
