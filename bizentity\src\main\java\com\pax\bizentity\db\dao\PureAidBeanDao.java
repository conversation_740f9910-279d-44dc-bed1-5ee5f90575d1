package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.clss.pure.PureAidBean;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "pure_aid".
*/
public class PureAidBeanDao extends AbstractDao<PureAidBean, Long> {

    public static final String TABLENAME = "pure_aid";

    /**
     * Properties of entity PureAidBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "pure_id");
        public final static Property AppName = new Property(1, String.class, "appName", false, "APP_NAME");
        public final static Property Aid = new Property(2, String.class, "aid", false, "AID");
        public final static Property SelFlag = new Property(3, byte.class, "selFlag", false, "SEL_FLAG");
        public final static Property TransLimit = new Property(4, long.class, "transLimit", false, "TRANS_LIMIT");
        public final static Property TransLimitFlag = new Property(5, byte.class, "transLimitFlag", false, "TRANS_LIMIT_FLAG");
        public final static Property FloorLimit = new Property(6, long.class, "floorLimit", false, "FLOOR_LIMIT");
        public final static Property FloorLimitFlag = new Property(7, byte.class, "floorLimitFlag", false, "FLOOR_LIMIT_FLAG");
        public final static Property CvmLimit = new Property(8, long.class, "cvmLimit", false, "CVM_LIMIT");
        public final static Property CvmLimitFlag = new Property(9, byte.class, "cvmLimitFlag", false, "CVM_LIMIT_FLAG");
        public final static Property TacDenial = new Property(10, String.class, "tacDenial", false, "TAC_DENIAL");
        public final static Property TacOnline = new Property(11, String.class, "tacOnline", false, "TAC_ONLINE");
        public final static Property TacDefault = new Property(12, String.class, "tacDefault", false, "TAC_DEFAULT");
        public final static Property AcquirerId = new Property(13, String.class, "acquirerId", false, "ACQUIRER_ID");
        public final static Property DDOL = new Property(14, String.class, "dDOL", false, "D_DOL");
        public final static Property Version = new Property(15, String.class, "version", false, "VERSION");
        public final static Property TerminalType = new Property(16, String.class, "terminalType", false, "TERMINAL_TYPE");
        public final static Property TerminalCapability = new Property(17, String.class, "terminalCapability", false, "TERMINAL_CAPABILITY");
        public final static Property TerminalAdditionalCapability = new Property(18, String.class, "terminalAdditionalCapability", false, "TERMINAL_ADDITIONAL_CAPABILITY");
        public final static Property AddTagObjList = new Property(19, String.class, "addTagObjList", false, "ADD_TAG_OBJ_LIST");
        public final static Property MandatoryTagObjList = new Property(20, String.class, "mandatoryTagObjList", false, "MANDATORY_TAG_OBJ_LIST");
        public final static Property AuthTransDataTagObjList = new Property(21, String.class, "authTransDataTagObjList", false, "AUTH_TRANS_DATA_TAG_OBJ_LIST");
        public final static Property ClssKernelCapability = new Property(22, String.class, "clssKernelCapability", false, "CLSS_KERNEL_CAPABILITY");
        public final static Property ClssPOSImplOption = new Property(23, String.class, "clssPOSImplOption", false, "CLSS_POSIMPL_OPTION");
        public final static Property AppAuthTransType = new Property(24, String.class, "appAuthTransType", false, "APP_AUTH_TRANS_TYPE");
        public final static Property RefundTacDenial = new Property(25, String.class, "refundTacDenial", false, "REFUND_TAC_DENIAL");
        public final static Property Timeout = new Property(26, String.class, "timeout", false, "TIMEOUT");
        public final static Property MemorySlotReadTemp = new Property(27, String.class, "memorySlotReadTemp", false, "MEMORY_SLOT_READ_TEMP");
        public final static Property MemorySlotUpdateTemp = new Property(28, String.class, "memorySlotUpdateTemp", false, "MEMORY_SLOT_UPDATE_TEMP");
        public final static Property ImpOption = new Property(29, String.class, "impOption", false, "IMP_OPTION");
        public final static Property CheckExceptionFilePan = new Property(30, String.class, "checkExceptionFilePan", false, "CHECK_EXCEPTION_FILE_PAN");
    }


    public PureAidBeanDao(DaoConfig config) {
        super(config);
    }
    
    public PureAidBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"pure_aid\" (" + //
                "\"pure_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"APP_NAME\" TEXT," + // 1: appName
                "\"AID\" TEXT," + // 2: aid
                "\"SEL_FLAG\" INTEGER NOT NULL ," + // 3: selFlag
                "\"TRANS_LIMIT\" INTEGER NOT NULL ," + // 4: transLimit
                "\"TRANS_LIMIT_FLAG\" INTEGER NOT NULL ," + // 5: transLimitFlag
                "\"FLOOR_LIMIT\" INTEGER NOT NULL ," + // 6: floorLimit
                "\"FLOOR_LIMIT_FLAG\" INTEGER NOT NULL ," + // 7: floorLimitFlag
                "\"CVM_LIMIT\" INTEGER NOT NULL ," + // 8: cvmLimit
                "\"CVM_LIMIT_FLAG\" INTEGER NOT NULL ," + // 9: cvmLimitFlag
                "\"TAC_DENIAL\" TEXT," + // 10: tacDenial
                "\"TAC_ONLINE\" TEXT," + // 11: tacOnline
                "\"TAC_DEFAULT\" TEXT," + // 12: tacDefault
                "\"ACQUIRER_ID\" TEXT," + // 13: acquirerId
                "\"D_DOL\" TEXT," + // 14: dDOL
                "\"VERSION\" TEXT," + // 15: version
                "\"TERMINAL_TYPE\" TEXT," + // 16: terminalType
                "\"TERMINAL_CAPABILITY\" TEXT," + // 17: terminalCapability
                "\"TERMINAL_ADDITIONAL_CAPABILITY\" TEXT," + // 18: terminalAdditionalCapability
                "\"ADD_TAG_OBJ_LIST\" TEXT," + // 19: addTagObjList
                "\"MANDATORY_TAG_OBJ_LIST\" TEXT," + // 20: mandatoryTagObjList
                "\"AUTH_TRANS_DATA_TAG_OBJ_LIST\" TEXT," + // 21: authTransDataTagObjList
                "\"CLSS_KERNEL_CAPABILITY\" TEXT," + // 22: clssKernelCapability
                "\"CLSS_POSIMPL_OPTION\" TEXT," + // 23: clssPOSImplOption
                "\"APP_AUTH_TRANS_TYPE\" TEXT," + // 24: appAuthTransType
                "\"REFUND_TAC_DENIAL\" TEXT," + // 25: refundTacDenial
                "\"TIMEOUT\" TEXT," + // 26: timeout
                "\"MEMORY_SLOT_READ_TEMP\" TEXT," + // 27: memorySlotReadTemp
                "\"MEMORY_SLOT_UPDATE_TEMP\" TEXT," + // 28: memorySlotUpdateTemp
                "\"IMP_OPTION\" TEXT," + // 29: impOption
                "\"CHECK_EXCEPTION_FILE_PAN\" TEXT);"); // 30: checkExceptionFilePan
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"pure_aid\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, PureAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
 
        String tacDenial = entity.getTacDenial();
        if (tacDenial != null) {
            stmt.bindString(11, tacDenial);
        }
 
        String tacOnline = entity.getTacOnline();
        if (tacOnline != null) {
            stmt.bindString(12, tacOnline);
        }
 
        String tacDefault = entity.getTacDefault();
        if (tacDefault != null) {
            stmt.bindString(13, tacDefault);
        }
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(14, acquirerId);
        }
 
        String dDOL = entity.getDDOL();
        if (dDOL != null) {
            stmt.bindString(15, dDOL);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(16, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(17, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(18, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(19, terminalAdditionalCapability);
        }
 
        String addTagObjList = entity.getAddTagObjList();
        if (addTagObjList != null) {
            stmt.bindString(20, addTagObjList);
        }
 
        String mandatoryTagObjList = entity.getMandatoryTagObjList();
        if (mandatoryTagObjList != null) {
            stmt.bindString(21, mandatoryTagObjList);
        }
 
        String authTransDataTagObjList = entity.getAuthTransDataTagObjList();
        if (authTransDataTagObjList != null) {
            stmt.bindString(22, authTransDataTagObjList);
        }
 
        String clssKernelCapability = entity.getClssKernelCapability();
        if (clssKernelCapability != null) {
            stmt.bindString(23, clssKernelCapability);
        }
 
        String clssPOSImplOption = entity.getClssPOSImplOption();
        if (clssPOSImplOption != null) {
            stmt.bindString(24, clssPOSImplOption);
        }
 
        String appAuthTransType = entity.getAppAuthTransType();
        if (appAuthTransType != null) {
            stmt.bindString(25, appAuthTransType);
        }
 
        String refundTacDenial = entity.getRefundTacDenial();
        if (refundTacDenial != null) {
            stmt.bindString(26, refundTacDenial);
        }
 
        String timeout = entity.getTimeout();
        if (timeout != null) {
            stmt.bindString(27, timeout);
        }
 
        String memorySlotReadTemp = entity.getMemorySlotReadTemp();
        if (memorySlotReadTemp != null) {
            stmt.bindString(28, memorySlotReadTemp);
        }
 
        String memorySlotUpdateTemp = entity.getMemorySlotUpdateTemp();
        if (memorySlotUpdateTemp != null) {
            stmt.bindString(29, memorySlotUpdateTemp);
        }
 
        String impOption = entity.getImpOption();
        if (impOption != null) {
            stmt.bindString(30, impOption);
        }
 
        String checkExceptionFilePan = entity.getCheckExceptionFilePan();
        if (checkExceptionFilePan != null) {
            stmt.bindString(31, checkExceptionFilePan);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, PureAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
 
        String tacDenial = entity.getTacDenial();
        if (tacDenial != null) {
            stmt.bindString(11, tacDenial);
        }
 
        String tacOnline = entity.getTacOnline();
        if (tacOnline != null) {
            stmt.bindString(12, tacOnline);
        }
 
        String tacDefault = entity.getTacDefault();
        if (tacDefault != null) {
            stmt.bindString(13, tacDefault);
        }
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(14, acquirerId);
        }
 
        String dDOL = entity.getDDOL();
        if (dDOL != null) {
            stmt.bindString(15, dDOL);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(16, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(17, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(18, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(19, terminalAdditionalCapability);
        }
 
        String addTagObjList = entity.getAddTagObjList();
        if (addTagObjList != null) {
            stmt.bindString(20, addTagObjList);
        }
 
        String mandatoryTagObjList = entity.getMandatoryTagObjList();
        if (mandatoryTagObjList != null) {
            stmt.bindString(21, mandatoryTagObjList);
        }
 
        String authTransDataTagObjList = entity.getAuthTransDataTagObjList();
        if (authTransDataTagObjList != null) {
            stmt.bindString(22, authTransDataTagObjList);
        }
 
        String clssKernelCapability = entity.getClssKernelCapability();
        if (clssKernelCapability != null) {
            stmt.bindString(23, clssKernelCapability);
        }
 
        String clssPOSImplOption = entity.getClssPOSImplOption();
        if (clssPOSImplOption != null) {
            stmt.bindString(24, clssPOSImplOption);
        }
 
        String appAuthTransType = entity.getAppAuthTransType();
        if (appAuthTransType != null) {
            stmt.bindString(25, appAuthTransType);
        }
 
        String refundTacDenial = entity.getRefundTacDenial();
        if (refundTacDenial != null) {
            stmt.bindString(26, refundTacDenial);
        }
 
        String timeout = entity.getTimeout();
        if (timeout != null) {
            stmt.bindString(27, timeout);
        }
 
        String memorySlotReadTemp = entity.getMemorySlotReadTemp();
        if (memorySlotReadTemp != null) {
            stmt.bindString(28, memorySlotReadTemp);
        }
 
        String memorySlotUpdateTemp = entity.getMemorySlotUpdateTemp();
        if (memorySlotUpdateTemp != null) {
            stmt.bindString(29, memorySlotUpdateTemp);
        }
 
        String impOption = entity.getImpOption();
        if (impOption != null) {
            stmt.bindString(30, impOption);
        }
 
        String checkExceptionFilePan = entity.getCheckExceptionFilePan();
        if (checkExceptionFilePan != null) {
            stmt.bindString(31, checkExceptionFilePan);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public PureAidBean readEntity(Cursor cursor, int offset) {
        PureAidBean entity = new PureAidBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // appName
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // aid
            (byte) cursor.getShort(offset + 3), // selFlag
            cursor.getLong(offset + 4), // transLimit
            (byte) cursor.getShort(offset + 5), // transLimitFlag
            cursor.getLong(offset + 6), // floorLimit
            (byte) cursor.getShort(offset + 7), // floorLimitFlag
            cursor.getLong(offset + 8), // cvmLimit
            (byte) cursor.getShort(offset + 9), // cvmLimitFlag
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // tacDenial
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // tacOnline
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // tacDefault
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // acquirerId
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // dDOL
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // version
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // terminalType
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // terminalCapability
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // terminalAdditionalCapability
            cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19), // addTagObjList
            cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20), // mandatoryTagObjList
            cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21), // authTransDataTagObjList
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // clssKernelCapability
            cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23), // clssPOSImplOption
            cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24), // appAuthTransType
            cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25), // refundTacDenial
            cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26), // timeout
            cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27), // memorySlotReadTemp
            cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28), // memorySlotUpdateTemp
            cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29), // impOption
            cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30) // checkExceptionFilePan
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, PureAidBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAppName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAid(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSelFlag((byte) cursor.getShort(offset + 3));
        entity.setTransLimit(cursor.getLong(offset + 4));
        entity.setTransLimitFlag((byte) cursor.getShort(offset + 5));
        entity.setFloorLimit(cursor.getLong(offset + 6));
        entity.setFloorLimitFlag((byte) cursor.getShort(offset + 7));
        entity.setCvmLimit(cursor.getLong(offset + 8));
        entity.setCvmLimitFlag((byte) cursor.getShort(offset + 9));
        entity.setTacDenial(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setTacOnline(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setTacDefault(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setAcquirerId(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setDDOL(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setVersion(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setTerminalType(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setTerminalCapability(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setTerminalAdditionalCapability(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setAddTagObjList(cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19));
        entity.setMandatoryTagObjList(cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20));
        entity.setAuthTransDataTagObjList(cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21));
        entity.setClssKernelCapability(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setClssPOSImplOption(cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23));
        entity.setAppAuthTransType(cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24));
        entity.setRefundTacDenial(cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25));
        entity.setTimeout(cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26));
        entity.setMemorySlotReadTemp(cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27));
        entity.setMemorySlotUpdateTemp(cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28));
        entity.setImpOption(cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29));
        entity.setCheckExceptionFilePan(cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(PureAidBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(PureAidBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(PureAidBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
