<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2021/04/01                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <path
        android:pathData="M5,3L19,3A2,2 0,0 1,21 5L21,9A2,2 0,0 1,19 11L5,11A2,2 0,0 1,3 9L3,5A2,2 0,0 1,5 3z"
        android:strokeWidth="2"
        android:strokeColor="@color/color_search_card_icon"/>
    <path
        android:pathData="M18,11L18,19C18,20.1046 17.1046,21 16,21L8,21C6.8954,21 6,20.1046 6,19L6,11"
        android:strokeWidth="2"
        android:strokeColor="@color/color_search_card_icon"/>
    <path
        android:pathData="M12.5,11L13.5,11A0.5,0.5 0,0 1,14 11.5L14,12A0.5,0.5 0,0 1,13.5 12.5L12.5,12.5A0.5,0.5 0,0 1,12 12L12,11.5A0.5,0.5 0,0 1,12.5 11z"
        android:strokeWidth="2"
        android:strokeColor="@color/color_search_card_icon"/>
    <path
        android:pathData="M9,8l3,-2.0041l3,2.0041"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:strokeColor="@color/color_search_card_icon"
        android:strokeLineCap="round"/>
</vector>
