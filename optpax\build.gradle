plugins {
    id 'com.android.application'
    id 'WMRouter'
}

def static releaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("UTC"))
}

def loadSigningConfigs() {
    def Properties props = new Properties()
    def propFile = file('../signing.properties')
    if (propFile.canRead()) {
        props.load(new FileInputStream(propFile))
        if (props != null && props.containsKey('RELEASE_STORE_FILE') && props.containsKey('RELEASE_STORE_PASSWORD') &&
                props.containsKey('RELEASE_KEY_ALIAS') && props.containsKey('RELEASE_KEY_PASSWORD')) {
            android.signingConfigs.release.storeFile = file(props['RELEASE_STORE_FILE'])
            android.signingConfigs.release.storePassword = props['RELEASE_STORE_PASSWORD']
            android.signingConfigs.release.keyAlias = props['RELEASE_KEY_ALIAS']
            android.signingConfigs.release.keyPassword = props['RELEASE_KEY_PASSWORD']
        } else {
            android.buildTypes.release.signingConfig = null
        }
    } else {
        android.buildTypes.release.signingConfig = null
    }
}

android {
    compileSdk rootProject.compileSdkVersion

    defaultConfig {
        applicationId 'com.visionpay.opt.pax'
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        versionCode 19
        versionName "0.00.25" + "_" + releaseTime()
        multiDexEnabled true
        multiDexKeepProguard file('multiDexKeep.pro')
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters 'armeabi'
        }
    }

    lintOptions {
        abortOnError false
    }

    //签名
    signingConfigs {
        debug {
        }
        release {
        }
    }

    buildTypes {
        debug {
            debuggable true
            buildConfigField('boolean','RELEASE','false')
            buildConfigField('boolean','needRemoveCard','true')
            buildConfigField('String', 'opt_api','"http://apioptstaging.posmaster.app"') // api.staging.opt.posmaster.com.au
            versionNameSuffix "-debug"
            minifyEnabled false
            zipAlignEnabled false
            shrinkResources false
        }
        release {
            buildConfigField('boolean','RELEASE','true')
            buildConfigField('boolean','needRemoveCard','true')
            buildConfigField('String', 'opt_api','"https://apiopt.posmaster.app"') // api.opt.posmaster.com.au
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'),
            //        'proguard-rules.pro'
            //minifyEnabled true
            zipAlignEnabled false
            shrinkResources false

            loadSigningConfigs()
            //签名
            signingConfig signingConfigs.release
        }
        stagingRelease {
            buildConfigField('boolean','RELEASE','true')
            buildConfigField('boolean','needRemoveCard','true')
            buildConfigField('String', 'opt_api','"http://apioptstaging.posmaster.app"') // api.staging.opt.posmaster.com.au
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'),
            //        'proguard-rules.pro'
            //minifyEnabled true
            zipAlignEnabled false
            shrinkResources false

            loadSigningConfigs()
            //签名
            signingConfig signingConfigs.release
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            if (outputFileName != null && outputFile.name.endsWith('.apk')) {
                def type = ""
                if (variant.buildType.name == 'debug') {
                    type = "_debug"
                }else if(variant.buildType.name == 'autoTest'){
                    type = "_autoTest"
                }
                else if(variant.buildType.name == 'stagingRelease'){
                    type = "_staging"
                }
                def fileName = "VisionPay_V${defaultConfig.versionName}${type}.apk"
                outputFileName = fileName
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    flatDir {
        dirs '../poslib/libs', '../commonui/libs'
    }
    mavenCentral()
}

dependencies {
    def lifecycle_version = "2.4.1"

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':commonui')
    implementation project(':bizlib')
    implementation project(':configservice:impl')
    implementation project(':emvservice:emv')
    implementation project(':preflib')

    implementation "androidx.lifecycle:lifecycle-runtime:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-livedata:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel:$lifecycle_version"

    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation "com.github.bumptech.glide:okhttp-integration:$rootProject.glide"
    implementation 'com.google.code.gson:gson:2.10'
    implementation 'com.whatspos.sdk:paxstore-3rd-app-android-sdk:8.7.2'

    //REST API
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    annotationProcessor "io.github.meituan-dianping:compiler:$rootProject.routerCompiler"
    
    testImplementation "junit:junit:$rootProject.junit"
    testImplementation "org.mockito:mockito-all:$rootProject.mockito"
    testImplementation "org.hamcrest:hamcrest-all:$rootProject.hamcrest"
    testImplementation "androidx.arch.core:core-testing:$rootProject.coreTesting"
    androidTestImplementation "androidx.test:runner:$rootProject.runner"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    androidTestImplementation "junit:junit:$rootProject.junit"
    debugImplementation "com.squareup.leakcanary:leakcanary-android:$rootProject.leakcanary"
}