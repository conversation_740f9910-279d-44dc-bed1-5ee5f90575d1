package com.visionpay.opt.pax.entity;

import androidx.annotation.Keep;

import java.io.Serializable;

@Keep
public class IM30Transaction implements Serializable {

    private int state;

    private String im30Reference;

    private String externalReference;

    private String errorMessage;

    public IM30Transaction(POSTransaction posTransaction, String errorMessage) {
        this(posTransaction);
        state = 0;
        this.errorMessage = errorMessage;
    }

    public IM30Transaction(POSTransaction posTransaction){
        state = 1;
        if(posTransaction != null)
            externalReference = posTransaction.getExternalReference();
        im30Reference = "";//java.util.UUID.randomUUID().toString();
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getIm30Reference() {
        return im30Reference;
    }

    public void setIm30Reference(String im30Reference) {
        this.im30Reference = im30Reference;
    }

    public String getExternalReference() {
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}

