<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="emv_rsp_online_card_denied">Host approved,but card reject</string>
    <string name="emv_rsp_emv_ok">success</string>
    <string name="emv_rsp_icc_reset_err">icc reset error</string>
    <string name="emv_rsp_icc_cmd_err">icc cmd error</string>
    <string name="emv_rsp_icc_block">icc block</string>
    <string name="emv_rsp_emv_rsp_err">emv response error</string>
    <string name="emv_rsp_emv_app_block">emv application block</string>
    <string name="emv_rsp_emv_no_app">emv no application</string>
    <string name="emv_rsp_emv_user_cancel">emv user cancel</string>
    <string name="emv_rsp_emv_time_out">emv timeout</string>
    <string name="emv_rsp_emv_data_err">emv data error</string>
    <string name="emv_rsp_emv_not_accept">emv not accept</string>
    <string name="emv_rsp_emv_denial">emv denial</string>
    <string name="emv_rsp_emv_key_exp">emv key expiry</string>
    <string name="emv_rsp_emv_no_pinpad">emv no pinpad</string>
    <string name="emv_rsp_emv_no_password">emv no password</string>
    <string name="emv_rsp_emv_sum_err">emv checksum error</string>
    <string name="emv_rsp_emv_not_found">emv not found</string>
    <string name="emv_rsp_emv_no_data">emv no data</string>
    <string name="emv_rsp_emv_overflow">emv overflow</string>
    <string name="emv_rsp_no_trans_log">emv no trans log</string>
    <string name="emv_rsp_record_notexist">emv recode is not existed</string>
    <string name="emv_rsp_logitem_notexist">emv log item is not existed</string>
    <string name="emv_rsp_icc_rsp_6985">icc response 6985</string>
    <string name="emv_rsp_clss_use_contact">clss use contact</string>
    <string name="emv_rsp_emv_file_err">emv file error</string>
    <string name="emv_rsp_clss_terminate">clss terminate</string>
    <string name="emv_rsp_clss_failed">clss failed</string>
    <string name="emv_rsp_clss_decline">clss decline</string>
    <string name="emv_rsp_clss_try_another_card">try another card</string>
    <string name="emv_rsp_emv_param_err">emv parameter error</string>
    <string name="emv_rsp_clss_wave2_oversea">clss wave2 oversea</string>
    <string name="emv_rsp_clss_wave2_terminated">clss wave2 terminated</string>
    <string name="emv_rsp_clss_wave2_us_card">clss wave2 us card</string>
    <string name="emv_rsp_clss_wave3_ins_card">clss wave3 ins card</string>
    <string name="emv_rsp_clss_reselect_app">clss reselect app</string>
    <string name="emv_rsp_clss_card_expired">clss card expired</string>
    <string name="emv_rsp_emv_no_app_ppse_err">clss no app ppse error</string>
    <string name="emv_rsp_clss_use_vsdc">clss use vsdc</string>
    <string name="emv_rsp_clss_cvmdecline">clss cvm decline</string>
    <string name="emv_rsp_clss_refer_consumer_device">clss refer consumer device</string>
    <string name="emv_rsp_clss_last_cmd_err">clss last cmd error</string>
    <string name="emv_rsp_clss_api_order_err">clss api order error</string>
    <string name="emv_rsp_clss_torn_cardnum_err">clss torn cardnum error</string>
    <string name="emv_rsp_clss_torn_aid_err">clss torn aid error</string>
    <string name="emv_rsp_clss_torn_amt_err">clss torn amount error</string>
    <string name="emv_rsp_clss_card_expired_req_online">clss expired requset online</string>
    <string name="emv_rsp_clss_file_not_found">clss file not found</string>
    <string name="emv_rsp_clss_try_again">clss try again</string>
    <string name="emv_rsp_clss_payment_not_accept">clss payment not accept</string>
    <string name="err_undefine">Undefined Error</string>
</resources>