/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.pin;

import android.app.Activity;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.AnyThread;
import androidx.annotation.MainThread;
import androidx.annotation.WorkerThread;
import androidx.fragment.app.FragmentActivity;

import com.pax.bizlib.ped.InjectKeyUtil;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.emvservice.export.IEmvBase;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.dialog.MyEnterPinDialog;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.device.EmvDeviceImpl;
import com.pax.bizlib.ped.PedHelper;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.IPed;
import com.pax.dal.exceptions.PedDevException;
import com.pax.emvbase.constant.EmvConstant;
import com.visionpay.opt.pax.utils.DialogUtils;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicIntegerArray;

/**
 * Offline PIN Task
 *
 * SmartFuel PAX 只支持内置的 PCI 模式 PINPAD，因此实际上 Offline PIN 部分主要就是设置一下监听，更新一下 UI。
 */
@WorkerThread
public class OfflinePinTask extends BasePinTask {
    private static final String TAG = "OfflinePinTask";

    private final int leftTimes;
    private final PinImpl callback = new PinImpl();
    private final IEmvService.ErrorCallback errorCallback;
    private TextView pinEditText;
    private MyEnterPinDialog dialog;

    public OfflinePinTask(int leftTimes, IEmvService.ErrorCallback errorCallback) {
        this.leftTimes = leftTimes;
        this.errorCallback = errorCallback;
    }

    @Override
    public int start(IEmvBase emv, boolean isSupportPinByPass, boolean forceSoftEncrypt) {
        DialogUtils.dismiss();

        // Create Dialog
        App.getApp().runOnUiThread(() -> {
            Activity activity = ActivityStack.getInstance().top();
            if (activity instanceof FragmentActivity) {
                /*DialogUtils.createEnterPinDialog((FragmentActivity) activity,
                        "Enter Offline PIN (" + leftTimes + ")",
                        editText -> pinEditText = editText,
                        dialog -> {
                            pinEditText = null;
                            if (pinService != null) {
                                pinService.setInputPinListener(null);
                            }
                            EmvDeviceImpl.getInstance().setPinCallback(null);
                        });*/
                dialog = DialogUtils.createCustomEnterPinDialog((FragmentActivity) activity,
                        "Enter Offline PIN (" + leftTimes + ")",
                        editText -> pinEditText = (TextView) editText,
                        dialog -> {
                            pinEditText = null;
                            if (pinService != null) {
                                pinService.setInputPinListener(null);
                            }
                            EmvDeviceImpl.getInstance().setPinCallback(null);
                        });
            }
        });

        // Set PED
        IPed ped = PedHelper.getPed();
        pinService.setInputPinListener(new PinServicePCIInputListener(callback));

        try {
            ped.setIntervalTime(1, 1);
            ped.setKeyboardRandom(false);
            InjectKeyUtil.injectMKSK(ConfigKeyConstant.CommType.DEMO);
        } catch (PedDevException e) {
            LogUtils.e(TAG, "PedDevException", e);
        }

        EmvDeviceImpl.getInstance().setPinCallback(callback);

        // Because we are using PCI mode, we can simply return 0 here. Then pedVerifyPlainPin or
        // pedVerifyCipherPin in DeviceImplNeptune will be executed. The cipher pad is invoked when
        // IPed.verifyPlainPin or IPed.verifyCipherPin is executed.
        // 因为我们使用了 PCI 模式，所以这里直接返回 0 就可以了。之后 DeviceImplNeptune 中的
        // pedVerifyPlainPin() 或者 pedVerifyCipherPin() 会被执行，并且在执行 IPed.verifyPlainPin
        // 或者 IPed.verifyCipherPin 时会调起系统的密码键盘。
        return EmvConstant.ContactCallbackStatus.CONTACT_OK;
    }

    @AnyThread
    private void clear() {
        DialogUtils.dismiss();
        if(dialog != null)
            dialog.dismiss();
    }

    private class PinImpl implements PinCallback {
        @MainThread
        @Override
        public void onInput(int inputLen) {
            if (pinEditText != null) {
                StringBuilder builder = new StringBuilder();
                if (inputLen > 0) {
                    for (int i = 0; i < inputLen; i++) {
                        builder.append("●");
                    }
                }
                pinEditText.setText(builder.toString());
            }
        }

        @MainThread
        @Override
        public void onFinish() {
            clear();
        }

        @MainThread
        @Override
        public void onCancel() {
            if (errorCallback != null) {
                errorCallback.onError("PIN Input Cancel", "User cancel PIN Input.");
            }
            clear();
        }

        @MainThread
        @Override
        public void onNoPinPad() {
            if (errorCallback != null) {
                errorCallback.onError("No PIN Pad", "Cannot find PIN Pad. Please check the "
                        + "connection between POS and PIN Pad");
            }
            clear();
        }

        @MainThread
        @Override
        public void onTimeout() {
            if (errorCallback != null) {
                errorCallback.onError("Timeout", "User input PIN timeout.");
            }
            clear();
        }

        @MainThread
        @Override
        public void onError(String reason) {
            if (errorCallback != null) {
                errorCallback.onError("PED Unknown Error", "PED throw an exception: " + reason);
            }
            clear();
        }
    }
}
