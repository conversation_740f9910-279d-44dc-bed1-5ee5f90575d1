/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/01                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.viewmodel;

import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvTag;
import com.visionpay.opt.pax.entity.TransStatus;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.IEmvBase;
import com.pax.emvservice.export.IEmvContactService;
import com.pax.emvservice.export.IEmvContactlessService;
import com.sankuai.waimai.router.Router;
import java.util.List;

/**
 * Transaction CVM Type & EMV Tag List Fragment ViewModel.
 */
public class TransEmvResultViewModel extends BaseTransCardViewModel {
    private static final String TAG = "TransEmvResultViewModel";

    private final MutableLiveData<TransStatus> statusLiveData = new MutableLiveData<>();

    @Override
    public void setStatus(TransStatus status) {
        LogUtils.d(TAG, "Update Status");
        statusLiveData.postValue(status);
    }

    public LiveData<String> listenResultTitle() {
        return Transformations.map(statusLiveData, TransStatus::getTransResult);
    }

    public LiveData<List<CvmType>> listenCvmTypeList() {
        return Transformations.map(statusLiveData, TransStatus::getCvmTypeList);
    }

    public LiveData<List<EmvTag>> listenEmvTagList() {
        return Transformations.distinctUntilChanged(
                Transformations.map(statusLiveData, TransStatus::getEmvTagList));
    }

    public void searchEmvTag(@Nullable String input) {
        if (input != null && !input.isEmpty()) {
            int tag = -1;
            try {
                tag = Integer.parseInt(input, 16);
            } catch (Exception e) {
                LogUtils.e(TAG, "Parse Int Failed", e);
                return;
            }
            if (tag > 0) {
                TransStatus status = statusLiveData.getValue();
                if (status != null) {
                    IEmvBase emv;
                    if (status.isContactService()) {
                        emv = Router.getService(IEmvContactService.class,
                                EmvServiceConstant.EMVSERVICE_CONTACT);
                    } else {
                        emv = Router.getService(IEmvContactlessService.class,
                                EmvServiceConstant.EMVSERVICE_CONTACTLESS);
                    }
                    byte[] result = emv.getTlv(tag);
                    String tagStr = ConvertUtils.bcd2Str(ConvertUtils.intToByteArray(tag,
                            ConvertUtils.EEndian.BIG_ENDIAN));
                    String valueStr = ConvertUtils.bcd2Str(result);
                    if (!valueStr.isEmpty()) {
                        insertEmvTag(tagStr, valueStr);
                    } else {
                        insertEmvTag(tagStr, "(Empty)");
                    }
                }
            }
        }
    }

    public void insertEmvTag(String tag, String value) {
        TransStatus status = statusLiveData.getValue();
        if (status != null) {
            List<EmvTag> emvTagList = status.getEmvTagList();
            emvTagList.add(new EmvTag(tag, value));
        }
    }
}
