package com.pax.preflib.builder;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.preference.Preference;
import androidx.preference.PreferenceCategory;
import com.pax.preflib.builder.callback.ConvertCallback;
import com.pax.preflib.builder.factory.PrefFactory;
import com.pax.preflib.utils.PrefBuilderManager;
import java.util.List;

/**
 * Create PreferenceCategory builder.
 */
public class PrefCategoryBuilder extends BasePrefBuilder<PreferenceCategory, PrefCategoryBuilder> {
    private boolean iconSpaceReserved = true;

    protected PrefCategoryBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        super(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param title Title
     * @return New instance
     */
    public static PrefCategoryBuilder newInstance(@NonNull Context context, @NonNull String key,
            @NonNull String title) {
        return new PrefCategoryBuilder(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param titleId Title string resource id
     * @return New instance
     */
    public static PrefCategoryBuilder newInstance(Context context, String key, @StringRes int titleId) {
        return new PrefCategoryBuilder(context, key, context.getString(titleId));
    }

    /**
     * Add preference instance to this category.
     *
     * @param preference Preference
     * @return This builder
     */
    public PrefCategoryBuilder addPreference(@Nullable Preference preference) {
        if (preference != null) {
            PrefBuilderManager.registerBeforeScreen((screen, fragment) -> {
                PreferenceCategory category = screen.findPreference(key);
                if (category != null) {
                    category.addPreference(preference);
                }
            });
        }
        return this;
    }

    /**
     * Add preference instances to this category.
     *
     * @param list Source list
     * @param callback Callback which used to convert source to preference
     * @param <T> Source type
     * @return This builder
     */
    public <T> PrefCategoryBuilder addPreferences(@NonNull List<T> list,
            @NonNull ConvertCallback<T, Preference> callback) {
        if (list != null && !list.isEmpty() && callback != null) {
            PrefBuilderManager.registerBeforeScreen((screen, fragment) -> {
                PreferenceCategory category = screen.findPreference(key);
                if (category != null) {
                    for (T item : list) {
                        category.addPreference(callback.onConvert(item));
                    }
                }
            });
        }
        return this;
    }

    public PrefCategoryBuilder setIconSpaceReserved(boolean iconSpaceReserved) {
        this.iconSpaceReserved = iconSpaceReserved;
        return this;
    }

    @NonNull
    @Override
    public PreferenceCategory build() {
        PreferenceCategory preferenceCategory = PrefFactory.getInstance().createPreferenceCategory(context);
        preferenceCategory.setKey(key);
        preferenceCategory.setTitle(title);
        preferenceCategory.setIconSpaceReserved(iconSpaceReserved);
        return preferenceCategory;
    }
}
