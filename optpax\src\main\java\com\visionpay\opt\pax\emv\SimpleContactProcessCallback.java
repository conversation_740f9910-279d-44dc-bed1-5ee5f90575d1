package com.visionpay.opt.pax.emv;
import android.app.Activity;
import android.os.ConditionVariable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.pax.bizentity.entity.Issuer;
import com.pax.bizentity.entity.SearchMode;
import com.pax.bizlib.trans.Device;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.ICardReaderHelper;
import com.pax.dal.entity.EReaderType;
import com.pax.emvbase.constant.EmvConstant;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.contact.CandidateAID;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.dialog.RemoveCardDialog;
import com.visionpay.opt.pax.emv.contactless.ContactlessService;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EIssuer;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.utils.DialogUtils;
import com.visionpay.opt.pax.utils.EmvAidUtils;

import java.util.List;

@WorkerThread
public class SimpleContactProcessCallback implements SimpleEmvService.ISimpleContactCallback, IEmvProcessCallback {
    private static final String TAG = "SimpleContactProcessCallback";

    private final ISimpleEmvService emv;
    private final ConditionVariable cv;
    private IEmvService.ConfirmCallback confirmCallback;
    private IEmvService.ErrorCallback errorCallback;
    private ContactlessService.RemoveCardCallback removeCardCallback;
    private List<CvmType> cvmTypeList;
    private int detectResult;

    public SimpleContactProcessCallback(@NonNull ISimpleEmvService emv, @NonNull ConditionVariable cv, int detectResult) {
        this.emv = emv;
        this.cv = cv;
        this.detectResult = detectResult;
    }
    @Override
    public void onReadCardOk() {
        // do nothing
    }
    @Override
    public void onRemoveCard() {
        try {
            DialogUtils.dismiss();
            final boolean[] isShown = { false };
            RemoveCardDialog dialog = new RemoveCardDialog();

            Device.RemoveCardListener listener = result -> {
                if (!isShown[0]) {
                    Activity activity = ActivityStack.getInstance().top();
                    if (activity instanceof FragmentActivity) {
                        FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
                        dialog.show(manager, "Remove Card");
                        isShown[0] = true;
                    }
                    if (removeCardCallback != null) {
                        removeCardCallback.onRemove();
                    }
                }
            };
            if(detectResult == SearchMode.INTERNAL_MIFARE)
                Device.removeMifareCard(listener);
            else
                Device.removeCard(listener);
            dialog.dismissAllowingStateLoss();
        } catch (Exception e) {
            LogUtils.e(TAG, "Remove card error", e);
        }
    }
    @Override
    public boolean needSeePhone() {
        return false;
    }

    @Override
    public int confirmCard() {
        LogUtils.d(TAG, "============ Confirm Card Start ============");

        String aid = ConvertUtils.bcd2Str(emv.getTlv(0x84));
        int seqNumber = ConvertUtils.bcd2Int(emv.getTlv(0x5F34));
        String transType = ConvertUtils.bcd2Str(emv.getTlv(TagsTable.TRANS_TYPE));
        int type = 0;
        try {
            type = Integer.parseInt(transType);
        } catch (Exception e) {
            LogUtils.e(TAG, "Parse error", e);
        }
        List<EmvAidInfo> infoList = EmvAidUtils.getContactlessAidInfo(emv.getKernelType(), aid, type);
        String path = EmvAidUtils.getEditContactlessAidPath(emv.getKernelType(), aid);

        Issuer matchedIssuer = emv.getMatchedIssuerByPan();

        if (confirmCallback != null) {
            EIssuer info = EIssuer.parse(emv.getKernelType());
            if (info != null) {
                confirmCallback.onConfirm(emv.getPan(),
                        info.getIssuerIconRes(),
                        info.getDisplayName(),
                        getCardHolderName(),
                        infoList, path, aid, seqNumber + "",
                        emv.getExpireDate(),
                        new String(emv.getTlv(TagsTable.APP_LABEL)),
                        ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            } else {
                String issuerName = "Unknown";
                if (matchedIssuer != null) {
                    issuerName = matchedIssuer.getName();
                }
                confirmCallback.onConfirm(emv.getPan(),
                        R.drawable.ic_issuer_other, issuerName,
                        getCardHolderName(), infoList, path, aid, seqNumber + "",
                        emv.getExpireDate(),
                        new String(emv.getTlv(TagsTable.APP_LABEL)),
                        ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            }
        }
        LogUtils.d(TAG, "============ Confirm Card End ============");
        return 0;
    }
    @Override
    public int onWaitAppSelect(boolean isFirstSelect, List<CandidateAID> candList) {
        LogUtils.d(TAG, "============ Application Select Start ============");
        final int[] appSelectRet = {0};
        return appSelectRet[0];
    }

    @Override
    public int onCardHolderPwd(boolean isOnlinePin, boolean supportPINByPass, int leftTimes,
            byte[] pinData) {
        LogUtils.d(TAG, "============ PIN Input Start ============");
        return EmvConstant.ContactCallbackStatus.CONTACT_OK;
    }

    @Override
    public int showConfirmCard() {
        LogUtils.d(TAG, "============ Confirm Card Start ============");

        String aid = ConvertUtils.bcd2Str(emv.getTlv(0x84));
        int seqNumber = ConvertUtils.bcd2Int(emv.getTlv(0x5F34));
        List<EmvAidInfo> infoList = EmvAidUtils.getContactAidInfo(aid);
        String path = EmvAidUtils.getEditContactAidPath(aid);

        Issuer matchedIssuer = emv.getMatchedIssuerByPan();

        if (confirmCallback != null) {
            if (matchedIssuer != null) {
                String name = matchedIssuer.getName();
                EIssuer info = EIssuer.parse(name);
                if (info != null) {
                    confirmCallback.onConfirm(emv.getPan(),
                            info.getIssuerIconRes(),
                            info.getDisplayName(),
                            getCardHolderName(), infoList, path, aid, seqNumber + "",
                            emv.getExpireDate(),
                            new String(emv.getTlv(TagsTable.APP_LABEL)),
                            ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                } else {
                    confirmCallback.onConfirm(emv.getPan(),
                            R.drawable.ic_issuer_other, name,
                            getCardHolderName(), infoList, path, aid, seqNumber + "",
                            emv.getExpireDate(),
                            new String(emv.getTlv(TagsTable.APP_LABEL)),
                            ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                }
            } else {
                confirmCallback.onConfirm(emv.getPan(), R.drawable.ic_issuer_other,
                        "Unknown",
                        getCardHolderName(), infoList, path, aid, seqNumber + "",
                        emv.getExpireDate(),
                        new String(emv.getTlv(TagsTable.APP_LABEL)),
                        ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            }
        }
        LogUtils.d(TAG, "============ Confirm Card End ============");
        return 0;
    }

    @NonNull
    private String getCardHolderName() {
        String cardHolderName = emv.getCardholderName();
        if (cardHolderName == null || cardHolderName.isEmpty() || cardHolderName.trim().isEmpty()) {
            return "";
        }
        return new String(ConvertUtils.strToBcdPaddingLeft(cardHolderName));
    }

    @Override
    public int showEnterTip() {
        // do nothing
        return 0;
    }

    @Override
    public OnlineResultWrapper startOnlineProcess(long amount, String terminalId, int detectResult, String reference, ISendingOnlineCallback callback) {
        return null;
    }
    @Override
    public void onDetect2ndTap() {
        cv.close();

        Activity activity = ActivityStack.getInstance().top();
        if (activity instanceof FragmentActivity) {
            DialogUtils.createProcessingDialog((FragmentActivity) activity,
                    "Please Tap Card",
                    "Contactless kernel need second tap card");
        }

        App.getApp().runInBackground(() -> {
            try {
                ICardReaderHelper helper = App.getDal().getCardReaderHelper();
                helper.polling(EReaderType.PICC, 60 * 1000);
                helper.stopPolling();
            } catch (Exception e) {
                LogUtils.d(TAG, "Detect Card Failed", e);
            } finally {
                cv.open();
            }
        });

        cv.block(60 * 1000);
        DialogUtils.dismiss();
        LogUtils.d(TAG, "Detect 2nd Tap Done.");
    }
    @NonNull
    @Override
    public SimpleContactProcessCallback setConfirmCallback(@Nullable IEmvService.ConfirmCallback callback) {
        this.confirmCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public SimpleContactProcessCallback setErrorCallback(@Nullable IEmvService.ErrorCallback callback) {
        this.errorCallback = callback;
        return this;
    }

    @NonNull
    public SimpleContactProcessCallback setCvmTypeList(List<CvmType> cvmTypeList) {
        this.cvmTypeList = cvmTypeList;
        return this;
    }

    public SimpleContactProcessCallback setRemoveCardCallback(ContactlessService.RemoveCardCallback removeCardCallback) {
        this.removeCardCallback = removeCardCallback;
        return this;
    }
}
