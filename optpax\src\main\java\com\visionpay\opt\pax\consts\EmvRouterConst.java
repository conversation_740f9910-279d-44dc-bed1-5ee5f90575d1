/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.consts;

/**
 * Router Path Constants
 */
public class EmvRouterConst {
    public static final String SEARCH_CARD = "/Emv_Demo_Search_Card";
    public static final String READ_CARD = "/Emv_Demo_Read_Card";
    public static final String MAG_READ_CARD = "/Emv_Demo_Mag_Read_Card";
    public static final String RECEIPT_READ_CARD = "/Emv_Demo_Receipt_Read_Card";
    public static final String SUCCESS_CARD = "/Emv_Demo_Success_Card";
    public static final String SUCCESS_INTERNAL_CARD = "/Emv_Demo_Success_Internel_Card";
    public static final String ERROR_CARD = "/Emv_Demo_Error_Card";

    public static final String CONFIG_MAIN = "/Emv_Demo_Config_Main";
    public static final String CONFIG_COMMON = "/Emv_Demo_Config_Common";
    public static final String CONFIG_PARAM = "/Emv_Demo_Config_Param";
    public static final String CONFIG_PARAM_CONTACT = CONFIG_PARAM + "/Contact";
    public static final String CONFIG_PARAM_CONTACT_AID = CONFIG_PARAM_CONTACT + "/AID";
    public static final String CONFIG_PARAM_CLSS_AMEX = CONFIG_PARAM + "/Contactless/AMEX";
    public static final String CONFIG_PARAM_CLSS_AMEX_AID = CONFIG_PARAM_CLSS_AMEX + "/AID";
    public static final String CONFIG_PARAM_CLSS_AMEX_DRL = CONFIG_PARAM_CLSS_AMEX + "/DRL";
    public static final String CONFIG_PARAM_CLSS_DPAS = CONFIG_PARAM + "/Contactless/DPAS";
    public static final String CONFIG_PARAM_CLSS_DPAS_AID = CONFIG_PARAM_CLSS_DPAS + "/AID";
    public static final String CONFIG_PARAM_CLSS_EFT = CONFIG_PARAM + "/Contactless/EFT";
    public static final String CONFIG_PARAM_CLSS_EFT_AID = CONFIG_PARAM_CLSS_EFT + "/AID";
    public static final String CONFIG_PARAM_CLSS_JCB = CONFIG_PARAM + "/Contactless/JCB";
    public static final String CONFIG_PARAM_CLSS_JCB_AID = CONFIG_PARAM_CLSS_JCB + "/AID";
    public static final String CONFIG_PARAM_CLSS_MC = CONFIG_PARAM + "/Contactless/MC";
    public static final String CONFIG_PARAM_CLSS_MC_AID = CONFIG_PARAM_CLSS_MC + "/AID";
    public static final String CONFIG_PARAM_CLSS_MIR = CONFIG_PARAM + "/Contactless/MIR";
    public static final String CONFIG_PARAM_CLSS_MIR_AID = CONFIG_PARAM_CLSS_MIR + "/AID";
    public static final String CONFIG_PARAM_CLSS_PAYWAVE = CONFIG_PARAM + "/Contactless/PayWave";
    public static final String CONFIG_PARAM_CLSS_PAYWAVE_AID = CONFIG_PARAM_CLSS_PAYWAVE + "/AID";
    public static final String CONFIG_PARAM_CLSS_PAYWAVE_PROGRAM_ID = CONFIG_PARAM_CLSS_PAYWAVE + "/ProgramID";
    public static final String CONFIG_PARAM_CLSS_PBOC = CONFIG_PARAM + "/Contactless/PBOC";
    public static final String CONFIG_PARAM_CLSS_PBOC_AID = CONFIG_PARAM_CLSS_PBOC + "/AID";
    public static final String CONFIG_PARAM_CLSS_PURE = CONFIG_PARAM + "/Contactless/PURE";
    public static final String CONFIG_PARAM_CLSS_PURE_AID = CONFIG_PARAM_CLSS_PURE + "/AID";
    public static final String CONFIG_PARAM_CLSS_RUPAY = CONFIG_PARAM + "/Contactless/RUPAY";
    public static final String CONFIG_PARAM_CLSS_RUPAY_AID = CONFIG_PARAM_CLSS_RUPAY + "/AID";
    public static final String CONFIG_ABOUT = "/Emv_Demo_Config_About";

    private EmvRouterConst() {
        // do nothing
    }
}
