package com.visionpay.opt.pax.service;

import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.os.Messenger;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pax.commonlib.init.IModuleInit;
import com.pax.commonlib.utils.LazyInit;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.common.DefaultUriRequest;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.entity.IM30Transaction;
import com.visionpay.opt.pax.entity.POSTransaction;
import com.visionpay.opt.pax.entity.ProcessType;
import com.visionpay.opt.pax.message.BroadcastMesseger;
import com.visionpay.opt.pax.thread.OnlineThread;
import com.visionpay.opt.pax.utils.ParamUtils;
import com.visionpay.opt.pax.utils.ProcessTypeDeserializer;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class MessageService extends Service
{
    private Messenger messenger; //receives remote invocations
    ExecutorService executorService = Executors.newFixedThreadPool(4);

    //private final ConditionVariable cv = new ConditionVariable();

    private final LazyInit<IConfigParamService> configParamService = LazyInit.by(() ->
            Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG));

    @Override
    public IBinder onBind(Intent intent)
    {
        if(this.messenger == null)
        {
            synchronized(MessageService.class)
            {
                if(this.messenger == null)
                {
                    this.messenger = new Messenger(new IncomingHandler(this));
                    initializeApp();
                }
            }
        }
        //Return the proper IBinder instance
        return this.messenger.getBinder();
    }

    private void initializeApp(){
        //cv.close();
        //App.getApp().runInBackground(() -> {
            IModuleInit config = Router.getService(IModuleInit.class, ConfigServiceConstant.INIT_CONFIG);
            config.loadConfig(ParamUtils.loadConfigParam());

            if (configParamService.get().getBoolean(ConfigKeyConstant.IS_FIRST_RUN, true)) {
                config.setCallback(() ->{
                    configParamService.get().putBoolean(ConfigKeyConstant.IS_FIRST_RUN, false);
                });
                config.init();
            }
            ParamUtils.emvModuleInit();
            ParamUtils.emvParamInit();
            //cv.open();
        //});
        //cv.block();
    }

    private class IncomingHandler extends Handler
    {
        private final Context context;

        public IncomingHandler(Context context){
            this.context = context;
        }

        @Override
        public void handleMessage(Message msg)
        {
            Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                    .registerTypeAdapter(ProcessType.class, new ProcessTypeDeserializer()).create();

            POSTransaction POSTransaction = null;
            try {
                String message = BroadcastMesseger.getInstance().setMessage(msg);

                if(message == null || message.equals(""))
                    return;

                //int what = msg.what;
                if(!BroadcastMesseger.getInstance().receive(message)) {

                    POSTransaction = gson.fromJson(message, POSTransaction.class);
                    if (POSTransaction.checkValues()) {
                        if (POSTransaction.getProcessType() == ProcessType.Payment) {
                            Router.startUri(new DefaultUriRequest(context, EmvRouterConst.READ_CARD)
                                    .putExtra(BundleFieldConst.POSTRANSACTION, POSTransaction));
                        } else if (POSTransaction.getProcessType() == ProcessType.Magnetic) {
                            Router.startUri(new DefaultUriRequest(context, EmvRouterConst.MAG_READ_CARD)
                                    .putExtra(BundleFieldConst.POSTRANSACTION, POSTransaction));
                        } else if (POSTransaction.getProcessType() == ProcessType.Capture || POSTransaction.getProcessType() == ProcessType.Reversal) {
                            executorService.execute(new OnlineThread(POSTransaction));
                        } else if (POSTransaction.getProcessType() == ProcessType.Receipt) {
                            Router.startUri(new DefaultUriRequest(context, EmvRouterConst.RECEIPT_READ_CARD)
                                    .putExtra(BundleFieldConst.POSTRANSACTION, POSTransaction));
                        } else {
                            IM30Transaction im30t = new IM30Transaction(POSTransaction, "Incorrect Process Type.");
                            BroadcastMesseger.getInstance().send(gson.toJson(im30t));
                        }
                    } else {
                        IM30Transaction im30t = new IM30Transaction(POSTransaction, "Incorrect values.");
                        BroadcastMesseger.getInstance().send(gson.toJson(im30t));
                    }
                }
            }catch (Exception ex){
                IM30Transaction im30t = new IM30Transaction(POSTransaction, ex.getMessage());
                BroadcastMesseger.getInstance().send(gson.toJson(im30t));
            }
        }
    }
}
