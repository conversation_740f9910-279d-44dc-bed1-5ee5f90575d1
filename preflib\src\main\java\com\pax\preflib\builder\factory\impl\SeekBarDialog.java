/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/26                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.factory.impl;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.SeekBar;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatSeekBar;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.preference.PreferenceDialogFragmentCompat;
import com.pax.preflib.R;

/**
 * DialogFragment corresponding to SeekBarDialogPreference
 */
public class SeekBarDialog extends PreferenceDialogFragmentCompat implements SeekBar.OnSeekBarChangeListener {
    private static final String SAVE_STATE_MIN = "SeekBarDialog.min";
    private static final String SAVE_STATE_MAX = "SeekBarDialog.max";
    private static final String SAVE_STATE_INCREMENT = "SeekBarDialog.increment";
    private static final String SAVE_STATE_FORMAT = "SeekBarDialog.format";
    private static final String SAVE_STATE_VALUE = "SeekBarDialog.value";

    private AppCompatSeekBar seekBar;
    private AppCompatTextView textView;

    private float min = 0;
    private float max = 100;
    private float increment = 1;
    private String format = null;
    private float value = 0;

    private SeekBar.OnSeekBarChangeListener listener;

    public static SeekBarDialog newInstance(String key) {
        SeekBarDialog dialog = new SeekBarDialog();
        Bundle bundle = new Bundle(1);
        bundle.putString(ARG_KEY, key);
        dialog.setArguments(bundle);
        return dialog;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (savedInstanceState == null) {
            value = getSeekBarPreference().getValue();
        } else {
            min = savedInstanceState.getFloat(SAVE_STATE_MIN, 0);
            max = savedInstanceState.getFloat(SAVE_STATE_MAX, 100);
            increment = savedInstanceState.getFloat(SAVE_STATE_INCREMENT, 1);
            format = savedInstanceState.getString(SAVE_STATE_FORMAT, null);
            value = savedInstanceState.getFloat(SAVE_STATE_VALUE, 0);
        }
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putFloat(SAVE_STATE_MIN, min);
        outState.putFloat(SAVE_STATE_MAX, max);
        outState.putFloat(SAVE_STATE_INCREMENT, increment);
        outState.putString(SAVE_STATE_FORMAT, format);
        outState.putFloat(SAVE_STATE_VALUE, value);
    }

    @Override
    protected View onCreateDialogView(Context context) {
        return LayoutInflater.from(context).inflate(R.layout.pref_dialog_custom_seekbar, null);
    }

    @SuppressLint("SetTextI18n")
    @Override
    protected void onBindDialogView(View view) {
        super.onBindDialogView(view);

        seekBar = view.findViewById(R.id.seekbar);
        textView = view.findViewById(R.id.seekbar_value);

        seekBar.setMax((int) ((max - min) / increment));
        seekBar.setProgress((int) ((value - min) / increment));
        seekBar.setOnSeekBarChangeListener(this);

        if (format != null) {
            // Use default locale string format
            textView.setText(String.format(format, value));
        } else {
            textView.setText(Float.toString(value));
        }
    }

    private SeekBarDialogPreference getSeekBarPreference() {
        return (SeekBarDialogPreference) getPreference();
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        if (listener != null) {
            listener.onProgressChanged(seekBar, progress, fromUser);
        }

        if (!fromUser) {
            return;
        }

        if (textView != null) {
            if (format != null) {
                // Use default locale string format
                textView.setText(String.format(format, min + progress * increment));
            } else {
                textView.setText(Float.toString(min + progress * increment));
            }
        }
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        if (listener != null) {
            listener.onStartTrackingTouch(seekBar);
        }
    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        if (listener != null) {
            listener.onStopTrackingTouch(seekBar);
        }

        // Save current value 保存当前值
        if (seekBar != null) {
            value = min + (seekBar.getProgress() * increment);
        }
    }

    @Override
    public void onDialogClosed(boolean positiveResult) {
        if (positiveResult) {
            final SeekBarDialogPreference preference = getSeekBarPreference();
            if (preference != null && preference.callChangeListener(value)) {
                // This value can be saved 这个值可以保存
                preference.setValue(value);
            }
        }
    }

    /**
     * Set SeekBar minimum value.
     *
     * @param min SeekBar minimum value
     * @return This dialog
     */
    public SeekBarDialog setMin(float min) {
        this.min = min;
        return this;
    }

    /**
     * Set SeekBar maximum value.
     *
     * @param max SeekBar maximum value
     * @return This dialog
     */
    public SeekBarDialog setMax(float max) {
        this.max = max;
        return this;
    }

    /**
     * Set SeekBar increment.
     *
     * @param increment SeekBar increment
     * @return This dialog
     */
    public SeekBarDialog setIncrement(float increment) {
        this.increment = increment;
        if (seekBar != null) {
            seekBar.setMax((int) ((max - min) / increment));
        }
        return this;
    }

    /**
     * Set String value format.
     *
     * @param format String value format
     * @return This dialog
     */
    public SeekBarDialog setFormat(String format) {
        this.format = format;
        return this;
    }

    /**
     * Set {@code SeekBar.OnSeekBarChangeListener}.
     *
     * @param listener {@code SeekBar.OnSeekBarChangeListener}
     * @return This dialog
     */
    public SeekBarDialog setListener(SeekBar.OnSeekBarChangeListener listener) {
        this.listener = listener;
        return this;
    }
}
