/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.pin;

import android.app.Activity;
import android.os.ConditionVariable;
import android.widget.TextView;

import androidx.annotation.WorkerThread;
import androidx.fragment.app.FragmentActivity;

import com.pax.bizlib.ped.InjectKeyUtil;
import com.pax.bizlib.ped.PedHelper;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.LogUtils;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.pax.dal.IPed;
import com.pax.dal.exceptions.PedDevException;
import com.pax.emvbase.constant.EmvConstant;
import com.pax.emvservice.export.IEmvBase;
import com.pax.emvservice.export.exceptions.PinException;
import com.pax.poslib.gl.convert.ConvertHelper;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.dialog.MyEnterPinDialog;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.utils.DialogUtils;
import com.visionpay.opt.pax.utils.OrientationUtils;

/**
 * Online PIN Task
 */
@WorkerThread
public class OnlinePinTask extends BasePinTask {
    private static final String TAG = "OnlinePinTask";

    private final ConditionVariable cv = new ConditionVariable();
    private final PinImpl callback = new PinImpl();
    private final IEmvService.ErrorCallback errorCallback;
    private TextView pinEditText;
    private MyEnterPinDialog dialog;

    public OnlinePinTask(IEmvService.ErrorCallback errorCallback) {
        this.errorCallback = errorCallback;
    }

    @Override
    public int start(IEmvBase emv, boolean isSupportPinByPass, boolean forceSoftEncrypt) {
        String panBlock = emv.getPanBlock();
        cv.close();
        DialogUtils.dismiss();

        // Create Dialog
        App.getApp().runOnUiThread(() -> {
            Activity activity = ActivityStack.getInstance().top();
            if (activity instanceof FragmentActivity) {
                /*DialogUtils.createEnterPinDialog((FragmentActivity) activity,
                        "Enter Online PIN", editText -> {
                            pinEditText = editText;
                        }, null);
                cv.open();*/
                dialog = DialogUtils.createCustomEnterPinDialog((FragmentActivity) activity,
                        "Enter Online PIN", editText -> {
                            pinEditText = (TextView) editText;
                        }, null);
                cv.open();
            }
        });

        // Set PED
        IPed ped = PedHelper.getPed();
        pinService.setInputPinListener(new PinServiceInputListener(callback));

        byte[] result = null;
        byte[] pin = null;

        try {
            ped.setIntervalTime(1, 1);
            ped.setKeyboardRandom(false);
            InjectKeyUtil.injectMKSK(ConfigKeyConstant.CommType.DEMO);
            cv.block();
            // Get PIN Block
            IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
            String environment = configParamService.getString(ConfigKeyConstant.ENCRYPTION_ENVIRONMENT, "Production");
            result = pinService.getEncryptedPinData(panBlock, isSupportPinByPass,
                    OrientationUtils.isLandScape(), environment, forceSoftEncrypt);
        } catch (PinException e) {
            LogUtils.e(TAG, "PinException", e);
            pinService.setInputPinListener(null);
            return handleException(e, callback);
        } catch (PedDevException e) {
            LogUtils.e(TAG, "PedDevException", e);
            pinService.setInputPinListener(null);
            return handleException(new PinException(String.valueOf(e.getErrCode()),e.getErrMsg()),
                    callback);
        }

        // Set Data
        if (result != null) {
            String data = ConvertHelper.getConvert().bcdToStr(result);

            if (data != null && !data.isEmpty()) {
                clear();
                emv.setPin(result);
                pinService.setInputPinListener(null);
                return EmvConstant.ContactCallbackStatus.CONTACT_OK;
            }
        }
        clear();
        pinService.setInputPinListener(null);
        return EmvConstant.ContactCallbackStatus.NO_PASSWORD;
    }

    private void clear() {
        DialogUtils.dismiss();
        if(dialog != null)
            dialog.dismiss();
        pinEditText = null;
    }

    private class PinImpl implements PinCallback {
        @Override
        public void onInput(int inputLen) {
            if (pinEditText != null) {
                StringBuilder builder = new StringBuilder();
                if (inputLen > 0) {
                    for (int i = 0; i < inputLen; i++) {
                        builder.append("●");
                    }
                }
                pinEditText.setText(builder.toString());
            }
        }

        @Override
        public void onFinish() {
            clear();
        }

        @Override
        public void onCancel() {
            clear();
            if (errorCallback != null) {
                errorCallback.onError("PIN Input Cancel", "User cancel PIN Input.");
            }
        }

        @Override
        public void onNoPinPad() {
            clear();
            if (errorCallback != null) {
                errorCallback.onError("No PIN Pad", "Cannot find PIN Pad. Please check the "
                        + "connection between POS and PIN Pad");
            }
        }

        @Override
        public void onTimeout() {
            clear();
            if (errorCallback != null) {
                errorCallback.onError("Timeout", "User input PIN timeout.");
            }
        }

        @Override
        public void onError(String reason) {
            clear();
            if (errorCallback != null) {
                errorCallback.onError("PED Unknown Error", "PED throw an exception: " + reason);
            }
        }
    }
}
