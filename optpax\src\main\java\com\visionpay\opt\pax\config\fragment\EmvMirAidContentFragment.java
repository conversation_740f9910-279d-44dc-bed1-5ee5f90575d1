/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/13                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;
import com.google.android.material.snackbar.Snackbar;
import com.visionpay.opt.pax.config.datastore.MirAidDataStore;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.bizentity.db.helper.MirAidDbHelper;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.AmountEditTextPrefBuilder;
import com.pax.preflib.builder.StringEditTextPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

/**
 * MIR contactless AID content page
 *
 * Settings -> Param -> MIR Kernel Param -> AID
 */
@RouterService(interfaces = EmvParamBaseFragment.class, key = EmvRouterConst.CONFIG_PARAM_CLSS_MIR_AID)
public class EmvMirAidContentFragment extends EmvParamBaseFragment {
    private String aid;

    @Override
    public void setParam(String param) {
        aid = param;
    }

    @NonNull
    @Override
    public String getFragmentTitle() {
        return aid;
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new MirAidDataStore(MirAidDbHelper.getInstance().findAID(aid))
                    .setRefreshCachedParamCallback(() -> Snackbar
                            .make(getListView(), "Need Refresh Cached EMV Param", Snackbar.LENGTH_LONG)
                            .setAction("REFRESH", v -> refreshCachedParam())
                            .show()));
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // APP Name
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.APP_NAME, R.string.config_param_app_name)
                    .buildAndApply(source -> source.setSelectable(false)));

            // Trans limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.TRANS_LIMIT, R.string.config_param_trans_limit)
                    .build());

            // Floor limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.FLOOR_LIMIT, R.string.config_param_floor_limit)
                    .build());

            // CVM limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.CVM_LIMIT, R.string.config_param_cvm_limit)
                    .build());

            // TAC Default
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.TAC_DEFAULT, R.string.config_param_tac_default)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 10))
                    .build());

            // TAC Denial
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.TAC_DENIAL, R.string.config_param_tac_denial)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 10))
                    .build());

            // TAC Online
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.TAC_ONLINE, R.string.config_param_tac_online)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 10))
                    .build());

            // Terminal TPM Capability
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, MirAidDataStore.TERM_TPM_CAP, R.string.config_param_term_tpm_cap)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 4))
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
