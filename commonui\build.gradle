apply plugin: 'com.android.library'
android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        buildConfigField('String', 'DATABASE_PWD', DATABASE_PWD)
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'

        ndk {
            abiFilters 'armeabi'
        }
    }
    resourcePrefix "commonui"

    buildTypes {
        release {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        stagingRelease {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        autoTest{
            buildConfigField('boolean','RELEASE','false')
        }
        debug{
            buildConfigField('boolean','RELEASE','false')
        }
    }

}
repositories {
    flatDir {
        dirs '../poslib/libs', 'libs'
    }
    mavenCentral()
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "com.google.android.material:material:$rootProject.material"
    compileOnly 'org.jetbrains:annotations:+@jar'
    api "androidx.appcompat:appcompat:$rootProject.androidAppcompat"
    api(name: 'expandablerecyclerview-release', ext: 'aar')
    testImplementation "junit:junit:$rootProject.junit"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.androidxExtJunit"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    api project(':poslib')
    api project(':configservice:export')
    annotationProcessor "io.github.meituan-dianping:compiler:$rootProject.routerCompiler"
}