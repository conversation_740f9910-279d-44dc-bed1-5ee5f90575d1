<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/commonui_space_horizontal"
    android:layout_marginEnd="@dimen/commonui_space_horizontal"
    android:background="@drawable/commonui_toast_bg"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/commonui_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="3"
        android:textColor="@color/commonui_primary_text_dark"
        android:textSize="@dimen/commonui_font_size_value" />

    <TextView
        android:id="@+id/percentage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/commonui_space_horizontal_small"
        android:gravity="center"
        android:textColor="@color/commonui_primary_text_dark"
        android:textSize="@dimen/commonui_font_size_hint"
        android:visibility="gone" />
</RelativeLayout>