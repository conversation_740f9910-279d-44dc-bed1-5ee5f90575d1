/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.list;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.entity.EmvAidInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/19
 */
public class AidInfoAdapter extends RecyclerView.Adapter<AidInfoViewHolder> {
    private List<EmvAidInfo> infoList = new ArrayList<>();

    public AidInfoAdapter setInfoList(List<EmvAidInfo> infoList) {
        this.infoList = infoList;
        return this;
    }

    @NonNull
    @Override
    public AidInfoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_emv_aid_content, parent, false);
        return new AidInfoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AidInfoViewHolder holder, int position) {
        EmvAidInfo info = infoList.get(position);
        holder.titleTextView.setText(info.getTitle());
        holder.contentTextView.setText(info.getContent());
    }

    @Override
    public int getItemCount() {
        return infoList.size();
    }
}
