package com.visionpay.opt.pax.emv.online.interchange.messages.responses;

import com.visionpay.opt.pax.emv.online.interchange.messages.Message;

import java.util.Arrays;

public class InitResponse extends Message {

    public String CAID;
    public String Address1;
    public String Address2;
    public String Address3;
    public String Footer;

    public InitResponse(byte[] buffer) {
        super(buffer);
    }

    @Override
    protected byte[] internalGetMessage(){
        return this.mMessage;
    }

    @Override
    protected int internalParse(int i){
        i = super.internalParse(i);
        this.CAID = new String(Arrays.copyOfRange(this.mMessage, i, i + 50)).trim();
	    i += 50;
        this.Address1 = new String(Arrays.copyOfRange(this.mMessage, i, i + 37)).trim();
	    i += 37;
        this.Address2 = new String(Arrays.copyOfRange(this.mMessage, i, i + 37)).trim();
	    i += 37;
        this.Address3 = new String(Arrays.copyOfRange(this.mMessage, i, i + 37)).trim();
	    i += 37;
        this.Footer = new String(Arrays.copyOfRange(this.mMessage, i, i + 37)).trim();
        i += 37;
        return i;
    }

}
