package com.visionpay.opt.pax.emv.online.interchange.enums;

public enum InterchangeTransactionType {
    NONE((byte)0x00),
    INSERT((byte)0x01),
    TAP((byte)0x02),
    SWIPE((byte)0x03);

    public final byte value;

    InterchangeTransactionType(byte i) {
        this.value = i;
    }

    public static InterchangeTransactionType get(int value)
    {
        InterchangeTransactionType[] As = InterchangeTransactionType.values();
        for(int i = 0; i < As.length; i++)
        {
            if(As[i].value == value)
                return As[i];
        }
        return null;
    }
}
