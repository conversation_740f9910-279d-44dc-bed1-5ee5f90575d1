package com.visionpay.opt.pax.utils;

import com.google.gson.Gson;
import com.google.gson.TypeAdapter;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;

public class EnumTypeAdapterFactory implements TypeAdapterFactory {
    @Override
    // Actually it might be easier to just make EnumTypeAdapter non-generic
    // but on the other hand it might be better if used in some other contexts
    // and in some other ways. Thus these suppressions
    @SuppressWarnings({"rawtypes", "unchecked"})
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
        // This if applies to all Enums. Change if not wanted.
        if(Enum.class.isAssignableFrom(type.getRawType())) {
            return new EnumTypeAdapter();
        } else {
            return  null;
        }
    }
}
