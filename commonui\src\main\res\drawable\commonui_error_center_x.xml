<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <rotate
            android:fromDegrees="45"
            android:toDegrees="45"
            android:pivotX="50%"
            android:pivotY="50%">
            <shape android:shape="rectangle">
                <solid android:color="@color/commonui_accent" />
                <corners android:radius="4dp" />
                <size
                    android:height="3dp"
                    android:width="28dp" />
            </shape>
        </rotate>
    </item>

    <item>
        <rotate
            android:fromDegrees="315"
            android:toDegrees="315"
            android:pivotX="50%"
            android:pivotY="50%">
            <shape android:shape="rectangle">
                <solid android:color="@color/commonui_accent" />
                <corners android:radius="4dp" />
                <size
                    android:height="3dp"
                    android:width="28dp" />
            </shape>
        </rotate>
    </item>
</layer-list>