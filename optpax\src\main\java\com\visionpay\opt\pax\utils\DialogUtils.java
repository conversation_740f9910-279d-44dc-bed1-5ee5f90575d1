/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.utils;

import android.app.Dialog;
import android.content.DialogInterface;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import com.pax.commonlib.utils.LogUtils;
import com.visionpay.opt.pax.callback.OnBindEditTextCallback;
import com.visionpay.opt.pax.dialog.BaseDialogFragment;
import com.visionpay.opt.pax.dialog.EnterPinDialog;
import com.visionpay.opt.pax.dialog.MyEnterPinDialog;
import com.visionpay.opt.pax.dialog.ProcessingDialog;

/**
 * Dialog Utils
 */
public class DialogUtils {
    private static final String TAG = "DialogUtils";

    private static BaseDialogFragment<?> currentDialog;

    /**
     * Display Processing Dialog.
     *
     *  @param activity Fragment activity
     * @param title Process title
     * @param info Process info
     */
    public static synchronized void createProcessingDialog(
            @NonNull FragmentActivity activity,
            String title,
            String info) {
        try {
            dismiss();
            ProcessingDialog dialog = new ProcessingDialog()
                    .setTitle(title)
                    .setInfo(info);
            currentDialog = dialog;
            dialog.setOnDismissListener(dialogInterface -> {
                if (dialog == currentDialog) {
                    clearDialog();
                }
            }).show(activity.getSupportFragmentManager(), "Process Dialog");
        } catch (Exception e) {
            LogUtils.e(TAG, "Create processing dialog error", e);
        }
    }

    /**
     * Display Enter PIN Dialog.
     *
     * @param activity Fragment activity
     * @param title Dialog title
     * @param callback Bind EditText callback
     */
    public static synchronized void createEnterPinDialog(
            @NonNull FragmentActivity activity,
            String title,
            @Nullable OnBindEditTextCallback callback) {
        createEnterPinDialog(activity, title, callback, null);
    }

    /**
     * Display Enter PIN Dialog.
     *
     * @param activity Fragment activity
     * @param title Dialog title
     * @param callback Bind EditText callback
     * @param listener Dialog dismiss listener
     */
    public static synchronized void createEnterPinDialog(
            @NonNull FragmentActivity activity,
            String title,
            @Nullable OnBindEditTextCallback callback,
            @Nullable DialogInterface.OnDismissListener listener) {
        try {
            dismiss();
            EnterPinDialog dialog = new EnterPinDialog()
                    .setTitle(title)
                    .setOnBindEditTextCallback(callback);
            currentDialog = dialog;
            dialog.setOnDismissListener(dialogInterface -> {
                if (listener != null) {
                    listener.onDismiss(dialogInterface);
                }
                if (dialog == currentDialog) {
                    clearDialog();
                }
            }).show(activity.getSupportFragmentManager(), "Enter Pin Dialog");
        } catch (Exception e) {
            LogUtils.e(TAG, "Create enter PIN dialog error", e);
        }
    }

    /**
     * Display Custom Enter PIN Dialog.
     *
     * @param activity Fragment activity
     * @param title Dialog title
     */
    public static synchronized MyEnterPinDialog createCustomEnterPinDialog(
            @NonNull FragmentActivity activity,
            String title,
            @Nullable OnBindEditTextCallback callback,
            @Nullable DialogInterface.OnDismissListener listener) {
        try {
            dismiss();
            MyEnterPinDialog dialog = new MyEnterPinDialog(activity)
                    .setTitle(title)
                    .setOnBindEditTextCallback(callback);;
            //currentDialog = dialog;
            dialog.setOnDismissListener(dialogInterface -> {
                        if (listener != null) {
                            listener.onDismiss(dialogInterface);
                        }
                        //if (dialog == currentDialog) {
                        //    clearDialog();
                        //}
                    });
            dialog.show();
            return dialog;
        } catch (Exception e) {
            LogUtils.e(TAG, "Create enter PIN dialog error", e);
        }
        return null;
    }

    public static synchronized void dismiss() {
        if (currentDialog != null) {
            try {
                currentDialog.dismissAllowingStateLoss();
            } catch (IllegalStateException e) {
                LogUtils.e(TAG, "Close dialog failed", e);
                if (currentDialog != null) {
                    try {
                        Dialog dialog = currentDialog.getDialog();
                        if (dialog != null) {
                            dialog.dismiss();
                        }
                    } catch (Exception exception) {
                        LogUtils.e(TAG, "Force close dialog failed", exception);
                    }
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "Close dialog failed", e);
            } finally {
                clearDialog();
            }
        } else {
            LogUtils.d(TAG, "Current dialog is null");
        }
    }

    private static synchronized void clearDialog() {
        LogUtils.d(TAG, "Clear Dialog");
        currentDialog = null;
    }
}
