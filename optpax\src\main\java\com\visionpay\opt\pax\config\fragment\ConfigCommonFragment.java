/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;

import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.config.datastore.PrefDataStore;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.RestartUtils;
import com.pax.configservice.export.ConfigKeyConstant;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.ListPrefBuilder;
import com.pax.preflib.builder.StringEditTextPrefBuilder;
import com.pax.preflib.builder.SwitchPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

import java.util.ArrayList;
import java.util.List;

/**
 * Common setting page
 *
 * Settings -> Common
 */
@RouterService(interfaces = EmvConfigBaseFragment.class, key = EmvRouterConst.CONFIG_COMMON)
public class ConfigCommonFragment extends EmvConfigBaseFragment {
    @NonNull
    @Override
    public String getFragmentTitle() {
        return "Common";
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new PrefDataStore());
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // Merchant Name
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, ConfigKeyConstant.EDC_MERCHANT_NAME_EN, R.string.config_merchant_name)
                    .addPrefChangedCallback((preference, newValue) -> newValue != null && !newValue.isEmpty())
                    .build());

            // Merchant Address
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, ConfigKeyConstant.EDC_MERCHANT_ADDRESS, R.string.config_merchant_address)
                    .addPrefChangedCallback((preference, newValue) -> newValue != null && !newValue.isEmpty())
                    .build());

            // Currency
            List<String> currencyList = new ArrayList<>();
            CurrencyConverter.getSupportedLocaleList(currencyList);
            screen.addPreference(ListPrefBuilder
                    .newInstance(context, ConfigKeyConstant.EDC_CURRENCY_LIST, R.string.config_currency)
                    .addEntries(currencyList, item -> item)
                    .addPrefChangedCallback((preference, newCurrency) -> {
                        if (newCurrency == null) {
                            return false;
                        }
                        LogUtils.d(TAG, "Currency: " + newCurrency);
                        CurrencyConverter.setDefCurrency(newCurrency);
                        App.getApp().runOnUiThreadDelay(RestartUtils::restart, 100);
                        return true;
                    })
                    .build());

            // Solve Contactless Conflict
            screen.addPreference(SwitchPrefBuilder
                    .newInstance(context, ConfigKeyConstant.EDC_SOLVE_IMAG_CLS_CONFLICT,
                            R.string.config_solve_clss_conflict)
                    .build());

            // Physical Contactless Lights
            screen.addPreference(SwitchPrefBuilder
                    .newInstance(context, ConfigKeyConstant.EDC_PHYSICAL_CLS_LIGHT, R.string.config_support_clss_lights)
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
