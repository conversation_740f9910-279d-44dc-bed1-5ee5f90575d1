package com.pax.preflib.builder.pref;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.SeekBar;
import androidx.annotation.NonNull;
import androidx.preference.DialogPreference;

/**
 * Custom SeekBar dialog Preference.
 * <br>
 * SeekBarPreference is provided in Jetpack Preference, but there are many limitations. For
 * example, the maximum and minimum values can only be set as integers, and the displayed value
 * size cannot be customized. The most important problem is that SeekBarPreference cannot be
 * displayed as a dialog.
 * <br>
 * SeekBarDialogPreference is implemented based on DialogPreference, adds many new functions, and
 * places SeekBar in a dialog.
 * <br><br>
 * Jetpack Preference 中提供了 SeekBarPreference，但是存在着诸多限制。比如说最大最小值都只能设置为整数，
 * 显示的值大小不能自定义格式等。最大的问题是，SeekBarPreference 不能作为弹窗显示。
 * <br>
 * SeekBarDialogPreference基于DialogPreference实现，新增了许多功能，并且将SeekBar放置在一个对话框中。
 */
public abstract class BaseSeekBarDialogPreference extends DialogPreference {
    public BaseSeekBarDialogPreference(Context context, AttributeSet attrs,
            int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public BaseSeekBarDialogPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public BaseSeekBarDialogPreference(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public BaseSeekBarDialogPreference(Context context) {
        super(context);
    }

    /**
     * Get SeekBar minimum value.
     *
     * @return SeekBar minimum value
     */
    public abstract float getMin();

    /**
     * Set SeekBar minimum value.
     *
     * @param min SeekBar minimum value
     */
    public abstract void setMin(float min);

    /**
     * Get SeekBar maximum value.
     *
     * @return SeekBar maximum value
     */
    public abstract float getMax();

    /**
     * Set SeekBar maximum value.
     *
     * @param max SeekBar maximum value
     */
    public abstract void setMax(float max);

    /**
     * Get SeekBar increment.
     *
     * @return SeekBar increment
     */
    public abstract float getIncrement();

    /**
     * Set SeekBar increment.
     *
     * @param increment SeekBar increment
     */
    public abstract void setIncrement(float increment);

    /**
     * Get String value format.
     *
     * @return String value format
     */
    public abstract String getFormat();

    /**
     * Set String value format.
     *
     * @param format String value format
     */
    public abstract void setFormat(String format);

    /**
     * Get value.
     *
     * @return value
     */
    public abstract float getValue();

    /**
     * Set value.
     *
     * @param value value
     */
    public abstract void setValue(float value);

    /**
     * Set {@code SeekBar.OnSeekBarChangeListener}.
     *
     * @param listener {@code SeekBar.OnSeekBarChangeListener}
     */
    public abstract void setListener(SeekBar.OnSeekBarChangeListener listener);

    public static final class SimpleSummaryProvider implements SummaryProvider<BaseSeekBarDialogPreference> {

        private static SimpleSummaryProvider simpleSummaryProvider;

        private SimpleSummaryProvider() { }

        @NonNull
        public static SimpleSummaryProvider getInstance() {
            if (simpleSummaryProvider == null) {
                simpleSummaryProvider = new SimpleSummaryProvider();
            }
            return simpleSummaryProvider;
        }

        @Override
        public CharSequence provideSummary(BaseSeekBarDialogPreference preference) {
            return String.format(preference.getFormat(), preference.getValue());
        }
    }
}
