package com.visionpay.opt.pax.entity;

import androidx.annotation.Keep;

import java.io.Serializable;

@Keep
public class POSTransaction implements Serializable {

    private String externalDeviceToken;

    private String externalReference;

    private long transactionAmount;

    private int transactionCurrency;

    private ProcessType processType;

    public POSTransaction(){
        processType = ProcessType.Payment;
    }

    public String getExternalDeviceToken() {
        return externalDeviceToken;
    }

    public void setExternalDeviceToken(String externalDeviceToken) {
        this.externalDeviceToken = externalDeviceToken;
    }

    public String getExternalReference() {
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public long getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(long transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public int getTransactionCurrency() {
        return transactionCurrency;
    }

    public void setTransactionCurrency(int transactionCurrency) {
        this.transactionCurrency = transactionCurrency;
    }

    public ProcessType getProcessType() {
        return processType;
    }

    public void setProcessType(ProcessType processType) {
        this.processType = processType;
    }

    public boolean checkValues(){
        if(externalDeviceToken == null || externalDeviceToken.equals(""))
            return false;

        if(processType != ProcessType.Receipt && processType != ProcessType.Cancel) {
            if (externalReference == null || externalReference.equals(""))
                return false;
        }

        if(processType == ProcessType.Payment) {
            if (transactionAmount <= 0)
                return false;

            if (transactionCurrency <= 0)
                return false;
        }

        return true;
    }
}

