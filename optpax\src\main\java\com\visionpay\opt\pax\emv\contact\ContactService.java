/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.contact;

import android.os.ConditionVariable;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.pax.emvbase.process.ISendingOnlineCallback;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.contactless.ContactlessService;
import com.visionpay.opt.pax.emv.device.EmvDeviceImpl;
import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.entity.EPiccType;
import com.pax.emvbase.utils.EmvDebugger;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvTag;
import com.pax.emvservice.export.IEmvContactService;
import com.pax.jemv.device.DeviceManager;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Contact EMV Service.
 */
public class ContactService implements IEmvService<IEmvContactService> {
    private static final String TAG = "ContactService";
    private final ConditionVariable cv = new ConditionVariable();
    private final List<String> emvTagContentList = new ArrayList<>();
    private final List<EmvTag> emvTagList = new ArrayList<>();
    private ConfirmCallback confirmCallback;
    private ErrorCallback errorCallback;
    private CompleteCallback completeCallback;
    private ContactlessService.RemoveCardCallback removeCardCallback;
    private ISendingOnlineCallback sendinfOnlineCallback;

    @NonNull
    @Override
    public ContactService start(@NonNull IEmvContactService emv, long amount, String terminalId, int detectResult, String reference) {
        LogUtils.d(TAG, "============ Start Contact EMV Service ============");
        App.getApp().runInBackground(() -> {
            emvTagContentList.clear();
            emvTagList.clear();
            EmvDebugger.setDebuggable(true);
            EmvDebugger.setCustomDebugger(new EmvTagDebugger());
            List<CvmType> cvmTypeList = new LinkedList<>();
            try {
                DeviceManager.getInstance().setIDevice(EmvDeviceImpl.getInstance());
                int ret = emv.startTransProcess(amount, terminalId, detectResult, reference, new ContactProcessCallback(emv, cv)
                        .setRemoveCardCallback(removeCardCallback)
                        .setConfirmCallback(confirmCallback)
                        .setErrorCallback(errorCallback)
                        .setCvmTypeList(cvmTypeList), sendinfOnlineCallback);
                LogUtils.d(TAG, "Emv ret: " + ret);
            } catch (Exception e) {
                LogUtils.e(TAG, "Emv service error", e);
                if (errorCallback != null) {
                    errorCallback.onError("Unexpected error",
                            "EMV service throw a "
                                    + e.getClass().getSimpleName()
                                    + ". Please check log.");
                }
            } finally {
                LogUtils.d(TAG, "============ Stop Search Card ============");
                closeMag();
                closeIcc();
                closeInternalPicc();
                closeExternalPicc();

                LogUtils.d(TAG, "============ Start Check Result ============");

                try {
                    emv.checkContactResult(new ContactResultListener(
                            emv,
                            cvmTypeList,
                            emvTagList,
                            errorCallback,
                            completeCallback));
                } catch (Exception e) {
                    LogUtils.e(TAG, "Emv service error", e);
                }

                EmvDebugger.setCustomDebugger(null);
            }
        });
        return this;
    }

    private class EmvTagDebugger implements EmvDebugger.Debugger {
        @Override
        public void d(@Nullable String logTag, @Nullable String methodName, @NonNull String emvTag,
                @Nullable String tagValue) {
            if (tagValue == null || tagValue.isEmpty()) {
                return;
            }
            // tagValue: 00000000000000000......
            if (tagValue.matches("^0+$") && tagValue.length() > 20) {
                return;
            }
            if (!emvTagContentList.contains(emvTag)) {
                emvTagList.add(new EmvTag(emvTag, tagValue));
                emvTagContentList.add(emvTag);
            }
            LogUtils.d(logTag, methodName + " - tag: " + emvTag + ", value: " + tagValue);
        }
    }

    @NonNull
    @Override
    public ContactService setConfirmCallback(@Nullable ConfirmCallback callback) {
        this.confirmCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public ContactService setErrorCallback(@Nullable ErrorCallback callback) {
        this.errorCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public ContactService setCompleteCallback(@Nullable CompleteCallback callback) {
        this.completeCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public ContactService setSendinfOnlineCallback(@Nullable ISendingOnlineCallback callback) {
        this.sendinfOnlineCallback = callback;
        return this;
    }

    @NonNull
    public ContactService setRemoveCardCallback(@Nullable ContactlessService.RemoveCardCallback callback) {
        this.removeCardCallback = callback;
        return this;
    }

    private void closeMag() {
        try {
            App.getDal().getMag().close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close mag failed", e);
        }
    }

    private void closeIcc() {
        try {
            App.getDal().getIcc().close((byte) 0);
        } catch (Exception e) {
            LogUtils.e(TAG, "close icc failed", e);
        }
    }

    private void closeInternalPicc() {
        try {
            App.getDal().getPicc(EPiccType.INTERNAL).close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close internal picc failed", e);
        }
    }

    private void closeExternalPicc() {
        try {
            App.getDal().getPicc(EPiccType.EXTERNAL).close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close external picc failed", e);
        }
    }
}
