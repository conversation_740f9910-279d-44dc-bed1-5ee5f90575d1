package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.clss.rupay.RupayAidBean;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "rupay_aid".
*/
public class RupayAidBeanDao extends AbstractDao<RupayAidBean, Long> {

    public static final String TABLENAME = "rupay_aid";

    /**
     * Properties of entity RupayAidBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "rupay_id");
        public final static Property AppName = new Property(1, String.class, "appName", false, "APP_NAME");
        public final static Property Aid = new Property(2, String.class, "aid", false, "AID");
        public final static Property SelFlag = new Property(3, byte.class, "selFlag", false, "SEL_FLAG");
        public final static Property TransLimit = new Property(4, long.class, "transLimit", false, "TRANS_LIMIT");
        public final static Property TransLimitFlag = new Property(5, byte.class, "transLimitFlag", false, "TRANS_LIMIT_FLAG");
        public final static Property FloorLimit = new Property(6, long.class, "floorLimit", false, "FLOOR_LIMIT");
        public final static Property FloorLimitFlag = new Property(7, byte.class, "floorLimitFlag", false, "FLOOR_LIMIT_FLAG");
        public final static Property CvmLimit = new Property(8, long.class, "cvmLimit", false, "CVM_LIMIT");
        public final static Property CvmLimitFlag = new Property(9, byte.class, "cvmLimitFlag", false, "CVM_LIMIT_FLAG");
        public final static Property TacDenial = new Property(10, String.class, "tacDenial", false, "TAC_DENIAL");
        public final static Property TacOnline = new Property(11, String.class, "tacOnline", false, "TAC_ONLINE");
        public final static Property TacDefault = new Property(12, String.class, "tacDefault", false, "TAC_DEFAULT");
        public final static Property AcquirerId = new Property(13, String.class, "acquirerId", false, "ACQUIRER_ID");
        public final static Property Version = new Property(14, String.class, "version", false, "VERSION");
        public final static Property TerminalType = new Property(15, String.class, "terminalType", false, "TERMINAL_TYPE");
        public final static Property TerminalCapability = new Property(16, String.class, "terminalCapability", false, "TERMINAL_CAPABILITY");
        public final static Property TerminalAdditionalCapability = new Property(17, String.class, "terminalAdditionalCapability", false, "TERMINAL_ADDITIONAL_CAPABILITY");
        public final static Property ServiceId = new Property(18, String.class, "serviceId", false, "SERVICE_ID");
        public final static Property Crypto17Flag = new Property(19, byte.class, "crypto17Flag", false, "CRYPTO17_FLAG");
        public final static Property ZeroAmountNoAllowed = new Property(20, byte.class, "zeroAmountNoAllowed", false, "ZERO_AMOUNT_NO_ALLOWED");
        public final static Property StatusCheckFlag = new Property(21, byte.class, "statusCheckFlag", false, "STATUS_CHECK_FLAG");
        public final static Property Ttq = new Property(22, String.class, "ttq", false, "TTQ");
    }


    public RupayAidBeanDao(DaoConfig config) {
        super(config);
    }
    
    public RupayAidBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"rupay_aid\" (" + //
                "\"rupay_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"APP_NAME\" TEXT," + // 1: appName
                "\"AID\" TEXT," + // 2: aid
                "\"SEL_FLAG\" INTEGER NOT NULL ," + // 3: selFlag
                "\"TRANS_LIMIT\" INTEGER NOT NULL ," + // 4: transLimit
                "\"TRANS_LIMIT_FLAG\" INTEGER NOT NULL ," + // 5: transLimitFlag
                "\"FLOOR_LIMIT\" INTEGER NOT NULL ," + // 6: floorLimit
                "\"FLOOR_LIMIT_FLAG\" INTEGER NOT NULL ," + // 7: floorLimitFlag
                "\"CVM_LIMIT\" INTEGER NOT NULL ," + // 8: cvmLimit
                "\"CVM_LIMIT_FLAG\" INTEGER NOT NULL ," + // 9: cvmLimitFlag
                "\"TAC_DENIAL\" TEXT," + // 10: tacDenial
                "\"TAC_ONLINE\" TEXT," + // 11: tacOnline
                "\"TAC_DEFAULT\" TEXT," + // 12: tacDefault
                "\"ACQUIRER_ID\" TEXT," + // 13: acquirerId
                "\"VERSION\" TEXT," + // 14: version
                "\"TERMINAL_TYPE\" TEXT," + // 15: terminalType
                "\"TERMINAL_CAPABILITY\" TEXT," + // 16: terminalCapability
                "\"TERMINAL_ADDITIONAL_CAPABILITY\" TEXT," + // 17: terminalAdditionalCapability
                "\"SERVICE_ID\" TEXT," + // 18: serviceId
                "\"CRYPTO17_FLAG\" INTEGER NOT NULL ," + // 19: crypto17Flag
                "\"ZERO_AMOUNT_NO_ALLOWED\" INTEGER NOT NULL ," + // 20: zeroAmountNoAllowed
                "\"STATUS_CHECK_FLAG\" INTEGER NOT NULL ," + // 21: statusCheckFlag
                "\"TTQ\" TEXT);"); // 22: ttq
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"rupay_aid\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, RupayAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
 
        String tacDenial = entity.getTacDenial();
        if (tacDenial != null) {
            stmt.bindString(11, tacDenial);
        }
 
        String tacOnline = entity.getTacOnline();
        if (tacOnline != null) {
            stmt.bindString(12, tacOnline);
        }
 
        String tacDefault = entity.getTacDefault();
        if (tacDefault != null) {
            stmt.bindString(13, tacDefault);
        }
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(14, acquirerId);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(15, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(16, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(17, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(18, terminalAdditionalCapability);
        }
 
        String serviceId = entity.getServiceId();
        if (serviceId != null) {
            stmt.bindString(19, serviceId);
        }
        stmt.bindLong(20, entity.getCrypto17Flag());
        stmt.bindLong(21, entity.getZeroAmountNoAllowed());
        stmt.bindLong(22, entity.getStatusCheckFlag());
 
        String ttq = entity.getTtq();
        if (ttq != null) {
            stmt.bindString(23, ttq);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, RupayAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
 
        String tacDenial = entity.getTacDenial();
        if (tacDenial != null) {
            stmt.bindString(11, tacDenial);
        }
 
        String tacOnline = entity.getTacOnline();
        if (tacOnline != null) {
            stmt.bindString(12, tacOnline);
        }
 
        String tacDefault = entity.getTacDefault();
        if (tacDefault != null) {
            stmt.bindString(13, tacDefault);
        }
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(14, acquirerId);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(15, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(16, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(17, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(18, terminalAdditionalCapability);
        }
 
        String serviceId = entity.getServiceId();
        if (serviceId != null) {
            stmt.bindString(19, serviceId);
        }
        stmt.bindLong(20, entity.getCrypto17Flag());
        stmt.bindLong(21, entity.getZeroAmountNoAllowed());
        stmt.bindLong(22, entity.getStatusCheckFlag());
 
        String ttq = entity.getTtq();
        if (ttq != null) {
            stmt.bindString(23, ttq);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public RupayAidBean readEntity(Cursor cursor, int offset) {
        RupayAidBean entity = new RupayAidBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // appName
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // aid
            (byte) cursor.getShort(offset + 3), // selFlag
            cursor.getLong(offset + 4), // transLimit
            (byte) cursor.getShort(offset + 5), // transLimitFlag
            cursor.getLong(offset + 6), // floorLimit
            (byte) cursor.getShort(offset + 7), // floorLimitFlag
            cursor.getLong(offset + 8), // cvmLimit
            (byte) cursor.getShort(offset + 9), // cvmLimitFlag
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // tacDenial
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // tacOnline
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // tacDefault
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // acquirerId
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // version
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // terminalType
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // terminalCapability
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // terminalAdditionalCapability
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // serviceId
            (byte) cursor.getShort(offset + 19), // crypto17Flag
            (byte) cursor.getShort(offset + 20), // zeroAmountNoAllowed
            (byte) cursor.getShort(offset + 21), // statusCheckFlag
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22) // ttq
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, RupayAidBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAppName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAid(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSelFlag((byte) cursor.getShort(offset + 3));
        entity.setTransLimit(cursor.getLong(offset + 4));
        entity.setTransLimitFlag((byte) cursor.getShort(offset + 5));
        entity.setFloorLimit(cursor.getLong(offset + 6));
        entity.setFloorLimitFlag((byte) cursor.getShort(offset + 7));
        entity.setCvmLimit(cursor.getLong(offset + 8));
        entity.setCvmLimitFlag((byte) cursor.getShort(offset + 9));
        entity.setTacDenial(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setTacOnline(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setTacDefault(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setAcquirerId(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setVersion(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setTerminalType(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setTerminalCapability(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setTerminalAdditionalCapability(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setServiceId(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setCrypto17Flag((byte) cursor.getShort(offset + 19));
        entity.setZeroAmountNoAllowed((byte) cursor.getShort(offset + 20));
        entity.setStatusCheckFlag((byte) cursor.getShort(offset + 21));
        entity.setTtq(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(RupayAidBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(RupayAidBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(RupayAidBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
