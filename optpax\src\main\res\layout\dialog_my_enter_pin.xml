<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_background"
    app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
    app:behavior_hideable="true"
    android:padding="20dp"
    tools:context=".dialog.MyEnterPinDialog">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="20dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:id="@+id/enter_pin_title"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_marginBottom="260dp"
            android:layout_marginTop="10dp"
            android:background="#33FFFFFF"
            android:textSize="40dp"
            android:gravity="center"
            android:textColor="@color/white"
            android:id="@+id/txvPin"/>

        <!--GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:rowCount="4"
            android:columnCount="3"
            android:useDefaultMargins="true">

            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="7"
                style="@style/PinPadNumber"
                android:id="@+id/button7"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="8"
                style="@style/PinPadNumber"
                android:id="@+id/button8"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="9"
                style="@style/PinPadNumber"
                android:id="@+id/button9"/>

            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="4"
                style="@style/PinPadNumber"
                android:id="@+id/button4"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="5"
                style="@style/PinPadNumber"
                android:id="@+id/button5"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="6"
                style="@style/PinPadNumber"
                android:id="@+id/button6"/>

            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="1"
                style="@style/PinPadNumber"
                android:id="@+id/button1"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="2"
                style="@style/PinPadNumber"
                android:id="@+id/button2"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="3"
                style="@style/PinPadNumber"
                android:id="@+id/button3"/>

            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="Clear"
                style="@style/PinPadClear"
                android:layout_marginTop="-4dp"
                android:id="@+id/buttonClear"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="0"
                style="@style/PinPadNumber"
                android:id="@+id/button0"/>
            <android.widget.Button
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_columnWeight="1"
                android:gravity="center"
                android:layout_gravity="fill_horizontal"
                android:text="Enter"
                style="@style/PinPadEnter"
                android:layout_marginTop="-4dp"
                android:id="@+id/buttonEnter"/>

        </GridLayout-->

    </androidx.appcompat.widget.LinearLayoutCompat>

</FrameLayout>