/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.preference.Preference;
import com.pax.preflib.builder.factory.PrefFactory;

/**
 * Create Preference with a click event callback builder.
 */
public class ButtonPrefBuilder extends BasePrefBuilder<Preference, ButtonPrefBuilder> {
    private Integer iconId;
    private String summary;

    private ButtonPrefBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        super(context, key, title);
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param titleId Title string resource id
     * @return New instance
     */
    public static ButtonPrefBuilder newInstance(@NonNull Context context, @NonNull String key,
            @StringRes int titleId) {
        return new ButtonPrefBuilder(context, key, context.getString(titleId));
    }

    /**
     * Create new instance.
     *
     * @param context Context
     * @param key Configuration key
     * @param title Title
     * @return New instance
     */
    public static ButtonPrefBuilder newInstance(@NonNull Context context, @NonNull String key, @NonNull String title) {
        return new ButtonPrefBuilder(context, key, title);
    }

    /**
     * Set preference icon resource id. If not set, no space will be reserved on the screen.
     *
     * @param iconId Icon resource id
     * @return This builder
     */
    public ButtonPrefBuilder setIconId(@DrawableRes int iconId) {
        this.iconId = iconId;
        return this;
    }

    /**
     * Set summary by string resource id.
     *
     * @param summaryId Summary string resource id
     * @return This builder
     */
    public ButtonPrefBuilder setSummary(@StringRes int summaryId) {
        this.summary = context.getString(summaryId);
        return this;
    }

    /**
     * Set summary.
     *
     * @param summary Summary
     * @return This builder
     */
    public ButtonPrefBuilder setSummary(@Nullable String summary) {
        this.summary = summary;
        return this;
    }

    @NonNull
    @Override
    public Preference build() {
        Preference preference = PrefFactory.getInstance().createPreference(context);
        preference.setKey(key);
        preference.setTitle(title);
        if (iconId != null) {
            preference.setIcon(iconId);
        } else {
            preference.setIconSpaceReserved(false);
        }
        if (summary != null) {
            preference.setSummary(summary);
        }
        if (onPreferenceClickListener != null) {
            preference.setOnPreferenceClickListener(onPreferenceClickListener);
        }
        return preference;
    }
}
