package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.Acquirer;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "acquirer".
*/
public class AcquirerDao extends AbstractDao<Acquirer, Long> {

    public static final String TABLENAME = "acquirer";

    /**
     * Properties of entity Acquirer.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "acquirer_id");
        public final static Property Name = new Property(1, String.class, "name", false, "acquirer_name");
        public final static Property Nii = new Property(2, String.class, "nii", false, "NII");
        public final static Property TerminalId = new Property(3, String.class, "terminalId", false, "TERMINAL_ID");
        public final static Property MerchantId = new Property(4, String.class, "merchantId", false, "MERCHANT_ID");
        public final static Property CurrBatchNo = new Property(5, int.class, "currBatchNo", false, "CURR_BATCH_NO");
        public final static Property Ip = new Property(6, String.class, "ip", false, "IP");
        public final static Property Port = new Property(7, int.class, "port", false, "PORT");
        public final static Property Url = new Property(8, String.class, "url", false, "URL");
        public final static Property IpBak1 = new Property(9, String.class, "ipBak1", false, "IP_BAK1");
        public final static Property PortBak1 = new Property(10, short.class, "portBak1", false, "PORT_BAK1");
        public final static Property IpBak2 = new Property(11, String.class, "ipBak2", false, "IP_BAK2");
        public final static Property PortBak2 = new Property(12, short.class, "portBak2", false, "PORT_BAK2");
        public final static Property TcpTimeOut = new Property(13, int.class, "tcpTimeOut", false, "TCP_TIME_OUT");
        public final static Property WirelessTimeOut = new Property(14, int.class, "wirelessTimeOut", false, "WIRELESS_TIME_OUT");
        public final static Property SslType = new Property(15, String.class, "sslType", false, "ssl_type");
        public final static Property IsEnableKeyIn = new Property(16, boolean.class, "isEnableKeyIn", false, "key_in");
        public final static Property IsEnableQR = new Property(17, boolean.class, "isEnableQR", false, "qr");
    }


    public AcquirerDao(DaoConfig config) {
        super(config);
    }
    
    public AcquirerDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"acquirer\" (" + //
                "\"acquirer_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"acquirer_name\" TEXT UNIQUE ," + // 1: name
                "\"NII\" TEXT NOT NULL ," + // 2: nii
                "\"TERMINAL_ID\" TEXT NOT NULL ," + // 3: terminalId
                "\"MERCHANT_ID\" TEXT NOT NULL ," + // 4: merchantId
                "\"CURR_BATCH_NO\" INTEGER NOT NULL ," + // 5: currBatchNo
                "\"IP\" TEXT," + // 6: ip
                "\"PORT\" INTEGER NOT NULL ," + // 7: port
                "\"URL\" TEXT," + // 8: url
                "\"IP_BAK1\" TEXT," + // 9: ipBak1
                "\"PORT_BAK1\" INTEGER NOT NULL ," + // 10: portBak1
                "\"IP_BAK2\" TEXT," + // 11: ipBak2
                "\"PORT_BAK2\" INTEGER NOT NULL ," + // 12: portBak2
                "\"TCP_TIME_OUT\" INTEGER NOT NULL ," + // 13: tcpTimeOut
                "\"WIRELESS_TIME_OUT\" INTEGER NOT NULL ," + // 14: wirelessTimeOut
                "\"ssl_type\" TEXT," + // 15: sslType
                "\"key_in\" INTEGER NOT NULL ," + // 16: isEnableKeyIn
                "\"qr\" INTEGER NOT NULL );"); // 17: isEnableQR
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"acquirer\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, Acquirer entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(2, name);
        }
        stmt.bindString(3, entity.getNii());
        stmt.bindString(4, entity.getTerminalId());
        stmt.bindString(5, entity.getMerchantId());
        stmt.bindLong(6, entity.getCurrBatchNo());
 
        String ip = entity.getIp();
        if (ip != null) {
            stmt.bindString(7, ip);
        }
        stmt.bindLong(8, entity.getPort());
 
        String url = entity.getUrl();
        if (url != null) {
            stmt.bindString(9, url);
        }
 
        String ipBak1 = entity.getIpBak1();
        if (ipBak1 != null) {
            stmt.bindString(10, ipBak1);
        }
        stmt.bindLong(11, entity.getPortBak1());
 
        String ipBak2 = entity.getIpBak2();
        if (ipBak2 != null) {
            stmt.bindString(12, ipBak2);
        }
        stmt.bindLong(13, entity.getPortBak2());
        stmt.bindLong(14, entity.getTcpTimeOut());
        stmt.bindLong(15, entity.getWirelessTimeOut());
 
        String sslType = entity.getSslType();
        if (sslType != null) {
            stmt.bindString(16, sslType);
        }
        stmt.bindLong(17, entity.getIsEnableKeyIn() ? 1L: 0L);
        stmt.bindLong(18, entity.getIsEnableQR() ? 1L: 0L);
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, Acquirer entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String name = entity.getName();
        if (name != null) {
            stmt.bindString(2, name);
        }
        stmt.bindString(3, entity.getNii());
        stmt.bindString(4, entity.getTerminalId());
        stmt.bindString(5, entity.getMerchantId());
        stmt.bindLong(6, entity.getCurrBatchNo());
 
        String ip = entity.getIp();
        if (ip != null) {
            stmt.bindString(7, ip);
        }
        stmt.bindLong(8, entity.getPort());
 
        String url = entity.getUrl();
        if (url != null) {
            stmt.bindString(9, url);
        }
 
        String ipBak1 = entity.getIpBak1();
        if (ipBak1 != null) {
            stmt.bindString(10, ipBak1);
        }
        stmt.bindLong(11, entity.getPortBak1());
 
        String ipBak2 = entity.getIpBak2();
        if (ipBak2 != null) {
            stmt.bindString(12, ipBak2);
        }
        stmt.bindLong(13, entity.getPortBak2());
        stmt.bindLong(14, entity.getTcpTimeOut());
        stmt.bindLong(15, entity.getWirelessTimeOut());
 
        String sslType = entity.getSslType();
        if (sslType != null) {
            stmt.bindString(16, sslType);
        }
        stmt.bindLong(17, entity.getIsEnableKeyIn() ? 1L: 0L);
        stmt.bindLong(18, entity.getIsEnableQR() ? 1L: 0L);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public Acquirer readEntity(Cursor cursor, int offset) {
        Acquirer entity = new Acquirer( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // name
            cursor.getString(offset + 2), // nii
            cursor.getString(offset + 3), // terminalId
            cursor.getString(offset + 4), // merchantId
            cursor.getInt(offset + 5), // currBatchNo
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // ip
            cursor.getInt(offset + 7), // port
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // url
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // ipBak1
            cursor.getShort(offset + 10), // portBak1
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // ipBak2
            cursor.getShort(offset + 12), // portBak2
            cursor.getInt(offset + 13), // tcpTimeOut
            cursor.getInt(offset + 14), // wirelessTimeOut
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // sslType
            cursor.getShort(offset + 16) != 0, // isEnableKeyIn
            cursor.getShort(offset + 17) != 0 // isEnableQR
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, Acquirer entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setNii(cursor.getString(offset + 2));
        entity.setTerminalId(cursor.getString(offset + 3));
        entity.setMerchantId(cursor.getString(offset + 4));
        entity.setCurrBatchNo(cursor.getInt(offset + 5));
        entity.setIp(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setPort(cursor.getInt(offset + 7));
        entity.setUrl(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setIpBak1(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setPortBak1(cursor.getShort(offset + 10));
        entity.setIpBak2(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setPortBak2(cursor.getShort(offset + 12));
        entity.setTcpTimeOut(cursor.getInt(offset + 13));
        entity.setWirelessTimeOut(cursor.getInt(offset + 14));
        entity.setSslType(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setIsEnableKeyIn(cursor.getShort(offset + 16) != 0);
        entity.setIsEnableQR(cursor.getShort(offset + 17) != 0);
     }
    
    @Override
    protected final Long updateKeyAfterInsert(Acquirer entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(Acquirer entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(Acquirer entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
