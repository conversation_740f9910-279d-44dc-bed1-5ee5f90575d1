package com.visionpay.opt.pax.activity;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.pax.bizentity.entity.ETransType;
import com.pax.bizentity.entity.SearchMode;
import com.pax.commonlib.application.ActivityStack;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.sankuai.waimai.router.common.DefaultUriRequest;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.entity.IM30Transaction;
import com.visionpay.opt.pax.entity.POSTransaction;
import com.visionpay.opt.pax.entity.ProcessType;
import com.visionpay.opt.pax.entity.ReadState;
import com.visionpay.opt.pax.entity.ReceiptMessage;
import com.visionpay.opt.pax.message.BroadcastMesseger;
import com.visionpay.opt.pax.message.BroadcastMessegerListener;
import com.visionpay.opt.pax.thread.OnlineThread;
import com.visionpay.opt.pax.utils.ProcessTypeDeserializer;
import com.visionpay.opt.pax.viewmodel.ReceiptReadCardViewModel;

import java.lang.reflect.Type;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@RouterUri(path = EmvRouterConst.RECEIPT_READ_CARD)
public class ReceiptReadCardActivity extends BaseActivity implements BroadcastMessegerListener {

    private TextView mTextData;
    private View cardTap;
    private View cardSwipe;
    private View cardInsert;

    private POSTransaction POSTransaction = null;

    private IM30Transaction im30t = null;
    private ReceiptMessage receiptMessage = null;

    private ReceiptReadCardViewModel viewModel;
    private boolean isRestore = false;

    private int FINISH_DELAY = 60000;

    private int attemptsReadCard = 3;

    private final int ERROR_ACTIVITY_REQUEST = 100;
    private final Handler mFinishHandler = new Handler(Looper.myLooper());
    private final Runnable mAutoFinishRunnable = () -> finishActivity(ReadState.Timeout);

    ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        isRestore = (savedInstanceState != null);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_receipt_read_card);

        viewModel = new ViewModelProvider(this).get(ReceiptReadCardViewModel.class);

        mTextData = findViewById(R.id.text_data);
        cardTap = findViewById(R.id.cardTap);
        cardSwipe = findViewById(R.id.cardSwipe);
        cardInsert = findViewById(R.id.cardInsert);

        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        FINISH_DELAY = configParamService.getInt(ConfigKeyConstant.AUTO_CANCEL_RECEIPT_DELAY, 60000);
        attemptsReadCard = configParamService.getInt(ConfigKeyConstant.ATTEMPTS_READ_CARD, 3);

        if (!isRestore) {
            Intent intent = getIntent();
            if (intent != null) {
                POSTransaction = (POSTransaction) getIntent().getSerializableExtra(BundleFieldConst.POSTRANSACTION);

                im30t = new IM30Transaction(POSTransaction);
                receiptMessage = new ReceiptMessage();

                viewModel.initTrans(ETransType.SALE);

                BroadcastMesseger.getInstance().send(im30t);
                BroadcastMesseger.getInstance().send(receiptMessage);

                setListeners();
            }

            BroadcastMesseger.getInstance().start(this);
        }

        mFinishHandler.postDelayed(mAutoFinishRunnable, FINISH_DELAY);
    }

    @Override
    public void onBackPressed() {
        mFinishHandler.removeCallbacks(mAutoFinishRunnable);
        super.onBackPressed();
    }

    public void finishActivity(ReadState receipState){
        receiptMessage.setReceiptState(receipState);
        BroadcastMesseger.getInstance().send(receiptMessage);
        BroadcastMesseger.getInstance().stop();
        if(!BroadcastMesseger.getInstance().isInternalMessage()) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK); // equal to Intent.FLAG_ACTIVITY_CLEAR_TASK which is only available from API level 11
            this.startActivity(intent);
        } else {
            ActivityStack.getInstance().popAll();
        }
    }

    @Override
    protected void onDestroy() {
        mFinishHandler.removeCallbacks(mAutoFinishRunnable);
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == ERROR_ACTIVITY_REQUEST) {
            viewModel.initTrans(ETransType.SALE);
            attemptsReadCard--;
            mTextData.setText("Please, try again. Waiting Card...");
            cardTap.setVisibility(View.VISIBLE);
            cardSwipe.setVisibility(View.VISIBLE);
            cardInsert.setVisibility(View.VISIBLE);
            mFinishHandler.postDelayed(mAutoFinishRunnable, FINISH_DELAY);
            BroadcastMesseger.getInstance().start(this);
            viewModel.startSearchCard();
        }
    }

    protected void setListeners() {
        viewModel.setMessageCallback(message -> runOnUiThread(() -> mTextData.setText(message)));

        viewModel.setErrorEventCallback(event -> {
            if (event != null) {
                runOnUiThread(() -> {
                    if(attemptsReadCard <= 0) {
                        receiptMessage.setReceiptState(ReadState.CardReadFailed);
                        BroadcastMesseger.getInstance().send(receiptMessage);
                    }
                    Router.startUri(new DefaultUriRequest(this, EmvRouterConst.ERROR_CARD)
                            .activityRequestCode(ERROR_ACTIVITY_REQUEST)
                            .putExtra(BundleFieldConst.ATTEMPTS_READ_CARD, attemptsReadCard));
                });
            }
        });

        viewModel.setCardDetectCallback((searchMode) -> {
            runOnUiThread(() -> {
                mFinishHandler.removeCallbacks(mAutoFinishRunnable);
                BroadcastMesseger.getInstance().stop();
                switch (searchMode){
                    case SearchMode.INSERT:
                        cardTap.setVisibility(View.GONE);
                        cardSwipe.setVisibility(View.GONE);
                        break;
                    case SearchMode.WAVE:
                        cardInsert.setVisibility(View.GONE);
                        cardSwipe.setVisibility(View.GONE);
                        break;
                    case SearchMode.SWIPE:
                        cardTap.setVisibility(View.GONE);
                        cardInsert.setVisibility(View.GONE);
                        break;
                }
            });
        });

        viewModel.setCompleteReaderCallback((cardSignature) -> {
            runOnUiThread(() -> {
                receiptMessage.setReceiptState(ReadState.CardReadSuccess);
                receiptMessage.setCardSignature(cardSignature);
                BroadcastMesseger.getInstance().send(receiptMessage);
                if (!BroadcastMesseger.getInstance().isInternalMessage()) {
                    Router.startUri(new DefaultUriRequest(this, EmvRouterConst.SUCCESS_CARD)
                            .putExtra(BundleFieldConst.TITLESUCESS, "Receipt Generated"));
                } else {
                    Router.startUri(new DefaultUriRequest(this, EmvRouterConst.SUCCESS_INTERNAL_CARD)
                            .putExtra(BundleFieldConst.TITLESUCESS, "Receipt Generated"));
                }
            });
        });

        if (!isRestore) {
            attemptsReadCard--;
            viewModel.startSearchCard();
        }

        isRestore = false;  // Reset flag
    }

    @Override
    public void OnMessageReceive(String message) {

        Gson gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                .registerTypeAdapter(ProcessType.class, new ProcessTypeDeserializer()).create();

        Type listType = new TypeToken<List<POSTransaction>>(){}.getType();
        try {
            message = message.replaceAll("\\p{Cntrl}", "").replace("}{", "},{");
            message =  "[" + message + "]";

            List<POSTransaction> ptList = gson.fromJson(message, listType);
            for (POSTransaction pt : ptList) {
                if (pt.checkValues()) {
                    if (pt.getProcessType() == ProcessType.Capture || pt.getProcessType() == ProcessType.Reversal) {
                        executorService.execute(new OnlineThread(pt));
                    }
                    if (pt.getExternalDeviceToken().equals(POSTransaction.getExternalDeviceToken())
                        && pt.getProcessType() == ProcessType.Cancel) {
                        finishActivity(ReadState.Canceled);
                    }
                } else {
                    IM30Transaction im30t = new IM30Transaction(pt, "Incorrect values.");
                    BroadcastMesseger.getInstance().send(gson.toJson(im30t));
                }
            }
        } catch (Exception ex) {
            IM30Transaction im30t = new IM30Transaction(POSTransaction, ex.getMessage());
            BroadcastMesseger.getInstance().send(gson.toJson(im30t));
        }
    }
}