/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/31                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;
import com.pax.commonlib.utils.LogUtils;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.entity.TransStatus;
import java.util.List;

/**
 * Transaction Card PAN & AID Confirm Fragment ViewModel.
 */
public class TransConfirmViewModel extends BaseTransCardViewModel {
    private static final String TAG = "TransConfirmViewModel";
    private final MutableLiveData<TransStatus> statusLiveData = new MutableLiveData<>();

    @Override
    public void setStatus(TransStatus status) {
        LogUtils.d(TAG, "Update Status");
        statusLiveData.postValue(status);
    }

    public LiveData<TransStatus> getStatusLiveData() {
        return statusLiveData;
    }

    public LiveData<Integer> listenIssuerIcon() {
        return Transformations.map(statusLiveData, TransStatus::getIssuerIconId);
    }

    public LiveData<String> listenPan() {
        return Transformations.map(statusLiveData, TransStatus::getPan);
    }

    public LiveData<String> listenInfo() {
        return Transformations.map(statusLiveData, input -> {
            StringBuilder builder = new StringBuilder();
            if (input != null) {
                builder.append(input.getIssuerName());
                if (builder.length() != 0) {
                    builder.append(" · ");
                }
                builder.append(input.getEnterMode());
            }
            return builder.toString();
        });
    }

    public LiveData<String> listenCardholderName() {
        return Transformations.distinctUntilChanged(Transformations.map(statusLiveData,
                TransStatus::getCardHolderName));
    }

    public LiveData<List<EmvAidInfo>> listenAidInfo() {
        return Transformations.distinctUntilChanged(Transformations.map(statusLiveData,
                TransStatus::getAidInfoList));
    }
}
