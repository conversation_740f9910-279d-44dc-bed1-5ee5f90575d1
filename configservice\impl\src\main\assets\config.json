{"COMM_MODE": "ONLINE_TCP_ISO8583", "COMM_TYPE": "DEMO", "COMM_TIMEOUT": 30, "MOBILE_NEED_USER": false, "MOBILE_USER": "", "MOBILE_TEL_NO": "8888888", "MOBILE_APN": "szjrln.gd", "MOBILE_PWD": "", "MOBILE_HOST_PORT": 8998, "MOBILE_HOST_PORT_BAK": 0, "MOBILE_HOST_IP": "*************", "MOBILE_HOST_IP_BAK": "0.0.0.0", "LAN_LOCAL_IP": "*************", "LAN_NETMASK": "*************", "LAN_GATEWAY": "***********", "LAN_DNS1": "*************", "LAN_DNS2": "*************", "LAN_HOST_IP": "***************", "LAN_HOST_IP_BAK": "0.0.0.0", "LAN_HOST_PORT": 10021, "LAN_HOST_PORT_BAK": 0, "LAN_DHCP": false, "MK_INDEX": 1, "KEY_ALGORITHM": "3des", "MK_INDEX_MANUAL": 1, "MK_VALUE": "", "PK_VALUE": "", "AK_VALUE": "", "SEC_SYS_PWD": "c984aed014aec7623a54f0591da07a85fd4b762d", "SEC_MERCHANT_PWD": "c984aed014aec7623a54f0591da07a85fd4b762d", "SEC_TERMINAL_PWD": "c984aed014aec7623a54f0591da07a85fd4b762d", "SEC_VOID_PWD": "c984aed014aec7623a54f0591da07a85fd4b762d", "SEC_REFUND_PWD": "c984aed014aec7623a54f0591da07a85fd4b762d", "SEC_ADJUST_PWD": "c984aed014aec7623a54f0591da07a85fd4b762d", "SEC_SETTLE_PWD": "c984aed014aec7623a54f0591da07a85fd4b762d", "SEC_SYS_PWD_256": "91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203", "SEC_MERCHANT_PWD_256": "91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203", "SEC_TERMINAL_PWD_256": "91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203", "SEC_VOID_PWD_256": "91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203", "SEC_REFUND_PWD_256": "91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203", "SEC_ADJUST_PWD_256": "91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203", "SEC_SETTLE_PWD_256": "91b4d142823f7d20c5f08df69122de43f35f057a988d9619f6d3138485c9a203", "EDC_MERCHANT_NAME_EN": "VisionPay", "EDC_MERCHANT_ADDRESS": "", "EDC_CURRENCY_LIST": "English (Australia)", "EDC_PED_MODE": "Internal", "EDC_CLSS_MODE": "Internal", "EDC_RECEIPT_NUM": 1, "EDC_TRACE_NO": 1, "EDC_SUPPORT_TIP": true, "EDC_SUPPORT_KEYIN": true, "SUPPORT_USER_AGREEMENT": false, "EDC_ENABLE_PAPERLESS": true, "EDC_SMTP_HOST": "", "EDC_SMTP_PORT": 25, "EDC_SMTP_USERNAME": "", "EDC_SMTP_PASSWORD": "", "EDC_SMTP_ENABLE_SSL": false, "EDC_SMTP_SSL_PORT": 443, "EDC_SMTP_FROM": "", "EDC_PRINTER_TYPE": "Internal", "EDC_ENABLE_SAVE_LOG": false, "EDC_ENABLE_PRINT_LOG": false, "EDC_SOLVE_IMAG_CLS_CONFLICT": false, "EDC_PHYSICAL_CLS_LIGHT": false, "EDC_CATID": "", "EDC_CLIENT": "", "ACQ_NAME": "acquirer0", "COMPLETION_SCREEN_DELAY": 3000, "AUTO_CANCEL_MAG_DELAY": 60000, "AUTO_CANCEL_PAY_DELAY": 60000, "AUTO_CANCEL_RECEIPT_DELAY": 60000, "ATTEMPTS_READ_CARD": 3, "MAX_TRANS_COUNT": 500000, "COMM_REDIAL_TIMES": 3, "EDC_REVERSAL_RETRY": 3, "OFFLINE_TC_UPLOAD_TIMES": 3, "OFFLINE_TC_UPLOAD_NUM": 10, "QUICK_PASS_TRANS_PIN_FREE_AMOUNT": 30000, "QUICK_PASS_TRANS_SIGN_FREE_AMOUNT": 30000, "QUICK_PASS_TRANS_PIN_FREE_SWITCH": false, "QUICK_PASS_TRANS_FLAG": false, "QUICK_PASS_TRANS_SWITCH": false, "QUICK_PASS_TRANS_CDCVM_FLAG": false, "QUICK_PASS_TRANS_SIGN_FREE_FLAG": true, "OTHTC_VERIFY": true, "TTS_SALE": true, "TTS_VOID": true, "TTS_REFUND": true, "TTS_PREAUTH": true, "TTS_ADJUST": true, "LOG_FILE_INDEX": 1, "RESULT_READER_TYPE": 0, "BATCH_UP_STATUS": 0, "MERCHANT_CATEGORY_CODE": "5542", "TRANS_REFER_CURRENCY_CONVERSION": 1000, "ENABLE_PRINT_RECEIPT": true, "AD_BANNER_URL_1": "https://www.pax.com.cn/", "AD_BANNER_IMG_1": "https://www.pax.com.cn/uploadfiles/images/Web.png", "AD_BANNER_URL_2": "https://www.pax.com.cn/solutionsandroidsmartpos/index.aspx", "AD_BANNER_IMG_2": "https://www.pax.com.cn/uploadfiles/images/Solutions.png", "AD_BANNER_URL_3": "https://www.pax.com.cn/paxstore/index.aspx", "AD_BANNER_IMG_3": "https://www.pax.com.cn/uploadfiles/images/paxstore.png"}