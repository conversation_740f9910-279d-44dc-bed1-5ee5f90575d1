/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.callback;

import androidx.annotation.NonNull;

/**
 * Verify that the content entered is legal. This callback will put the string after
 * entering the new content into the {@code onChange()} method. You can check whether the
 * new string is legal in the callback. If it is not legal, return false, so that the user
 * can be prevented from entering this content, and the text in the input box will not
 * change. If it returns true, the user's current input is allowed.
 * <br>
 * Please note, do not regard this callback as {@code OnPreferenceChangeListener}, the
 * purpose of the two is completely different. This callback is to check whether the
 * content you input is legal, and {@code OnPreferenceChangeListener} is to check whether
 * the content you want to store is legal. This callback will be executed every time the
 * user enters or deletes a character, and {@code OnPreferenceChangeListener} will only
 * execute once before storing the data.
 * <br><br>
 * 添加一个验证输入内容是否合法的回调。这个回调会将用户输入了新内容的字符串通过 {@code onChange()} 方法传入。
 * 你可以通过检查这个字符串来验证输入的新内容是否合法。如果不合法，则返回 {@code false}，这样就会阻止用户输入
 * 这个新内容，而输入框仍然保持原来的内容。如果返回 {@code true}，则用户的输入将被批准。
 * <br>
 * 请注意，不要把这个回调和 {@code OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 搞
 * 混了，两者是完全不同的。{@code InputValidateCallback} 这个回调是用来检查用户正在输入的内容是否合法，而 {@code
 * OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 是用来检查用户想要保存的内容是否合法。
 * {@code InputValidateCallback} 这个回调会在每次用户输入或者删除一个字符的时候就被执行，而 {@code
 * OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 只有在保存数据前才会执行一次。
 */
public interface InputValidateCallback<T> {
    /**
     * Verify that the content entered is legal. This callback will put the string after
     * entering the new content into the {@code onChange()} method. You can check whether the
     * new string is legal in the callback. If it is not legal, return false, so that the user
     * can be prevented from entering this content, and the text in the input box will not
     * change. If it returns true, the user's current input is allowed.
     * <br>
     * Please note, do not regard this callback as {@code OnPreferenceChangeListener}, the
     * purpose of the two is completely different. This callback is to check whether the
     * content you input is legal, and {@code OnPreferenceChangeListener} is to check whether
     * the content you want to store is legal. This callback will be executed every time the
     * user enters or deletes a character, and {@code OnPreferenceChangeListener} will only
     * execute once before storing the data.
     * <br><br>
     * 验证输入内容是否合法的回调。这个回调会将用户输入了新内容的字符串通过 {@code onChange()} 方法传入。
     * 你可以通过检查这个字符串来验证输入的新内容是否合法。如果不合法，则返回 {@code false}，这样就会阻止用户输入
     * 这个新内容，而输入框仍然保持原来的内容。如果返回 {@code true}，则用户的输入将被批准。
     * <br>
     * 请注意，不要把这个回调和 {@code OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 搞
     * 混了，两者是完全不同的。{@code InputValidateCallback} 这个回调是用来检查用户正在输入的内容是否合法，而
     * {@code OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 是用来检查用户想要保存的内容
     * 是否合法。{@code InputValidateCallback} 这个回调会在每次用户输入或者删除一个字符的时候就被执行，而
     * {@code OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 只有在保存数据前才会执行一次。
     *
     * @param content The revised content. It may be shorter than the previous string, it may
     * be longer, or it may be completely different.
     * @return Whether to allow this modification. If it returns {@code true}, it means that
     * this modification is allowed and will not prevent the user from entering the current
     * content; if it returns {@code false}, it means that this modification is not allowed,
     * and it will prevent the user from entering the current content.
     */
    boolean onChanged(@NonNull T content);
}
