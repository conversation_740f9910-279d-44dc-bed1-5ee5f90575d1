<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/01/20                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <layer-list>
            <!-- Shadow -->
            <item android:top="2dp" android:left="2dp" android:bottom="2dp" android:right="2dp">
                <shape>
                    <padding android:bottom="1dp" />
                    <solid android:color="@color/main_highlight_button_shadow" />
                    <corners android:radius="12dp" />
                </shape>
            </item>

            <!-- Background -->
            <item android:top="2dp" android:left="2dp" android:bottom="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="@color/main_highlight_button_clicked" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <layer-list>
            <!-- Shadow -->
            <item android:top="2dp" android:left="2dp" android:bottom="2dp" android:right="2dp">
                <shape>
                    <padding android:bottom="1dp" />
                    <solid android:color="@color/main_highlight_button_shadow" />
                    <corners android:radius="12dp" />
                </shape>
            </item>

            <!-- Background -->
            <item android:top="2dp" android:left="2dp" android:bottom="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="@color/main_highlight_button" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>