package com.visionpay.opt.pax.emv.online.opt.utils;

import retrofit2.Response;

public class ApiException extends Exception {

    private Response<?> response;

    public ApiException(Response<?> response, Throwable throwable){
        super(throwable);
        this.response = response;
    }

    public ApiException(Throwable throwable){
        super(throwable);
    }

    public ApiException(Response<?> response, String message, Throwable throwable){
        super(message, throwable);
        this.response = response;
    }

    public ApiException(Response<?> response, String message){
        super(message);
        this.response = response;
    }

    public int getResponseCode(){
        if(response != null)
            return response.code();
        else return 0;
    }

    @Override
    public String toString(){
        return "Response Code: " + getResponseCode() + "\n" + super.toString();
    }
}
