/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/12                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;

import com.visionpay.opt.pax.config.datastore.PrefDataStore;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.ConfigUIKeyConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.bizentity.db.helper.EFTAidDbHelper;
import com.pax.bizentity.entity.clss.eft.EFTAidBean;
import com.pax.commonlib.utils.ResourceUtil;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.PrefCategoryBuilder;
import com.pax.preflib.builder.RouterPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;
import com.sankuai.waimai.router.common.DefaultUriRequest;

import java.util.List;

/**
 * EFT contactless param list page
 *
 * Settings -> Param -> EFT Kernel Param
 */
@RouterService(interfaces = EmvConfigBaseFragment.class, key = EmvRouterConst.CONFIG_PARAM_CLSS_EFT)
public class EmvEftParamListFragment extends EmvConfigBaseFragment {
    @NonNull
    @Override
    public String getFragmentTitle() {
        return ResourceUtil.getString(R.string.config_param_contactless_eft);
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new PrefDataStore());
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // AID
            List<EFTAidBean> aidBeanList = EFTAidDbHelper.getInstance().loadAll();
            screen.addPreference(PrefCategoryBuilder
                    .newInstance(context, ConfigUIKeyConst.EMV_CONTACTLESS_EFT_AID_CATEGORY, R.string.config_param_aid)
                    .addPreferences(aidBeanList, source -> RouterPrefBuilder
                            .newInstance(context, source.getAid(), source.getAid())
                            .setRequest(new DefaultUriRequest(context, EmvRouterConst.CONFIG_PARAM_CLSS_EFT_AID)
                                    .putExtra(BundleFieldConst.EMV_PARAM, source.getAid()))
                            .setSummary(source.getAppName())
                            .build())
                    .setIconSpaceReserved(false)
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
