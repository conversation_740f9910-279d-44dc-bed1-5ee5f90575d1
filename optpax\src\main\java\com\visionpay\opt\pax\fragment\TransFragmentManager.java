/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/21                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.fragment;

import android.os.Bundle;
import android.view.View;
import androidx.annotation.AnyThread;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.pax.commonlib.utils.LogUtils;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.entity.TransStatus;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Trans Fragment Manager.
 *
 * TransFragmentManager 用来管理 Fragment 的添加与删除，以及通知 Fragment 更新 Status。
 * 每一个 Fragment 都有对应的 Tag，通过 Tag 来指定是哪个 Fragment 需要添加和删除。
 */
@AnyThread
public class TransFragmentManager {
    private static final String TAG = "TransFragmentManager";

    public static final String SEARCH_CARD_PROMPT = "SEARCH_CARD_PROMPT";
    public static final String CONFIRM_AID = "CONFIRM_AID";
    public static final String EMV_TAG = "EMV_TAG";

    @Nullable
    private FragmentManager manager;
    private int containerId;
    private final Map<String, BaseTransFragment> transFragmentMap = new HashMap<>();
    private TransStatus transStatus;

    private final FragmentManager.FragmentLifecycleCallbacks lifecycleCallbacks = new FragmentManager.FragmentLifecycleCallbacks() {
        @Override
        public void onFragmentViewCreated(@NonNull FragmentManager fm, @NonNull Fragment f,
                @NonNull View v, @Nullable Bundle savedInstanceState) {
            super.onFragmentViewCreated(fm, f, v, savedInstanceState);
            if (f instanceof BaseTransFragment) {
                LogUtils.d(TAG, "++++++++++++++ " + f.getClass().getSimpleName() + " onViewCreated");
                BaseTransFragment fragment = (BaseTransFragment) f;
                transFragmentMap.put(fragment.getFragmentTag(), fragment);
                fragment.updateStatus(transStatus);
            }
        }

        @Override
        public void onFragmentViewDestroyed(@NonNull FragmentManager fm, @NonNull Fragment f) {
            super.onFragmentViewDestroyed(fm, f);
            if (f instanceof BaseTransFragment) {
                LogUtils.d(TAG, "--- " + f.getClass().getSimpleName() + " onViewDestroyed");
                BaseTransFragment fragment = (BaseTransFragment) f;
                transFragmentMap.remove(fragment.getFragmentTag());
            }
        }
    };

    private TransFragmentManager() {
        // do nothing
    }

    private static class Holder {
        private static final TransFragmentManager INSTANCE = new TransFragmentManager();
    }

    public static TransFragmentManager getInstance() {
        return Holder.INSTANCE;
    }

    public void init(@NonNull FragmentManager manager, @IdRes int containerId) {
        this.manager = manager;
        this.containerId = containerId;

        manager.registerFragmentLifecycleCallbacks(lifecycleCallbacks, false);
    }

    @Nullable
    private BaseTransFragment find(String tag) {
        BaseTransFragment fragment = transFragmentMap.get(tag);
        if (fragment == null) {
            switch (tag) {
                case SEARCH_CARD_PROMPT:
                    fragment = new TransPromptFragment();
                    break;
                case CONFIRM_AID:
                    fragment = new TransConfirmFragment();
                    break;
                case EMV_TAG:
                    fragment = new TransEmvResultFragment();
                    break;
                default:
                    fragment = null;
                    break;
            }
        }
        return fragment;
    }

    public TransFragmentManager show(String tag) {
        if (tag == null || tag.isEmpty() || manager == null) {
            return this;
        }
        try {
            BaseTransFragment fragment = find(tag);
            if (fragment != null) {
                App.getApp().runOnUiThread(() -> {
                    manager.beginTransaction()
                            .add(containerId, fragment, tag)
                            .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN)
                            .commitNow();
                });
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "show " + tag + " fragment failed", e);
        }
        return this;
    }

    public TransFragmentManager hide(String tag) {
        if (tag == null || tag.isEmpty() || manager == null) {
            return this;
        }
        try {
            BaseTransFragment fragment = transFragmentMap.get(tag);
            if (fragment == null)  {
                List<Fragment> fragmentList = manager.getFragments();
                for (Fragment f : fragmentList) {
                    if (f instanceof BaseTransFragment) {
                        BaseTransFragment temp = (BaseTransFragment) f;
                        if (temp.getFragmentTag().equals(tag)) {
                            fragment = temp;
                            break;
                        }
                    }
                }
            }
            BaseTransFragment finalFragment = fragment;
            if (finalFragment != null) {
                App.getApp().runOnUiThread(() -> manager.beginTransaction()
                        .remove(finalFragment)
                        .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)
                        .commitNowAllowingStateLoss());
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "hide " + tag + " fragment failed", e);
        }
        return this;
    }

    public TransFragmentManager hideAll() {
        if (manager == null) {
            return this;
        }
        try {
            FragmentTransaction transaction = manager.beginTransaction();
            for (Fragment fragment : manager.getFragments()) {
                if (fragment instanceof BaseTransFragment) {
                    transaction.remove(fragment);
                }
            }
            App.getApp().runOnUiThread(() -> transaction
                    .setTransition(FragmentTransaction.TRANSIT_FRAGMENT_CLOSE)
                    .commitNowAllowingStateLoss());
            transFragmentMap.clear();
        } catch (Exception e) {
            LogUtils.e(TAG, "hide all fragment failed", e);
        }
        return this;
    }

    public void updateStatus(TransStatus status) {
        this.transStatus = status;
        if (manager != null) {
            try {
                for (Map.Entry<String, BaseTransFragment> entry : transFragmentMap.entrySet()) {
                    BaseTransFragment fragment = entry.getValue();
                    if (fragment != null) {
                        fragment.updateStatus(status);
                    }
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "update trans status failed", e);
            }
        }
    }

    public void clear() {
        if (manager != null) {
            manager.unregisterFragmentLifecycleCallbacks(lifecycleCallbacks);
            manager = null;
        }
        transFragmentMap.clear();
    }
}
