/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/24                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.viewmodel;

import android.content.DialogInterface;
import androidx.lifecycle.ViewModel;
import com.visionpay.opt.pax.dialog.SingleSelectionDialog;
import java.util.List;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/24
 */
public class SingleSelectionDialogViewModel extends ViewModel {
    private boolean isRestore = false;
    private String title;
    private List<String> itemList;
    private int checkedItem;
    private DialogInterface.OnClickListener onItemClickListener;
    private String positiveButtonText;
    private Integer positiveButtonIconRes;
    private SingleSelectionDialog.OnClickCallback positiveButtonClickListener;
    private String negativeButtonText;
    private Integer negativeButtonIconRes;
    private SingleSelectionDialog.OnClickCallback negativeButtonClickListener;

    public boolean isRestore() {
        return isRestore;
    }

    public void setRestore(boolean restore) {
        isRestore = restore;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<String> getItemList() {
        return itemList;
    }

    public void setItemList(List<String> itemList) {
        this.itemList = itemList;
    }

    public int getCheckedItem() {
        return checkedItem;
    }

    public void setCheckedItem(int checkedItem) {
        this.checkedItem = checkedItem;
    }

    public DialogInterface.OnClickListener getOnItemClickListener() {
        return onItemClickListener;
    }

    public void setOnItemClickListener(DialogInterface.OnClickListener onClickListener) {
        this.onItemClickListener = onClickListener;
    }

    public String getPositiveButtonText() {
        return positiveButtonText;
    }

    public void setPositiveButtonText(String positiveButtonText) {
        this.positiveButtonText = positiveButtonText;
    }

    public Integer getPositiveButtonIconRes() {
        return positiveButtonIconRes;
    }

    public void setPositiveButtonIconRes(Integer positiveButtonIconRes) {
        this.positiveButtonIconRes = positiveButtonIconRes;
    }

    public SingleSelectionDialog.OnClickCallback getPositiveButtonClickListener() {
        return positiveButtonClickListener;
    }

    public void setPositiveButtonClickListener(
            SingleSelectionDialog.OnClickCallback positiveButtonClickListener) {
        this.positiveButtonClickListener = positiveButtonClickListener;
    }

    public String getNegativeButtonText() {
        return negativeButtonText;
    }

    public void setNegativeButtonText(String negativeButtonText) {
        this.negativeButtonText = negativeButtonText;
    }

    public Integer getNegativeButtonIconRes() {
        return negativeButtonIconRes;
    }

    public void setNegativeButtonIconRes(Integer negativeButtonIconRes) {
        this.negativeButtonIconRes = negativeButtonIconRes;
    }

    public SingleSelectionDialog.OnClickCallback getNegativeButtonClickListener() {
        return negativeButtonClickListener;
    }

    public void setNegativeButtonClickListener(
            SingleSelectionDialog.OnClickCallback negativeButtonClickListener) {
        this.negativeButtonClickListener = negativeButtonClickListener;
    }
}
