package com.visionpay.opt.pax.activity;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.widget.TextView;

import com.pax.commonlib.application.ActivityStack;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.visionpay.opt.pax.message.BroadcastMesseger;

@RouterUri(path = EmvRouterConst.SUCCESS_INTERNAL_CARD)
public class SuccessInternalActivity extends BaseActivity {

    private int FINISH_DELAY = 10000;

    private final Handler mFinishHandler = new Handler(Looper.myLooper());

    private final Runnable mAutoFinishRunnable = () -> finishScreen();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_success_internal);

        /*Intent intent = getIntent();
        if (intent != null) {
            String title = intent.getStringExtra(BundleFieldConst.TITLESUCESS);
            if(title != null && !title.isEmpty()) {
                ((TextView) findViewById(R.id.txtTitle)).setText(title);
                ((TextView) findViewById(R.id.txtText)).setText("");
            }
        }*/

        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        FINISH_DELAY = configParamService.getInt(ConfigKeyConstant.COMPLETION_SCREEN_DELAY, 10000);

        mFinishHandler.postDelayed(mAutoFinishRunnable, FINISH_DELAY/2);
    }

    public void finishScreen(){
        ActivityStack.getInstance().popAll();
        if(!BroadcastMesseger.getInstance().isInternalMessage()) {
            Intent intent = new Intent(this, MainActivity.class);
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK); // equal to Intent.FLAG_ACTIVITY_CLEAR_TASK which is only available from API level 11
            this.startActivity(intent);
        }
    }

    @Override
    protected void onDestroy() {
        mFinishHandler.removeCallbacks(mAutoFinishRunnable);
        super.onDestroy();
    }
}