package com.visionpay.opt.pax.emv.online;

public interface IOnlineService {

    IOnlineService setErrorCallback(ErrorCallback errorCallback);

    IOnlineService setRequestCompleteCallback(RequestCompleteCallback requestCompleteCallback);

    IOnlineService startCapture(String kioskId, String terminalId, long internalID, long stan, long amount);

    IOnlineService startReversal(String kioskId, String terminalId, long internalID, long stan, long amount);

    interface ErrorCallback {
        void onSend(String messageError);
    }

    interface RequestCompleteCallback {
        void onComplete(boolean isApproved, String STAN, String gatewayResponse, String gatewayResponseCode);
    }
}
