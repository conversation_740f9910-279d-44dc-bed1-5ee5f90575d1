package com.visionpay.opt.pax.entity;

public enum ProcessType implements BaseEnum<Integer> {
    Payment(0),
    Capture(1),
    Reversal(2),
    Magnetic(3),

    Receipt(4),
    Cancel(99);

    private Integer value;

    ProcessType(Integer value){
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    public static ProcessType fromValue(Integer value) {
        for(ProcessType v : values()){
            if( v.value.equals(value)){
                return v;
            }
        }
        return null;
    }
}
