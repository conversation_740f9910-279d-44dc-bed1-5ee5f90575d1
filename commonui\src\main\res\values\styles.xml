<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="commonuiAlertDialog" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowBackground">@color/commonui_background</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.4</item>
    </style>

    <style name="commonuiDialogButton" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="android:layout_width">@dimen/commonui_alert_width_small</item>
        <item name="android:layout_height">@dimen/commonui_button_height</item>
        <item name="android:textSize">@dimen/commonui_font_button</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
        <item name="android:textColor">@color/commonui_primary_text_light</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
    </style>
    <style name="commonuiPopupDialog" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:windowBackground">@color/commonui_background</item>
        <item name="android:textColor">@color/commonui_primary_text_light</item>
    </style>
    <style name="commonuiCustomKeyboardView" parent="@android:style/Widget.KeyboardView">
        <item name="android:background">@color/commonui_background</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:keyBackground">@drawable/commonui_btn_bg_dark</item>
        <item name="android:keyTextColor">@color/commonui_primary_text_light</item>
    </style>

    <style name="commonuiGuideHelperDialog" parent="Theme.AppCompat.DayNight.Dialog">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowBackground">@color/commonui_dark_background</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
</resources>