/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/02/23                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.activity;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.fragment.app.FragmentManager;
import com.google.android.material.appbar.MaterialToolbar;
import com.pax.commonlib.utils.LogUtils;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.config.fragment.ConfigAboutFragment;
import com.visionpay.opt.pax.config.fragment.ConfigMainFragment;
import com.visionpay.opt.pax.config.fragment.EmvConfigBaseFragment;
import com.visionpay.opt.pax.config.fragment.EmvParamBaseFragment;
import com.visionpay.opt.pax.consts.BundleFieldConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.preflib.activity.ConfigBaseActivity;
import com.sankuai.waimai.router.Router;
import com.sankuai.waimai.router.annotation.RouterUri;

@RouterUri(path = {
        EmvRouterConst.CONFIG_MAIN,
        EmvRouterConst.CONFIG_COMMON,
        EmvRouterConst.CONFIG_PARAM,
        EmvRouterConst.CONFIG_PARAM_CONTACT,
        EmvRouterConst.CONFIG_PARAM_CONTACT_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_AMEX,
        EmvRouterConst.CONFIG_PARAM_CLSS_AMEX_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_AMEX_DRL,
        EmvRouterConst.CONFIG_PARAM_CLSS_DPAS,
        EmvRouterConst.CONFIG_PARAM_CLSS_DPAS_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_EFT,
        EmvRouterConst.CONFIG_PARAM_CLSS_EFT_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_JCB,
        EmvRouterConst.CONFIG_PARAM_CLSS_JCB_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_MC,
        EmvRouterConst.CONFIG_PARAM_CLSS_MC_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_MIR,
        EmvRouterConst.CONFIG_PARAM_CLSS_MIR_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE,
        EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE_PROGRAM_ID,
        EmvRouterConst.CONFIG_PARAM_CLSS_PBOC,
        EmvRouterConst.CONFIG_PARAM_CLSS_PBOC_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_PURE,
        EmvRouterConst.CONFIG_PARAM_CLSS_PURE_AID,
        EmvRouterConst.CONFIG_PARAM_CLSS_RUPAY,
        EmvRouterConst.CONFIG_PARAM_CLSS_RUPAY_AID,
        EmvRouterConst.CONFIG_ABOUT
})
public class ConfigActivity extends ConfigBaseActivity {
    private String currentPath;
    private FrameLayout menuView;
    private FrameLayout containerView;

    @Override
    protected Drawable getCustomHomeAsUpDrawable() {
        return AppCompatResources.getDrawable(this, R.drawable.ic_back);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_config;
    }

    @Override
    protected void loadParam() {
        // do nothing
    }

    @NonNull
    @Override
    protected MaterialToolbar getToolbar() {
        MaterialToolbar toolbar = findViewById(R.id.config_toolbar);
        toolbar.setTitle("Config");
        return toolbar;
    }

    @Override
    protected int getContainerId() {
        return R.id.config_container;
    }

    @Override
    protected void initViews() {
        menuView = findViewById(R.id.config_menu);
        containerView = findViewById(getContainerId());

        super.initViews();
    }

    @Override
    protected void setFragment(@Nullable String path, @Nullable Intent intent) {
        if (path != null) {
            switch (path) {
                case EmvRouterConst.CONFIG_COMMON:
                case EmvRouterConst.CONFIG_PARAM:
                    if (menuView != null && menuView.getVisibility() == View.VISIBLE) {
                        // 防止重复点击，重复创建 Fragment
                        if (path.equals(currentPath)) {
                            LogUtils.d(TAG, "Touch menu again");
                            return;
                        }
                        // 防止旋转屏幕或者调整深色模式以后，销毁重建 UI 时，返回栈没有清空导致的错乱
                        clearBackStack();
                        if (placeContentFragment(path, false)) {
                            currentPath = path; // 只有当成功显示了 Fragment 时，才设置 currentPath
                        }
                    } else {
                        placeContentFragment(path);
                    }
                    break;
                case EmvRouterConst.CONFIG_ABOUT:
                    placeAboutFragment();
                    break;
                case EmvRouterConst.CONFIG_PARAM_CONTACT:
                case EmvRouterConst.CONFIG_PARAM_CLSS_AMEX:
                case EmvRouterConst.CONFIG_PARAM_CLSS_DPAS:
                case EmvRouterConst.CONFIG_PARAM_CLSS_EFT:
                case EmvRouterConst.CONFIG_PARAM_CLSS_JCB:
                case EmvRouterConst.CONFIG_PARAM_CLSS_MC:
                case EmvRouterConst.CONFIG_PARAM_CLSS_MIR:
                case EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE:
                case EmvRouterConst.CONFIG_PARAM_CLSS_PBOC:
                case EmvRouterConst.CONFIG_PARAM_CLSS_PURE:
                case EmvRouterConst.CONFIG_PARAM_CLSS_RUPAY:
                    placeContentFragment(path);
                    break;
                case EmvRouterConst.CONFIG_PARAM_CONTACT_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_AMEX_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_AMEX_DRL:
                case EmvRouterConst.CONFIG_PARAM_CLSS_DPAS_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_EFT_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_JCB_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_MC_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_MIR_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE_PROGRAM_ID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_PBOC_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_PURE_AID:
                case EmvRouterConst.CONFIG_PARAM_CLSS_RUPAY_AID:
                    String param = "";
                    if (intent != null) {
                        param = intent.getStringExtra(BundleFieldConst.EMV_PARAM);
                    }
                    placeEmvContentFragment(path, param);
                    break;
                case EmvRouterConst.CONFIG_MAIN:
                default:
                    placeMainFragment();
                    break;
            }
        } else {
            placeMainFragment();
        }
    }

    private boolean placeMainFragment() {
        if (menuView != null && menuView.getVisibility() == View.VISIBLE) {
            LogUtils.d(TAG, "Place menu");
            if (containerView != null) {
                containerView.setVisibility(View.INVISIBLE);
            }
            clearBackStack();
            ConfigMainFragment fragment = new ConfigMainFragment();
            placeFragment(R.id.config_menu, fragment, fragment.getFragmentTitle(), false);
            return true;
        } else {
            return placeContentFragment(EmvRouterConst.CONFIG_MAIN);
        }
    }

    private void placeAboutFragment() {
        if (containerView != null) {
            containerView.setVisibility(View.VISIBLE);
        }
        if (menuView != null && menuView.getVisibility() == View.VISIBLE) {
            // 防止重复点击，重复创建 Fragment
            if (EmvRouterConst.CONFIG_ABOUT.equals(currentPath)) {
                LogUtils.d(TAG, "Touch menu again");
                return;
            }
            // 防止旋转屏幕或者调整深色模式以后，销毁重建 UI 时，返回栈没有清空导致的错乱
            clearBackStack();
            placeFragment(getContainerId(), new ConfigAboutFragment(),
                    "About", false);
            currentPath = EmvRouterConst.CONFIG_ABOUT;
        } else {
            placeFragment(getContainerId(), new ConfigAboutFragment(),
                    "About", true);
        }
    }

    private boolean placeContentFragment(String path) {
        return placeContentFragment(path, true);
    }

    private boolean placeContentFragment(String path, boolean addToBackStack) {
        if (containerView != null) {
            containerView.setVisibility(View.VISIBLE);
        }
        EmvConfigBaseFragment fragment = null;
        try {
            fragment = Router.getService(EmvConfigBaseFragment.class, path);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        if (fragment == null) {
            return false;
        }
        placeFragment(getContainerId(), fragment, fragment.getFragmentTitle(), addToBackStack);
        return true;
    }

    private boolean placeEmvContentFragment(String path, String param) {
        return placeEmvContentFragment(path, param, true);
    }

    private boolean placeEmvContentFragment(String path, String param, boolean addToBackStack) {
        if (containerView != null) {
            containerView.setVisibility(View.VISIBLE);
        }
        EmvParamBaseFragment fragment = null;
        try {
            fragment = Router.getService(EmvParamBaseFragment.class, path);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        if (fragment == null) {
            return false;
        }
        fragment.setParam(param);
        placeFragment(getContainerId(), fragment, fragment.getFragmentTitle(), addToBackStack);
        return true;
    }

    private void clearBackStack() {
        FragmentManager manager = getSupportFragmentManager();
        while (manager.getBackStackEntryCount() != 0) {
            LogUtils.d(TAG, "pop back stack");
            manager.popBackStackImmediate();
        }
    }
}