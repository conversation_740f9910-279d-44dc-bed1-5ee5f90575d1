/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/26                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.factory.impl;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.SeekBar;
import androidx.annotation.Nullable;
import com.pax.preflib.R;
import com.pax.preflib.builder.pref.BaseSeekBarDialogPreference;
import com.pax.preflib.builder.pref.IDialogPreference;

/**
 * Custom SeekBar dialog Preference.
 * <br>
 * SeekBarPreference is provided in Jetpack Preference, but there are many limitations. For
 * example, the maximum and minimum values can only be set as integers, and the displayed value
 * size cannot be customized. The most important problem is that SeekBarPreference cannot be
 * displayed as a dialog.
 * <br>
 * SeekBarDialogPreference is implemented based on DialogPreference, adds many new functions, and
 * places SeekBar in a dialog.
 * <br><br>
 * Jetpack Preference 中提供了 SeekBarPreference，但是存在着诸多限制。比如说最大最小值都只能设置为整数，
 * 显示的值大小不能自定义格式等。最大的问题是，SeekBarPreference 不能作为弹窗显示。
 * <br>
 * SeekBarDialogPreference基于DialogPreference实现，新增了许多功能，并且将SeekBar放置在一个对话框中。
 */
class SeekBarDialogPreference extends BaseSeekBarDialogPreference implements
        IDialogPreference<SeekBarDialog> {

    private float min;
    private float max;
    private float increment;
    private String format;

    private float defValue = 0f;
    private float newValue = 0f;

    private SeekBar.OnSeekBarChangeListener listener;

    public SeekBarDialogPreference(Context context,
            AttributeSet attrs,
            int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        // Get parameter 获取参数
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.CustomSeekBarPreference, defStyleAttr, defStyleRes);
        min = array.getFloat(R.styleable.CustomSeekBarPreference_minValue, 0f);
        max = array.getFloat(R.styleable.CustomSeekBarPreference_maxValue, 100f);
        increment = array.getFloat(R.styleable.CustomSeekBarPreference_increment, 1f);
        format = array.getString(R.styleable.CustomSeekBarPreference_format);
        if (format == null) {
            format = "%.2f";
        }
        array.recycle();
    }

    public SeekBarDialogPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public SeekBarDialogPreference(Context context, AttributeSet attrs) {
        this(context, attrs, R.attr.dialogPreferenceStyle);
    }

    public SeekBarDialogPreference(Context context) {
        this(context, null);
    }

    @Override
    protected Object onGetDefaultValue(TypedArray a, int index) {
        if (a == null) {
            return null;
        }
        defValue = a.getFloat(index, 0f);
        return defValue;
    }

    @Override
    protected void onSetInitialValue(@Nullable Object defaultValue) {
        float value;
        if (defaultValue instanceof Number) {
            value = (float) defaultValue;
        } else {
            value = defValue;
        }
        newValue = getPersistedFloat(value);
    }

    @Override
    public float getMin() {
        return min;
    }

    @Override
    public void setMin(float min) {
        this.min = min;
    }

    @Override
    public float getMax() {
        return max;
    }

    @Override
    public void setMax(float max) {
        this.max = max;
    }

    @Override
    public float getIncrement() {
        return increment;
    }

    @Override
    public void setIncrement(float increment) {
        this.increment = increment;
    }

    @Override
    public String getFormat() {
        return format;
    }

    @Override
    public void setFormat(String format) {
        this.format = format;
    }

    @Override
    public void setValue(float value) {
        newValue = value;
        persistFloat(value);
        notifyChanged();
    }

    @Override
    public float getValue() {
        return newValue;
    }

    @Override
    public void setListener(SeekBar.OnSeekBarChangeListener listener) {
        this.listener = listener;
    }

    @Override
    public SeekBarDialog createDialog(String key) {
        return SeekBarDialog.newInstance(key)
                .setMin(min)
                .setMax(max)
                .setFormat(format)
                .setIncrement(increment)
                .setListener(listener);
    }
}
