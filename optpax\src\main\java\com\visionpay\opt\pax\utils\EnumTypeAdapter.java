package com.visionpay.opt.pax.utils;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.visionpay.opt.pax.entity.BaseEnum;

import java.io.IOException;

public class EnumTypeAdapter<T extends Enum<T>> extends TypeAdapter<T> {
    @Override
    public void write(JsonWriter out, T value) throws IOException {
        if(value instanceof BaseEnum<?>) {
            Object enumValue = ((BaseEnum<?>) value).getValue();
            if(enumValue instanceof Integer)
                out.value((Integer)enumValue);
            if(enumValue instanceof String)
                out.value((String)enumValue);
        }
        else
            out.jsonValue(value.toString());
    }
    @Override
    public T read(JsonReader in) throws IOException {
        return null;
    }
}