package com.visionpay.opt.pax.emv.online.interchange.messages;

import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageSubtype;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageType;

public class FinancialMessage extends Message {
    private final byte[] mSessionID;

    @Override
    protected byte[] internalGetMessage() {
        byte[] message = super.internalGetMessage();
        byte[] ret = new byte[message.length + mSessionID.length];
        System.arraycopy(message,0, ret, 0, message.length);
        System.arraycopy(mSessionID,0, ret, message.length, mSessionID.length);
        return ret;
    }

    public FinancialMessage(InterchangeMessageType type, InterchangeMessageSubtype subType, long refNo, String terminalID, byte[] sessionID){
        super(InterchangeMessageType.IMT_TRX, subType, refNo, terminalID);
        this.mSessionID = sessionID;
    }
}