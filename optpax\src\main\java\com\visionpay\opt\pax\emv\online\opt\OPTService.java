package com.visionpay.opt.pax.emv.online.opt;


import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.transaction.ITransactionService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.emv.online.IOnlineService;
import com.visionpay.opt.pax.emv.online.opt.apiclient.IApiClient;
import com.visionpay.opt.pax.emv.online.opt.messages.requests.AccountCardAuthenticateRequest;
import com.visionpay.opt.pax.emv.online.opt.messages.requests.AccountCardAuthoriseRequest;
import com.visionpay.opt.pax.emv.online.opt.messages.requests.AccountCardTransactionRequest;
import com.visionpay.opt.pax.emv.online.opt.messages.responses.AccountCardTransactionResponse;
import com.visionpay.opt.pax.emv.online.opt.messages.responses.BaseResponse;
import com.visionpay.opt.pax.emv.online.opt.utils.ApiException;
import com.visionpay.opt.pax.emv.online.opt.utils.ApiServiceHelper;

public class OPTService implements IOnlineService {

    private ErrorCallback errorCallback = null;
    private RequestCompleteCallback requestCompleteCallback = null;

    @Override
    public OPTService setErrorCallback(ErrorCallback errorCallback) {
        this.errorCallback = errorCallback;
        return this;
    }

    @Override
    public OPTService setRequestCompleteCallback(RequestCompleteCallback requestCompleteCallback) {
        this.requestCompleteCallback = requestCompleteCallback;
        return this;
    }

    public static AccountCardTransactionResponse authorizeCard(String kioskId,
                                                  long transactionReference, String pan, long amount, String track2, String pin,
                                                  String expiryDate, String terminalId) throws ApiException {
        IApiClient client = Router.getService(IApiClient.class);
        IOPTApiService apiService = client.getOPTApiService();

        String token = Logon(apiService, kioskId, terminalId);

        AccountCardAuthoriseRequest authoriseRequest = new AccountCardAuthoriseRequest();
        authoriseRequest.setTerminalID(terminalId);
        authoriseRequest.setAmount(amount);
        authoriseRequest.setExpiryDate(expiryDate);
        authoriseRequest.setPan(pan);
        authoriseRequest.setPin(pin);
        authoriseRequest.setTrack2(track2);
        authoriseRequest.setTransactionReferece(transactionReference);

        AccountCardTransactionResponse response = ApiServiceHelper.executeAPI(apiService.authorise(authoriseRequest, token));
        return response;
    }

    @Override
    public OPTService startCapture(String kioskId, String terminalId, long transactionReference, long stan, long amount){
        App.getApp().runInBackground(() -> {
            try {
                AccountCardTransactionResponse captureResponse = captureCard(kioskId, transactionReference, stan, amount, terminalId);
                if(captureResponse != null){
                    ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
                    transactionService.remove(transactionReference);
                    if(requestCompleteCallback != null)
                        requestCompleteCallback.onComplete(captureResponse.isApproved(),
                                captureResponse.getSTAN() + "", captureResponse.getGatwayResponse(),
                                captureResponse.getHostResponse());
                }
                else {
                    if(this.errorCallback != null)
                        errorCallback.onSend("");
                }

            } catch (Exception e) {
                e.printStackTrace();
                if(this.errorCallback != null)
                    errorCallback.onSend(e.getLocalizedMessage());
            }
        });
        return this;
    }

    @Override
    public OPTService startReversal(String kioskId, String terminalId, long transactionReference, long stan, long amount){
        App.getApp().runInBackground(() -> {
            try {
                AccountCardTransactionResponse captureResponse = reverseCard(kioskId, transactionReference, stan, amount, terminalId);
                if(captureResponse != null){
                    ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
                    transactionService.remove(transactionReference);
                    if(requestCompleteCallback != null)
                        requestCompleteCallback.onComplete(captureResponse.isApproved(),
                                captureResponse.getSTAN() + "", captureResponse.getGatwayResponse(),
                                captureResponse.getHostResponse());
                }
                else {
                    if(this.errorCallback != null)
                        errorCallback.onSend("");
                }

            } catch (Exception e) {
                e.printStackTrace();
                if(this.errorCallback != null)
                    errorCallback.onSend(e.getLocalizedMessage());
            }
        });
        return this;
    }

    private static String Logon(IOPTApiService apiService, String kioskId, String terminalId) throws ApiException {
        AccountCardAuthenticateRequest authenticateRequest = new AccountCardAuthenticateRequest();
        authenticateRequest.setKioskId(kioskId);
        authenticateRequest.setTerminalId(terminalId);

        BaseResponse authResponse = ApiServiceHelper.executeAPI(apiService.authenticate(authenticateRequest));

        return "Bearer " + authResponse.getMessage();
    }

    private static AccountCardTransactionResponse captureCard(String kioskId, long transactionReference,
                                                              long stan, long amount, String terminalId) throws ApiException {
        IApiClient client = Router.getService(IApiClient.class);
        IOPTApiService apiService = client.getOPTApiService();

        String token = Logon(apiService, kioskId, terminalId);

        AccountCardTransactionRequest transactionRequest = new AccountCardTransactionRequest();
        transactionRequest.setTerminalID(terminalId);
        transactionRequest.setAmount(amount);
        transactionRequest.setTransactionReferece(transactionReference);
        transactionRequest.setSTAN(stan);

        AccountCardTransactionResponse response = ApiServiceHelper.executeAPI(apiService.capture(transactionRequest, token));
        return response;
    }

    private static AccountCardTransactionResponse reverseCard(String kioskId, long transactionReference,
                                                              long stan, long amount, String terminalId) throws ApiException {
        IApiClient client = Router.getService(IApiClient.class);
        IOPTApiService apiService = client.getOPTApiService();

        String token = Logon(apiService, kioskId, terminalId);

        AccountCardTransactionRequest transactionRequest = new AccountCardTransactionRequest();
        transactionRequest.setTerminalID(terminalId);
        transactionRequest.setAmount(amount);
        transactionRequest.setTransactionReferece(transactionReference);
        transactionRequest.setSTAN(stan);

        AccountCardTransactionResponse response = ApiServiceHelper.executeAPI(apiService.reverse(transactionRequest, token));
        return response;
    }
}
