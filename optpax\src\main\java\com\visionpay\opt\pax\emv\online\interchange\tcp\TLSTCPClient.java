package com.visionpay.opt.pax.emv.online.interchange.tcp;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

public class TLSTCPClient extends BaseTCPClient implements AutoCloseable {
    //private mbedtls_net_context server_fd;

    private static final String[] protocols = new String[] {"TLSv1.2"};
    private static final String[] cipher_suites = new String[] {"TLS_AES_128_GCM_SHA256"};

    protected SSLSocket ssl;

    public TLSTCPClient(String ahostname, String aip, String aport){
        super(ahostname, aip, aport);
    }

    public boolean open() throws IOException {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[] { TLSCert.getCacert() };
                }

                @Override
                public void checkClientTrusted(X509Certificate[] certs, String authType) {}

                @Override
                public void checkServerTrusted(X509Certificate[] certs, String authType) {}
            }
            };

            SSLContext sc = SSLContext.getInstance(protocols[0]);
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            ssl = (SSLSocket) sc.getSocketFactory()
                    .createSocket(ip, Integer.parseInt(port));
            ssl.setSoTimeout(30000);
            //ssl.setEnabledProtocols(protocols);
            //ssl.setEnabledCipherSuites(cipher_suites);
            return true;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public void close() throws IOException {
        if (ssl != null && !ssl.isClosed()) {
            ssl.close();
        }
    }

    public boolean send(String s) throws IOException {
        return send(s.getBytes());
    }

    public boolean send(byte[] buffer) throws IOException {
        OutputStream os = new BufferedOutputStream(ssl.getOutputStream());
        os.write(buffer);
        os.flush();
        return true;
    }

    protected byte[] tryReceive(int timeout) throws IOException{
        byte[] buffer;
        try {
            Thread.sleep(timeout);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        InputStream is = new BufferedInputStream(ssl.getInputStream());

        byte[] data = new byte[2048];
        int len = is.read(data);
        if (len <= 0) {
            throw new IOException("no data received");
        }

        buffer = new byte[len];
        System.arraycopy(data,0, buffer, 0, len);

        return buffer;
    }
}

