/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                Author	               Action
 * 20210516 	        xieYb                  Create
 * ===========================================================================================
 *
 */
package com.pax.commonui.toast;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.StringRes;
import androidx.annotation.UiThread;
import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonui.R;

/**
 * The type Toast utils.
 */
public class ToastUtils {
    private static final String TAG = "ToastUtils";

    /**
     * old message
     */
    private static String oldMsg;
    /**
     * Toast object
     */
    private static Toast toast = null;
    /**
     * Current show toast request time
     */
    private static long currentRequestTime = 0;
    /**
     * System default toast show duration time
     */
    private static final int TOAST_DURATION = 2000;

    private ToastUtils() {
        //do nothing
    }

    /**
     * Show message.
     *
     * @param strId the str id
     */
    public static void showMessage(@StringRes int strId) {
        showMessage(BaseApplication.getAppContext(), BaseApplication.getAppContext().getString(strId));
    }

    /**
     * Show message.
     *
     * @param message the message
     */
    public static void showMessage(String message) {
        showMessage(BaseApplication.getAppContext(), message);
    }

    /**
     * Show message.
     *
     * @param context the context
     * @param message the message
     */
    @UiThread
    public static void showMessage(Context context, String message) {
        BaseApplication.getAppContext().runOnUiThread(() -> {
            LayoutInflater inflate = LayoutInflater.from(context);
            View view = inflate.inflate(R.layout.commonui_toast_layout, null);
            TextView textView = (TextView) view.findViewById(R.id.commonui_message);

            if (toast != null && oldMsg != null) {
                if (!oldMsg.equals(message)) {
                    // Toast is displaying, but new content needs to be displayed
                    currentRequestTime = System.currentTimeMillis();
                    final long thisRequestTime = currentRequestTime;

                    LogUtils.d(TAG, "Toast is displaying, but new content needs to be displayed");

                    oldMsg = message;
                    textView.setText(message);
                    toast.setView(view);
                    toast.show();
                    BaseApplication.getAppContext().runOnUiThreadDelay(() -> {
                        LogUtils.d(TAG, "Current request: " + currentRequestTime + "This "
                                + "request: " + thisRequestTime);
                        if (currentRequestTime == thisRequestTime && toast != null) {
                            toast.cancel();
                            toast = null;
                        }
                    }, TOAST_DURATION);
                }
            } else {
                // Create new toast
                LogUtils.d(TAG, "Create new toast");
                textView.setText(message);
                toast = new Toast(context);
                toast.setDuration(Toast.LENGTH_LONG);
                toast.setGravity(Gravity.CENTER, 0, 0);// set gravity center
                toast.setView(view);
                toast.show();

                oldMsg = message;
                currentRequestTime = System.currentTimeMillis();
                final long thisRequestTime = currentRequestTime;
                BaseApplication.getAppContext().runOnUiThreadDelay(() -> {
                    LogUtils.d(TAG, "Current request: " + currentRequestTime + "This "
                            + "request: " + thisRequestTime);
                    if (currentRequestTime == thisRequestTime && toast != null) {
                        toast.cancel();
                        toast = null;
                    }
                }, TOAST_DURATION);
            }
        });
    }
}
