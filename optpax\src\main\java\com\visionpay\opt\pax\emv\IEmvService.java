/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvservice.export.IEmvBase;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.entity.EmvTag;

import java.util.List;

/**
 * EMV Service
 */
public interface IEmvService<T extends IEmvBase> {
    /**
     * Start EMV Process.
     *
     * @param emv EMV Service Implementation Instance.
     * @return This service.
     */
    @NonNull
    IEmvService<T> start(@NonNull T emv, long amount, String terminalId, int detectResult, String reference);

    @NonNull
    IEmvService<T> setConfirmCallback(@Nullable ConfirmCallback callback);

    @NonNull
    IEmvService<T> setErrorCallback(@Nullable ErrorCallback callback);

    @NonNull
    IEmvService<T> setCompleteCallback(@Nullable CompleteCallback callback);

    @NonNull
    IEmvService<T> setSendinfOnlineCallback(@Nullable ISendingOnlineCallback callback);

    interface ConfirmCallback {
        void onConfirm(@NonNull String pan,
                @DrawableRes int issuerIconId,
                @NonNull String issuerName,
                @NonNull String cardHolderName,
                @Nullable List<EmvAidInfo> aidInfoList,
                @Nullable String editAidPath,
                @Nullable String editAidParam,
                @Nullable String cardSequenceNumber,
                @Nullable String cardExpiryDate,
                @Nullable String applicationLabel,
                @Nullable int applicationTransactionCounter,
                @Nullable boolean isBankCard);
    }

    interface ErrorCallback {
        void onError(@NonNull String title, @Nullable String reason);
    }

    interface CompleteCallback {
        void onComplete(boolean isContact, @NonNull TransResultEnum result,
                @NonNull List<CvmType> cvmTypeList,
                @NonNull List<EmvTag> emvTagList);
    }
}
