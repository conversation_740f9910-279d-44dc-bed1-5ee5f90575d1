/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/01/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.factory;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.preference.EditTextPreference;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceCategory;
import androidx.preference.SwitchPreference;
import com.pax.preflib.builder.pref.BaseAmountDialogPreference;
import com.pax.preflib.builder.pref.BaseSeekBarDialogPreference;

/**
 * Preference Factory.
 */
public interface IPreferenceFactory {
    @NonNull
    BaseAmountDialogPreference createAmountEditTextDialogPreference(@NonNull Context context);

    @NonNull
    EditTextPreference createEditTextPreference(@NonNull Context context);

    @NonNull
    ListPreference createListPreference(@NonNull Context context);

    @NonNull
    Preference createPreference(@NonNull Context context);

    @NonNull
    BaseSeekBarDialogPreference createSeekBarDialogPreference(@NonNull Context context);

    @NonNull
    SwitchPreference createSwitchPreference(@NonNull Context context);

    @NonNull
    PreferenceCategory createPreferenceCategory(@NonNull Context context);
}
