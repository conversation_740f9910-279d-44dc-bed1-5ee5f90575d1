package com.visionpay.opt.pax.emv.online.interchange.utils;

import android.util.Log;

import androidx.annotation.NonNull;

import com.pax.commonlib.utils.ConvertUtils;
import com.pax.emvservice.export.IEmvBase;

import java.io.ByteArrayOutputStream;

public class ICCBuilder {

    private static final String TAG = ICCBuilder.class.getName();

    public static byte[] build(@NonNull IEmvBase emv, byte[] aid){
        String aidStr = ConvertUtils.bcd2Str(aid);
        EMVTag[] emvTags;

        if (aidStr.startsWith("A000000003")) {
            emvTags = EMV_VISA_TAGS;
        } else if (aidStr.startsWith("A000000384")) {
            emvTags = EMV_EPAL_TAGS;
        } else {
            emvTags = EMV_MC_TAGS;
        }

        try {
            ByteArrayOutputStream icc = new ByteArrayOutputStream();
            for (EMVTag i : emvTags) {
                byte[] t = emv.getTlv(i.Tag);
                if (t != null && t.length > 0) {
                    byte one = (byte) ((i.Tag >> 8) & 0xFF);
                    if(one != 0)
                        icc.write(one);
                    icc.write((byte) ((i.Tag>> 0) & 0xFF));
                    //icc.write(i.Tag);
                    Integer l;
                    if (i.Len == -1) {
                        l = t.length;
                    } else {
                        l = i.Len / 2;
                        String s = ConvertUtils.bcd2Str(t);
                        while (s.length() < i.Len)
                            s = "0" + s;
                        if(s.length() > i.Len)
                            s = s.substring(s.length()-i.Len);
                        t = ConvertUtils.strToBcd(s, ConvertUtils.EPaddingPosition.PADDING_LEFT);
                    }
                    icc.write(l.byteValue());
                    icc.write(t);
                }
            }
            icc.close();

            return icc.toByteArray();
        } catch (Exception e) {
            Log.e(TAG, e.toString());
        }
        return null;
    }

    private static final EMVTag
            ET_82 = new EMVTag(){{ Tag = 0x82; Len = -1; }},	            //ICC - GPO receive
            ET_84 = new EMVTag(){{ Tag = 0x84; Len = -1; }},	            //ICC - SELECT APP
            ET_95 = new EMVTag(){{ Tag = 0x95; Len = -1; }},	            //+*TERMINAL - Terminal Verification Results - Status of the different functions as seen from the terminal, len 5
            ET_9A = new EMVTag(){{ Tag = 0x9A; Len = 6; }},		            //TERMINAL - Generate - Transaction Date - Local date that the transaction was authorised, len 3
            ET_9C = new EMVTag(){{ Tag = 0x9C; Len = 2; }},		            //TERMINAL - Transaction Type - Indicates the type of financial transaction, represented by the first two digits of ISO 8583:1987 Processing Code, len 1
            ET_5F20 = new EMVTag(){{ Tag = 0x5F20; Len = -1; }},   //ICC - Cardholder Name
            ET_5F24 = new EMVTag(){{ Tag = 0x5F24; Len = 6; }},   //ICC - READ RECORD
            ET_5F2A = new EMVTag(){{ Tag = 0x5F2A; Len = 4; }},	//TERMINAL - Client (kiosk) - Transaction Currency Code - Indicates the currency code of the transaction according to ISO 4217, len 2
            ET_5F34 = new EMVTag(){{ Tag = 0x5F34; Len = 2; }},   //ICC - READ RECORD
            ET_9F02 = new EMVTag(){{ Tag = 0x9F02; Len = 12; }},  //TERMINAL - Client (kiosk) - Amount, Authorised (Numeric) - Authorised amount of the transaction (excluding adjustments), len 6
            ET_9F03 = new EMVTag(){{ Tag = 0x9F03; Len = 12; }},  //TERMINAL - Amount, Other (Numeric) - Secondary amount associated with the transaction representing a cashback amount, len 6
            ET_9F06 = new EMVTag(){{ Tag = 0x9F06; Len = -1; }},  //TERMINAL - SCHEME - Application Identifier (AID) – terminal - Identifies the application as described in ISO/IEC 7816-5, len 5-16
            ET_9F09 = new EMVTag(){{ Tag = 0x9F09; Len = -1; }},  //TERMINAL - SCHEME - Application Version Number - Version number assigned by the payment system for the application - len 2
            ET_9F10 = new EMVTag(){{ Tag = 0x9F10; Len = -1; }},  //ICC - GAC
            ET_9F19 = new EMVTag(){{ Tag = 0x9F19; Len = -1; }},  //ICC - READ RECORD
            ET_9F1A = new EMVTag(){{ Tag = 0x9F1A; Len = 4; }},	//TERMINAL - EMVTerminal Table - +*Terminal Country Code - Indicates the country of the terminal, represented according to ISO 3166, len 2
            ET_9F24 = new EMVTag(){{ Tag = 0x9F24; Len = -1; }},  //*
            ET_9F26 = new EMVTag(){{ Tag = 0x9F26; Len = -1; }},  //ICC - GAC
            ET_9F27 = new EMVTag(){{ Tag = 0x9F27; Len = -1; }},  //ICC - GAC
            ET_9F33 = new EMVTag(){{ Tag = 0x9F33; Len = -1; }},  //TERMINAL - EMVTerminal Table - Terminal Capabilities - Indicates the card data input, CVM, and security capabilities of the terminal, len 3
            ET_9F34 = new EMVTag(){{ Tag = 0x9F34; Len = -1; }},  //+*TERMINAL - Cardholder Verification Method (CVM) Results - Indicates the results of the last CVM performed, len 3
            ET_9F35 = new EMVTag(){{ Tag = 0x9F35; Len = 2; }},   //TERMINAL - EMVTerminal Table - Terminal Type - Indicates the environment of the terminal, its communications capability, and its operational control, len 1
            ET_9F36 = new EMVTag(){{ Tag = 0x9F36; Len = -1; }},  //ICC - ATC(many places)
            ET_9F37 = new EMVTag(){{ Tag = 0x9F37; Len = -1; }},  //TERMINAL - Generated random - Unpredictable Number - Value to provide variability and uniqueness to the generation of a cryptogram, len 4
            ET_9F66 = new EMVTag(){{ Tag = 0x9F66; Len = -1; }},  //TERMINAL - SCHEME - Terminal Transaction Qualifiers(TTQ)(9F66)(DPAS), len 4
            ET_9F6E = new EMVTag(){{ Tag = 0x9F6E; Len = -1; }};  //*???Contact Mode Supported

    private static final EMVTag[] EMV_VISA_TAGS = new EMVTag[]{ ET_82, ET_84, ET_95, ET_9A, ET_5F20, ET_9C, ET_5F34, ET_5F2A, ET_9F02, ET_9F03, ET_9F10, ET_9F1A, ET_9F26, ET_9F27, ET_9F33, ET_9F34, ET_9F36, ET_9F37, ET_9F6E };
    private static final EMVTag[] EMV_MC_TAGS = new EMVTag[]{ ET_82, ET_84, ET_95, ET_9A, ET_9C, ET_5F20, ET_5F34, ET_5F2A, ET_9F02, ET_9F03, ET_9F10, ET_9F1A, ET_9F26, ET_9F27, ET_9F33, ET_9F34, ET_9F36, ET_9F37 };
    //private static final EMVTag[] EMV_AMEX_TAGS = new EMVTag[]{ ET_82, ET_84, ET_95, ET_9A, ET_9C, ET_5F2A, ET_5F34, ET_9F02, ET_9F03, ET_9F06, ET_9F09, ET_9F10, ET_9F26, ET_9F27, ET_9F34, ET_9F36, ET_9F37, ET_9F1A };
    private static final EMVTag[] EMV_EPAL_TAGS = new EMVTag[]{ ET_82, ET_84, ET_95, ET_9A, ET_9C, ET_5F20, ET_5F24, ET_5F2A, ET_5F34, ET_9F02, ET_9F03, ET_9F10, ET_9F19, ET_9F1A, ET_9F24, ET_9F26, ET_9F27, ET_9F33, ET_9F34, ET_9F35, ET_9F36, ET_9F37, ET_9F66, ET_9F6E };

    private static class EMVTag {
        public int Tag;
        public int Len;
    }
}
