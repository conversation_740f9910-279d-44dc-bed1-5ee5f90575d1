package com.pax.emvservice.export.mifare;

public interface IMifareResultListener {
    /**
     * need fallback
     */
    void fallback();

    /**
     * online denied
     */
    void onlineDenied();

    /**
     * offline denied
     * @param resultCode error code
     */
    void offlineDenied(int resultCode);

    /**
     * online card denied
     */
    void onlineCardDenied(int resultCode);

    /**
     * offline approved
     * @param needSignature needSignature
     */
    void offlineApproved(boolean needSignature, boolean needSetARC);

    /**
     * online approved
     * @param needSignature needSignature
     */
    void onlineApproved(boolean needSignature);

    /**
     * online failed
     */
    void onlineFailed();

    /**
     * simple flow
     */
    void simpleFlowEnd();
}
