/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.pax.commonlib.utils.LogUtils;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.dialog.ContentInputDialog;
import com.visionpay.opt.pax.list.CvmTypeAdapter;
import com.visionpay.opt.pax.list.EmvTagAdapter;
import com.visionpay.opt.pax.viewmodel.TransEmvResultViewModel;
import java.util.ArrayList;

/**
 * Transaction CVM Type & EMV Tag List Fragment.
 *
 * All EMV Tag read by {@code getTlv()} or write by {@code setTlv()} of this transaction will
 * display on the list.
 */
public class TransEmvResultFragment extends BaseTransFragment {
    private static final String TAG = "TransEmvResultFragment";

    private TransEmvResultViewModel viewModel;

    private MaterialCardView cvmTypeCard;
    private RecyclerView cvmTypeList;

    private TextView resultTitle;
    private RecyclerView emvTagList;
    private MaterialButton tagSearchButton;

    @NonNull
    @Override
    public String getFragmentTag() {
        return TransFragmentManager.EMV_TAG;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(this).get(TransEmvResultViewModel.class);
        viewModelPrepared(viewModel);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
            @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_emv_result_card, container, false);
        cvmTypeCard = view.findViewById(R.id.trans_cvm_card);
        cvmTypeList = view.findViewById(R.id.trans_cvm_list);
        resultTitle = view.findViewById(R.id.trans_result_title);
        emvTagList = view.findViewById(R.id.trans_emv_tag_list);
        tagSearchButton = view.findViewById(R.id.trans_emv_tag_button);
        return view;
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        if (viewModel != null) {
            // CVM List
            CvmTypeAdapter cvmTypeAdapter = new CvmTypeAdapter();
            cvmTypeList.setAdapter(cvmTypeAdapter);
            cvmTypeList.setLayoutManager(new LinearLayoutManager(getContext()));

            viewModel.listenCvmTypeList().observe(getViewLifecycleOwner(), list -> {
                if (list == null || list.isEmpty()) {
                    LogUtils.d(TAG, "Empty CVM List");
                    cvmTypeCard.setVisibility(View.GONE);
                    cvmTypeList.setVisibility(View.GONE);
                } else {
                    LogUtils.d(TAG, "Cvm Type Size: " + list.size());
                    cvmTypeCard.setVisibility(View.VISIBLE);
                    cvmTypeList.setVisibility(View.VISIBLE);
                    cvmTypeAdapter.setCvmTypeList(list).notifyDataSetChanged();
                    cvmTypeList.invalidateItemDecorations();
                }
            });

            // Title
            viewModel.listenResultTitle().observe(getViewLifecycleOwner(),
                    title -> resultTitle.setText(title));

            // Emv Tag List
            EmvTagAdapter emvTagAdapter = new EmvTagAdapter(new ArrayList<>());
            emvTagList.setAdapter(emvTagAdapter);
            emvTagList.setLayoutManager(new LinearLayoutManager(getContext()));

            viewModel.listenEmvTagList().observe(getViewLifecycleOwner(), list -> {
                if (list != null) {
                    LogUtils.d(TAG, "Update Emv Tag List, list size: " + list.size());
                    emvTagAdapter.setEmvTagList(list)
                            .notifyDataSetChanged();
                }
            });
        }

        tagSearchButton.setOnClickListener(v -> {
            new ContentInputDialog()
                    .setTitle("Search EMV Tag")
                    .setOnBindEditTextCallback(editText -> {
                        ((EditText)editText).setHint("Tag (HEX format)");
                    })
                    .setPositiveButton("SEARCH", null, (dialog, button, editText, input) -> {
                        if (input != null && !input.toString().isEmpty()) {
                            String content = input.toString();
                            if (content.matches("^[\\dabcdefABCDEF]+$")) {
                                viewModel.searchEmvTag(input.toString());
                                dialog.dismissAllowingStateLoss();
                            } else {
                                editText.setError("Error format");
                            }
                        } else {
                            editText.setError("Cannot be empty");
                        }
                    }).setNegativeButton("CANCEL", null, (dialog, button, editText, input) -> {
                        dialog.dismissAllowingStateLoss();
                    })
                    .show(getParentFragmentManager(), "Search Dialog");
        });
    }
}
