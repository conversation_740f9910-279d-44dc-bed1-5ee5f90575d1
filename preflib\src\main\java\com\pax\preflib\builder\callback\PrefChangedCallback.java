/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.callback;

import androidx.annotation.NonNull;

/**
 * A callback to check whether the new value is legal before storing.
 * <br>
 * In fact, this callback is not much different from the {@code OnPreferenceChangeListener}
 * provided by the Preference library. But this callback passed the type conversion during
 * execution, so the value passed in this callback is a certain type instead of {@code Object}.
 * Using this callback, you don't need to perform type judgment and conversion yourself.
 * <br>
 * Currently, there is no callback interface that is executed after the storage is completed. If
 * you want to perform any operation after the storage is completed (such as restarting the
 * application, popping up a dialog, etc.), you can only implement it in the DataStore or through
 * this interface.
 * <br><br>
 * 用于在存储前检查新的值是否合法的回调。
 * <br>
 * 实际上，这个回调和 Preference 库提供的 {@code OnPreferenceChangeListener} 没有太大的区别。但是这个
 * 回调在执行时通过了类型转换，因此，这个回调传入的值是确定的类型，而不是 {@code Object}。使用这个回调，你就不需要
 * 自己再进行类型判断和转换了。
 * <br>
 * 目前没有提供在存储完成之后执行的回调接口。如果您希望在存储完成后执行什么操作（例如重启应用，弹出弹窗等等），您只能
 * 在 DataStore 中实现，或者通过这个接口来实现。
 *
 * @param <P> Preference type.
 * @param <V> Persistent value type. For example, it is String for EditTextPreference and Boolean
 *              for SwitchPreference.
 */
public interface PrefChangedCallback<P, V> {
    boolean onChanged(@NonNull P preference, V newValue);
}
