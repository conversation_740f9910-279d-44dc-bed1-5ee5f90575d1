package com.visionpay.opt.pax.thread;

import com.pax.bizentity.entity.TransData;
import com.pax.commonlib.utils.LazyInit;
import com.pax.commonlib.utils.LogUtils;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.transaction.ITransactionService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.emv.online.IOnlineService;
import com.visionpay.opt.pax.emv.online.interchange.InterchangeService;
import com.visionpay.opt.pax.emv.online.opt.OPTService;
import com.visionpay.opt.pax.entity.IM30State;
import com.visionpay.opt.pax.entity.IM30Transaction;
import com.visionpay.opt.pax.entity.POSTransaction;
import com.visionpay.opt.pax.entity.ProcessType;
import com.visionpay.opt.pax.entity.TransactionStatus;
import com.visionpay.opt.pax.entity.TransactionType;
import com.visionpay.opt.pax.entity.VisionPayTransaction;
import com.visionpay.opt.pax.message.BroadcastMesseger;

import java.util.Date;

public class OnlineThread implements Runnable, InterchangeService.ErrorCallback, InterchangeService.RequestCompleteCallback {

    private static final String TAG = "InterchangeThread";

    private POSTransaction POSTransaction = null;
    private IM30Transaction im30t = null;
    private VisionPayTransaction visionPayTransaction = null;

    private ProcessType processType;

    private final LazyInit<InterchangeService> interchangeService = LazyInit.by(InterchangeService::new);
    private final LazyInit<OPTService> optService = LazyInit.by(OPTService::new);

    public OnlineThread(POSTransaction POSTransaction){

        this.POSTransaction = POSTransaction;

        processType = POSTransaction.getProcessType();

        im30t = new IM30Transaction(POSTransaction);

        visionPayTransaction = new VisionPayTransaction(POSTransaction, im30t, TransactionType.Sale);

        BroadcastMesseger.getInstance().send(im30t);
    }

    private void initTrans() {
        LogUtils.d(TAG, "Init trans");

        ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
        TransData transData = transactionService.getByReference(visionPayTransaction.getExternalReference());

        if(transData != null) {
            long amount = visionPayTransaction.getTransactionAmount();
            visionPayTransaction.loadTransData(transData);
            visionPayTransaction.setTransactionAmount(amount);
            IOnlineService service;

            if(transData.getEnterMode() == TransData.EnterMode.WHITECARD)
                service = optService.get();
            else
                service = interchangeService.get();

            service.setErrorCallback(this)
                    .setRequestCompleteCallback(this);

            if (processType == ProcessType.Capture) {
                sendMessageToCLient(IM30State.SendingCapture);
                long captureAmount = visionPayTransaction.getTransactionAmount();
                long authAmount = Long.parseLong(transData.getAmount());
                if(captureAmount > authAmount)
                    captureAmount = authAmount;
                service.startCapture(POSTransaction.getExternalDeviceToken(), visionPayTransaction.getIm30TerminalId(), transData.getId(), transData.getTraceNo(), captureAmount);
            }
            else if (processType == ProcessType.Reversal) {
                sendMessageToCLient(IM30State.SendingReversal);
                service.startReversal(POSTransaction.getExternalDeviceToken(), visionPayTransaction.getIm30TerminalId(), transData.getId(), transData.getTraceNo(), Long.parseLong(transData.getAmount()));
            }
            LogUtils.d(TAG, "Start Transaction Process");
        }
        else{
            onSend("Transaction not found.");
        }
    }

    private void sendMessageToCLient(IM30State im30State){
        sendMessageToCLient(im30State, null, "", "", "");
    }

    private void sendMessageToCLient(IM30State im30State, TransactionStatus transactionStatus,
                                     String STAN, String gatewayResponse, String gatewayResponseCode) {
        visionPayTransaction.setIm30State(im30State);
        visionPayTransaction.setCompletedUTCDateTime(new Date());
        if(transactionStatus != null)
            visionPayTransaction.setTransactionStatus(transactionStatus);
        if(!STAN.isEmpty())
            visionPayTransaction.setStan(STAN);
        if(!gatewayResponse.isEmpty())
            visionPayTransaction.setGatewayResponse(gatewayResponse);
        if(!gatewayResponseCode.isEmpty())
            visionPayTransaction.setGatewayResponseCode(gatewayResponseCode);
        BroadcastMesseger.getInstance().send(visionPayTransaction);
    }

    @Override
    public void onSend(String messageError) {
        if (processType == ProcessType.Capture)
            sendMessageToCLient(IM30State.SendingCaptureFailed);
        else if (processType == ProcessType.Reversal)
            sendMessageToCLient(IM30State.SendingReversalFailed);
        //if(this.errorCallback != null)
        //    this.errorCallback.onSend(messageError);
    }

    @Override
    public void onComplete(boolean isApproved, String STAN, String gatewayResponse, String gatewayResponseCode) {
        if (processType == ProcessType.Capture)
            sendMessageToCLient(IM30State.SendingCaptureComplete,
                    isApproved ? TransactionStatus.Approved : TransactionStatus.Declined, STAN, gatewayResponse, gatewayResponseCode);
        else if (processType == ProcessType.Reversal)
            sendMessageToCLient(IM30State.SendingReversalComplete,
                    isApproved ? TransactionStatus.Approved : TransactionStatus.Declined, STAN, gatewayResponse, gatewayResponseCode);
        //if(this.requestCompleteCallback != null)
        //    this.requestCompleteCallback.onComplete(isApproved, STAN, gatewayResponse, gatewayResponseCode);
    }

    @Override
    public void run() {
        try {
            initTrans();
        } catch (Exception e) {
            onSend(e.getMessage());
        }
    }
}
