<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/04/19                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/list_padding_vertical">

    <ImageView
        android:id="@+id/cvm_type_icon"
        android:layout_width="@dimen/icon_with_background_width"
        android:layout_height="@dimen/icon_with_background_height"
        android:paddingVertical="@dimen/icon_with_background_padding_vertical"
        android:paddingHorizontal="@dimen/icon_with_background_padding_horizontal"
        android:background="@drawable/bg_cvm_icon"
        app:tint="@color/cvm_icon"
        tools:src="@drawable/ic_pin" />

    <TextView
        android:id="@+id/cvm_type_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="@dimen/card_content_spacing_horizontal"
        android:textSize="@dimen/text_medium_size"
        android:textColor="@color/main_text"
        android:fontFamily="sans-serif-medium"
        tools:text="@string/cvm_online_pin" />

</LinearLayout>