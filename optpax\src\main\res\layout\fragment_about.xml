<?xml version="1.0" encoding="utf-8"?><!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/04/11                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/scrollable_page_content_to_screen_bottom">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_gravity="center_horizontal"
                app:cardCornerRadius="24dp"
                app:cardElevation="4dp">

                <ImageView
                    android:layout_width="98dp"
                    android:layout_height="98dp"
                    android:layout_gravity="center_horizontal"
                    android:src="@mipmap/app_logo"/>

            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/app_name"
                android:textSize="@dimen/text_large_size"
                android:textColor="@color/main_text"
                android:fontFamily="sans-serif-medium"
                android:gravity="center_horizontal"/>

            <TextView
                android:id="@+id/about_app_version"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textSize="@dimen/text_normal_size"
                android:textColor="@color/main_text"
                android:gravity="center_horizontal"
                tools:text="V1.00.00_20220411" />

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/card_margin_horizontal"
                android:layout_marginEnd="@dimen/card_margin_horizontal"
                android:layout_marginTop="64dp"
                app:cardElevation="0dp"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:strokeWidth="@dimen/card_stroke_width"
                app:strokeColor="@color/common_divider"
                app:cardBackgroundColor="@color/main_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingVertical="@dimen/card_padding_vertical"
                    android:paddingHorizontal="@dimen/card_padding_horizontal">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/config_about_kernel_version"
                        android:textColor="@color/main_text"
                        android:textSize="@dimen/card_title_size"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/about_kernel_version_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/card_content_spacing_vertical"
                        android:overScrollMode="never"/>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>