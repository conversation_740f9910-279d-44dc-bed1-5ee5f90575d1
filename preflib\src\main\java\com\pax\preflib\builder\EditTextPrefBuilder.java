/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.EditTextPreference;
import com.pax.commonlib.utils.LogUtils;
import com.pax.preflib.R;
import com.pax.preflib.builder.callback.BindEditTextCallback;
import com.pax.preflib.builder.callback.InputValidateCallback;
import com.pax.preflib.builder.factory.PrefFactory;
import java.util.LinkedList;
import java.util.List;

/**
 * Create EditTextPreference.
 * <br>
 * 创建 EditTextPreference
 */
abstract class EditTextPrefBuilder<T extends EditTextPrefBuilder<T>> extends BasePrefBuilder<EditTextPreference, T> {
    protected List<InputValidateCallback<String>> inputValidateCallbackList = new LinkedList<>();
    protected List<BindEditTextCallback> bindEditTextCallbackList = new LinkedList<>();
    protected boolean showSummary = true;

    protected EditTextPrefBuilder(@NonNull Context context, @NonNull String key, @NonNull String title) {
        super(context, key, title);
    }

    protected abstract boolean handlePrefChangedCallback(@NonNull EditTextPreference preference,
            @Nullable Object newValue);

    /**
     * Add a callback to verify that the input content is legal. This callback will put the
     * string after entering the new content into the {@code onChange()} method. You can check
     * whether the new string is legal in the callback. If it is not legal, return false, so that
     * the user can be prevented from entering this content, and the text in the input box will not
     * change. If it returns true, the user's current input is allowed.
     * <br>
     * Please note, do not regard this callback as {@code OnPreferenceChangeListener} or {@code
     * PrefChangedCallback}, the purpose of the two is completely different. This callback is to
     * check whether the content you input is legal, and {@code OnPreferenceChangeListener} or
     * {@code PrefChangedCallback} is to check whether the content you want to store is legal.
     * This callback will be executed every time the user enters or deletes a character, and
     * {@code OnPreferenceChangeListener} or {@code PrefChangedCallback} will only execute once
     * before storing the data.
     * <br>
     * Since this method is to add a callback instead of set callback, so you can add multiple
     * callbacks to an {@code EditTextPrefBuilder}.
     * <br><br>
     * 添加一个验证输入内容是否合法的回调。这个回调会将用户输入了新内容的字符串通过 {@code onChange()} 方法传入。
     * 你可以通过检查这个字符串来验证输入的新内容是否合法。如果不合法，则返回 {@code false}，这样就会阻止用户输入
     * 这个新内容，而输入框仍然保持原来的内容。如果返回 {@code true}，则用户的输入将被批准。
     * <br>
     * 请注意，不要把这个回调和 {@code OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 搞
     * 混了，两者是完全不同的。{@code InputValidateCallback} 这个回调是用来检查用户正在输入的内容是否合法，而 {@code
     * OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 是用来检查用户想要保存的内容是否合法。
     * {@code InputValidateCallback} 这个回调会在每次用户输入或者删除一个字符的时候就被执行，而 {@code
     * OnPreferenceChangeListener} 或者 {@code PrefChangedCallback} 只有在保存数据前才会执行一次。
     * <br>
     * 由于这个方法是添加一个 callback 而非设置一个 callback，所以你可以对一个 {@code EditTextPrefBuilder}
     * 执行多次 {@code addTextValidateCallback} 方法。
     *
     * @param callback A callback to verify that the input content is legal. <br>验证正在输入的内容是否合法的回调
     * @return This builder
     */
    public T addInputValidateCallback(@NonNull InputValidateCallback<String> callback) {
        inputValidateCallbackList.add(callback);
        return (T) this;
    }

    private boolean verifyNewContent(@NonNull String newValue) {
        for (InputValidateCallback<String> callback : inputValidateCallbackList) {
            if (!callback.onChanged(newValue)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Add a callback to be executed when {@code EditText} is bound. When the user clicks {@code
     * EditTextPreference} and a dialog with {@code EditText} pops up, this callback will be
     * executed, and this {@code EditText} instance will be passed in via the {@code onBind()}
     * method. You can use this {@code EditText} to perform some setting operations.
     * <br><br>
     * 添加一个绑定 {@code EditText} 时执行的回调。当用户点击 {@code EditTextPreference}，弹出带有 {@code
     * EditText} 的对话框时，这个回调将会被执行，并通过 {@code onBind()} 方法传入这个 {@code EditText} 实例。
     * 你可以利用这个 {@code EditText} 来进行一些设置操作。
     *
     * @param callback A callback to be executed when {@code EditText} is bound
     * @return This builder
     */
    public T addBindEditTextCallback(@NonNull BindEditTextCallback callback) {
        bindEditTextCallbackList.add(callback);
        return (T) this;
    }

    /**
     * Whether to display the content summary. The default is {@code true}. If it is set to
     * {@code false}, the user can see the saved content of this option on {@code EditText} only
     * after clicking {@code EditTextPreference}, and a summary of "(HIDE)" will be displayed for
     * this option.
     * <br>
     * Please note that this option does not hide the content displayed after the user clicks
     * {@code EditTextPreference}. If you need to completely hide the content of this option,
     * please do so by customizing the DataStore.
     * <br><br>
     * 是否显示内容摘要。默认为 {@code true}。如果设置 {@code false}，那么用户只有点击 {@code
     * EditTextPreference} 以后才能在 {@code EditText} 上看到这个选项保存的内容，并且会给这个选项显示一个
     * "(HIDE)" 的摘要。
     * <br>
     * 请注意，这个选项并不会隐藏用户点击 {@code EditTextPreference} 以后显示的内容。如果你需要彻底隐藏该选项
     * 的内容，请通过自定义 DataStore 来实现。
     *
     * @param showSummary Whether to display the content summary.
     * @return This builder
     */
    public T setShowSummary(boolean showSummary) {
        this.showSummary = showSummary;
        return (T) this;
    }

    @NonNull
    @Override
    public EditTextPreference build() {
        EditTextPreference preference = PrefFactory.getInstance().createEditTextPreference(context);
        preference.setKey(key);
        preference.setTitle(title);
        preference.setDialogTitle(title);
        if (showSummary) {
            preference.setSummaryProvider(EditTextPreference.SimpleSummaryProvider.getInstance());
        } else {
            preference.setSummaryProvider(null);
            preference.setSummary(R.string.configui_hide_value);
        }
        preference.setIconSpaceReserved(false);
        preference.setOnBindEditTextListener(editText -> {
            int length = editText.getText().length();
            // If there is no delay, the cursor cannot be positioned to the end of the text
            editText.postDelayed(() -> editText.setSelection(length), 30);
            if (!bindEditTextCallbackList.isEmpty()) {
                for (BindEditTextCallback callback : bindEditTextCallbackList) {
                    callback.onBind(editText);
                }
            }
            // Don't worry, you can set your own TextChangedListener in BindEditTextCallback.
            // Because one EditText can set multiple TextChangedListener.
            if (!inputValidateCallbackList.isEmpty()) {
                editText.addTextChangedListener(new TextWatcher() {
                    private String before;
                    private int cursorPosition = 0;

                    @Override
                    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                        if (s == null) {
                            before = "";
                            return;
                        }
                        before = s.toString();
                        cursorPosition = editText.getSelectionStart();
                    }

                    @Override
                    public void onTextChanged(CharSequence s, int start, int before, int count) {
                        // do nothing
                    }

                    @Override
                    public void afterTextChanged(Editable s) {
                        if (s == null) {
                            return;
                        }
                        String content = s.toString();
                        if (!content.equals(before) && !verifyNewContent(content)) {
                            // invalidate!!!
                            LogUtils.d("EditTextPrefBuilder", "invalidate content: " +
                                    content + ", before: " + before + ", selection: " + cursorPosition);
                            int beforeLen = before.length();
                            int afterLen = content.length();
                            editText.setText(before);
                            if (beforeLen > afterLen) {
                                // delete
                                editText.setSelection(Math.min(cursorPosition + 1, beforeLen));
                            } else if (beforeLen < afterLen) {
                                // add
                                editText.setSelection(Math.min(cursorPosition - 1, beforeLen));
                            } else {
                                // maybe paste
                                editText.setSelection(Math.min(cursorPosition, beforeLen));
                            }
                            cursorPosition = editText.getSelectionStart();
                        }
                    }
                });
            }
        });
        preference.setOnPreferenceChangeListener((pref, newValue) -> handlePrefChangedCallback(preference, newValue));
        if (onPreferenceClickListener != null) {
            preference.setOnPreferenceClickListener(onPreferenceClickListener);
        }
        return preference;
    }
}
