package com.visionpay.opt.pax.emv.online.interchange.messages.requests;

import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageSubtype;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageType;
import com.visionpay.opt.pax.emv.online.interchange.messages.FinancialMessage;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class AuthorizeRequest extends FinancialMessage {

    private final long mInternalID;
    private final byte mAccountType;
    private final byte mCardSource;
    private final long mAmount;
    private final int mCardSequenceNumber;
    private final byte[] mPAN;
    private final byte[] mPIN;
    private final byte[] mAID;
    private final byte[] mICC;
    private final String mTrack2;

    @Override
    protected byte[] internalGetMessage() {
        byte[] internalMessage = super.internalGetMessage();
        ByteArrayOutputStream message = new ByteArrayOutputStream();
        try {
            message.write(internalMessage);
        } catch (IOException e) {
            e.printStackTrace();
        }
        message.write((byte) ((this.mInternalID >> 24) & 0xFF));
        message.write((byte) ((this.mInternalID >> 16) & 0xFF));
        message.write((byte) ((this.mInternalID >> 8) & 0xFF));
        message.write((byte) ((this.mInternalID >> 0) & 0xFF));
        message.write(this.mCardSource);
        message.write(this.mAccountType);
        message.write((byte) this.mPAN.length);
        message.write(this.mPAN, 0, this.mPAN.length);
        message.write((byte) ((this.mAmount >> 24) & 0xFF));
        message.write((byte) ((this.mAmount >> 16) & 0xFF));
        message.write((byte) ((this.mAmount >> 8) & 0xFF));
        message.write((byte) ((this.mAmount >> 0) & 0xFF));
        message.write((byte) ((this.mCardSequenceNumber >> 24) & 0xFF));
        message.write((byte) ((this.mCardSequenceNumber >> 16) & 0xFF));
        message.write((byte) ((this.mCardSequenceNumber >> 8) & 0xFF));
        message.write((byte) ((this.mCardSequenceNumber >> 0) & 0xFF));
        message.write((byte) this.mTrack2.length());
        message.write(this.mTrack2.getBytes(), 0, this.mTrack2.length());
        message.write((byte) this.mPIN.length);
        message.write(this.mPIN, 0, this.mPIN.length);
        message.write((byte) this.mAID.length);
        message.write(this.mAID, 0, this.mAID.length);
        message.write((byte) ((this.mICC.length >> 8) & 0xFF));
        message.write((byte) ((this.mICC.length >> 0) & 0xFF));
        message.write(this.mICC, 0, this.mICC.length);

        return message.toByteArray();
    }

    public AuthorizeRequest(
            String terminalID,
            long refNo,
            byte[] sessionID,
            long internalID,
            byte accountType,
            byte cardSource,
            byte[] PAN,
            long amount,
            int cardSequenceNumber,
            String track2,
            byte[] PIN,
            byte[] AID,
            byte[] ICC) {
        super(InterchangeMessageType.IMT_TRX, InterchangeMessageSubtype.SMT_PREAUTH, refNo, terminalID, sessionID);

        this.mInternalID = internalID;
        this.mAccountType = accountType;
        this.mCardSource = cardSource;
        this.mPAN = PAN;
        this.mAmount = amount;
        this.mCardSequenceNumber = cardSequenceNumber;
        this.mTrack2 = track2;
        this.mAID = AID;
        this.mPIN = PIN;
        this.mICC = ICC;
    }
}