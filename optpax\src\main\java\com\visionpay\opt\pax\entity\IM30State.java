package com.visionpay.opt.pax.entity;

public enum IM30State implements BaseEnum<Integer>{
    AwaitingCardRead(0),
    CardReadSuccess(1),
    CardReadFailed(2),
    SendingOnline(3),
    SendingOnlineComplete(4),
    SendingOnlineFailed(5),
    PreAuthorizationComplete(6),
    SendingCapture(7),
    SendingCaptureComplete(8),
    SendingCaptureFailed(9),
    SendingReversal(10),
    SendingReversalComplete(11),
    SendingReversalFailed(12),
    Canceled(98),
    Timeout(99);

    private Integer value;

    IM30State(Integer value){
        this.value = value;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
