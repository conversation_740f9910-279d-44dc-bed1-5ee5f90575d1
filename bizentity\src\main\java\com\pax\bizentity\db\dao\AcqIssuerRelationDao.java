package com.pax.bizentity.db.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.SqlUtils;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.Acquirer;
import com.pax.bizentity.entity.Issuer;

import com.pax.bizentity.entity.AcqIssuerRelation;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "acq_issuer_relation".
*/
public class AcqIssuerRelationDao extends AbstractDao<AcqIssuerRelation, Long> {

    public static final String TABLENAME = "acq_issuer_relation";

    /**
     * Properties of entity AcqIssuerRelation.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property Acquirer_id = new Property(1, long.class, "acquirer_id", false, "ACQUIRER_ID");
        public final static Property Issuer_id = new Property(2, long.class, "issuer_id", false, "ISSUER_ID");
    }

    private DaoSession daoSession;


    public AcqIssuerRelationDao(DaoConfig config) {
        super(config);
    }
    
    public AcqIssuerRelationDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"acq_issuer_relation\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"ACQUIRER_ID\" INTEGER NOT NULL ," + // 1: acquirer_id
                "\"ISSUER_ID\" INTEGER NOT NULL );"); // 2: issuer_id
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"acq_issuer_relation\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, AcqIssuerRelation entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindLong(2, entity.getAcquirer_id());
        stmt.bindLong(3, entity.getIssuer_id());
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, AcqIssuerRelation entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
        stmt.bindLong(2, entity.getAcquirer_id());
        stmt.bindLong(3, entity.getIssuer_id());
    }

    @Override
    protected final void attachEntity(AcqIssuerRelation entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public AcqIssuerRelation readEntity(Cursor cursor, int offset) {
        AcqIssuerRelation entity = new AcqIssuerRelation( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.getLong(offset + 1), // acquirer_id
            cursor.getLong(offset + 2) // issuer_id
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, AcqIssuerRelation entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAcquirer_id(cursor.getLong(offset + 1));
        entity.setIssuer_id(cursor.getLong(offset + 2));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(AcqIssuerRelation entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(AcqIssuerRelation entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(AcqIssuerRelation entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getAcquirerDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T1", daoSession.getIssuerDao().getAllColumns());
            builder.append(" FROM acq_issuer_relation T");
            builder.append(" LEFT JOIN acquirer T0 ON T.\"ACQUIRER_ID\"=T0.\"acquirer_id\"");
            builder.append(" LEFT JOIN issuer T1 ON T.\"ISSUER_ID\"=T1.\"issuer_id\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected AcqIssuerRelation loadCurrentDeep(Cursor cursor, boolean lock) {
        AcqIssuerRelation entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        Acquirer acquirer = loadCurrentOther(daoSession.getAcquirerDao(), cursor, offset);
         if(acquirer != null) {
            entity.setAcquirer(acquirer);
        }
        offset += daoSession.getAcquirerDao().getAllColumns().length;

        Issuer issuer = loadCurrentOther(daoSession.getIssuerDao(), cursor, offset);
         if(issuer != null) {
            entity.setIssuer(issuer);
        }

        return entity;    
    }

    public AcqIssuerRelation loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<AcqIssuerRelation> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<AcqIssuerRelation> list = new ArrayList<AcqIssuerRelation>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<AcqIssuerRelation> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<AcqIssuerRelation> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
