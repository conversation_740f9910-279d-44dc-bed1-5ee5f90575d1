package com.pax.emvservice.emv.transaction;

import com.pax.bizentity.db.helper.GreendaoHelper;
import com.pax.bizentity.entity.TransData;
import com.pax.bizlib.trans.TransactionHelper;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.transaction.ITransactionService;
import com.sankuai.waimai.router.annotation.RouterService;

@RouterService(interfaces = ITransactionService.class,key = EmvServiceConstant.EMVSERVICE_TRANSACTION,singleton = true)
public class TransactionService implements ITransactionService {

    /**
     * transaction init
     * @return
     */
    @Override
    public TransData transInit(String reference, String amount, String pan, boolean hasPin, String track2, String aid, String appName, String expDate, String atc, String cardSerialNo, TransData.EnterMode enterMode){
        TransData td = TransactionHelper.transInit();
        td.setReference(reference);
        td.setAmount(amount);
        td.setPan(pan);
        td.setIsOnlineTrans(true);
        td.setHasPin(hasPin);
        td.setTrack2(track2);
        td.setAid(aid);
        td.setEmvAppLabel(appName);
        td.setExpDate(expDate);
        td.setAtc(atc);
        td.setCardSerialNo(cardSerialNo);
        td.setEnterMode(enterMode);
        GreendaoHelper.getTransDataHelper().insert(td);
        return td;
    }

    /**
     * transaction init
     * @return
     */
    @Override
    public TransData transUpdate(long id, String authCode, String emvResult, TransData.ReversalStatus reversalStatus, String responseCode, String issuerCode, long stan){
        TransData td = GreendaoHelper.getTransDataHelper().loadByRowId(id);
        if(td != null){
            td.setAuthCode(authCode);
            td.setEmvResult(emvResult);
            td.setReversalStatus(reversalStatus);
            td.setResponseCode(responseCode);
            td.setIssuerCode(issuerCode);
            td.setTraceNo(stan);
            GreendaoHelper.getTransDataHelper().update(td);
        }
        return td;
    }

    @Override
    public TransData getByReference(String reference){
        return GreendaoHelper.getTransDataHelper().findTransDataByReference(reference);
    }

    @Override
    public boolean remove(long id){
        return GreendaoHelper.getTransDataHelper().deleteByKey(id);
    }
}
