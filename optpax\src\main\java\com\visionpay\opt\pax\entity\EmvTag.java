/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.entity;

/**
 * EMV Tag Entity
 */
public class EmvTag {
    private String tag;
    private String value;

    public EmvTag(String tag, String value) {
        this.tag = tag;
        this.value = value;
    }

    public String getTag() {
        return tag;
    }

    public EmvTag setTag(String tag) {
        this.tag = tag;
        return this;
    }

    public String getValue() {
        return value;
    }

    public EmvTag setValue(String value) {
        this.value = value;
        return this;
    }
}
