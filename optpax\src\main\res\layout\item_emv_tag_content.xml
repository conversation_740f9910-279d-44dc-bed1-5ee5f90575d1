<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/22                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/emv_tag_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/list_padding_vertical"
    android:orientation="vertical"
    android:clickable="true"
    android:focusable="true">

    <TextView
        android:id="@+id/emv_tag_item_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/main_text"
        android:textSize="@dimen/text_normal_size"
        android:fontFamily="sans-serif-medium"
        tools:text="AID(4F)" />

    <TextView
        android:id="@+id/emv_tag_item_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:textColor="@color/main_text"
        android:textSize="@dimen/text_normal_size"
        android:fontFamily="monospace"
        android:letterSpacing="0.02"
        tools:text="A0000000041010" />

</LinearLayout>