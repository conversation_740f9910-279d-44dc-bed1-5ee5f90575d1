package com.visionpay.opt.pax.emv.online.interchange;

import android.util.Log;

import com.alibaba.fastjson.TypeReference;
import com.pax.commonlib.apppara.AppParaLoader;
import com.pax.commonlib.json.JsonProxy;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.configservice.export.ConfigKeyConstant;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IConfigParamService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageSubtype;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeTransactionType;
import com.visionpay.opt.pax.emv.online.interchange.messages.Message;
import com.visionpay.opt.pax.emv.online.interchange.messages.requests.AuthorizeRequest;
import com.visionpay.opt.pax.emv.online.interchange.messages.requests.CaptureRequest;
import com.visionpay.opt.pax.emv.online.interchange.messages.requests.InitRequest;
import com.visionpay.opt.pax.emv.online.interchange.messages.requests.LogonRequest;
import com.visionpay.opt.pax.emv.online.interchange.messages.requests.ReversalRequest;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.AuthorizeResponse;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.CaptureResponse;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.InitResponse;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.LogonResponse;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.ReversalResponse;
import com.visionpay.opt.pax.emv.online.interchange.tcp.SimpleTLSTCPClient;

import java.io.IOException;
import java.util.List;
import java.util.Random;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;

public class Interchange {
    private static SimpleTLSTCPClient mClient;

    private final String mHostName;
    private final String mHostIP;
    private final String mPort;
    private String mTerminalID;
    private String mMerchantID;
    private long mRefNo = 0;

    private byte[] mSessionID;

    private static Interchange interchange;
    private boolean interchangeInit = false;
    private static List<InterchangeHost> interchangeHosts;

    private static void readConfig(){
        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        String interchangeFile = configParamService.getString(ConfigKeyConstant.INTERCHANGE_HOSTS, "");
        interchangeHosts = JsonProxy.getInstance().readObjFromAppPara(interchangeFile,new TypeReference<List<InterchangeHost>>(){}.getType());
    }

    public static Interchange getInstance(String hostName, String hostIP, String port, String terminalID){
        if(interchange == null){
            interchange = new Interchange(hostName, hostIP, port, terminalID);
        }
        if(!interchange.mHostName.equals(hostName) || !interchange.mHostIP.equals(hostIP) || !interchange.mPort.equals(port) || !interchange.mTerminalID.equals(terminalID)) {
            interchange = new Interchange(hostName, hostIP, port, terminalID);
        }
        return interchange;
    }

    private Interchange(String hostName, String hostIP, String port, String terminalID)
    {
        mHostName = hostName;
        mHostIP = hostIP;
        mPort = port;
        mTerminalID = terminalID;
        while (mTerminalID.length() < 10) {
            mTerminalID = " " + mTerminalID;
        }

        mClient = SimpleTLSTCPClient.getInstance(mHostName, mHostIP, mPort);
    }

    private static Interchange interchangeLogon(String terminalId) throws Exception {
        int i = 0;
        IConfigParamService configParamService = Router.getService(IConfigParamService.class, ConfigServiceConstant.CONFIGSERVICE_CONFIG);
        String keyFile = configParamService.getString(ConfigKeyConstant.ENCRYPTION_KEY, "");
        byte[] key = getKey(keyFile);
        readConfig();
        while (i < interchangeHosts.size())
        {
            try {
                Interchange interchange = Interchange.getInstance(interchangeHosts.get(i).getHostName(), interchangeHosts.get(i).getiP(), interchangeHosts.get(i).getPort(), terminalId);

                Random rd = new Random();
                byte[] rnd1 = new byte[8];
                rd.nextBytes(rnd1);
                //	byte_vector rnd1 = { 0,0,0,0,0,0,0,0 };

                byte[] crnd1 = DES.encrypt(rnd1, key);
                //	byte_vector crnd1 = rnd1;

                byte[] buffer = interchange.logon1(crnd1);
                if(buffer != null && buffer.length > 0) {
                    LogonResponse logon1 = new LogonResponse(buffer);
                    if (logon1.parse()) {
                        //byte[] xrnd1 = DES.encrypt(logon1.RND1, key);
                        byte[] rnd2 = DES.encrypt(logon1.RND2, key);

                        byte[] crnd2 = DES.encrypt(rnd2, key);
                        //	byte_vector crnd2 = rnd2;

                        interchange.logon2(crnd2);

                        byte[] sessionID = new byte[8];
                        for (int ix = 0; ix < sessionID.length; ix++)
                            sessionID[ix] = (byte) (rnd1[ix] ^ rnd2[ix]);

                        interchange.setSessionID(sessionID);

                        if (!interchange.interchangeInit) {
                            buffer = interchange.init();
                            InitResponse init = new InitResponse(buffer);
                            if (init.parse()) {
                                interchange.mMerchantID = init.CAID;
                                //CDB::getInstance()->SetConfigValue("MID", init.CAID);
                                //CDB::getInstance()->SetConfigValue("Receipt2", init.Address1);
                                //CDB::getInstance()->SetConfigValue("Receipt3", init.Address2);
                                //CDB::getInstance()->SetConfigValue("Receipt4", init.Address3);
                                //CDB::getInstance()->SetConfigValue("Receipt5", init.Footer);

                                interchange.interchangeInit = true;
                            }
                        }
                        if (interchange.interchangeInit)
                            return interchange;
                    }
                }
            }
            catch (IOException ex){
                ex.printStackTrace();
            }
            i++;
        }
        return null;
    }

    private static byte[] getKey(String keyFile){
        try {
            return ConvertUtils.strToBcdPaddingRight(AppParaLoader.getString(keyFile));
        } catch (IOException e) {
            return new byte[0];
        }
    }

    public static CaptureResponse sendForOnlineCapture(String terminalId, long internalID, long amount)  throws Exception
    {
        CaptureResponse response = null;
        Interchange interchange = interchangeLogon(terminalId);
        if (interchange != null) {

            byte[] buffer = interchange.capture(internalID, amount);
            if (buffer != null && buffer.length >= 2) {
                response = new CaptureResponse(buffer);
                response.parse();
            }
        }
        return response;
    }

    public static ReversalResponse sendForOnlineReversal(String terminalId, long internalID) throws Exception
    {
        ReversalResponse response = null;
        Interchange interchange = interchangeLogon(terminalId);
        if (interchange != null) {
            byte[] buffer = interchange.reverse(internalID);
            if (buffer != null && buffer.length >= 2) {
                response = new ReversalResponse(buffer);
                response.parse();
            }
        }
        return response;
    }

    public static AuthorizeResponse onlineAuthorizeCard(long internalID, byte[] PAN, long amount, int cardSequenceNumber, String track2, byte[] PIN, byte[] AID, byte[] ICC, byte currentAccountType, InterchangeTransactionType currentTransactionType, String terminalId) throws Exception {
        AuthorizeResponse response = null;
        Interchange interchange = interchangeLogon(terminalId);
        if (interchange != null)
        {
            byte[] buffer = interchange.authorize(internalID, currentAccountType, currentTransactionType.value, PAN, amount, cardSequenceNumber, track2, PIN, AID, ICC);
            if (buffer != null && buffer.length >= 2)
            {
                response = new AuthorizeResponse(buffer, interchange.mMerchantID);
                response.parse();
            }
        }
        return response;
    }

    private byte[] sendReceive(Message request) throws Exception {
        byte[] buffer = null;
        Log.i("sendReceive:request", request.toString());
        if (mClient.send(request.getMessage()))
        {
            buffer = mClient.blockReceive(60000);
            if (buffer != null && buffer.length >= 2)
            {
                Message response = new Message(buffer);
                response.parse();
                Log.i("sendReceive:response", response.toString());
            }
        }
        return buffer;
    }

    private byte[] logon1(byte[] RND1) throws Exception {
        mClient.open();
        LogonRequest logon1 = new LogonRequest(mTerminalID, mRefNo++, RND1, InterchangeMessageSubtype.SMT_LOGON1);
        return sendReceive(logon1);
    }

    private byte[] logon2(byte[] RND2) throws Exception {
        LogonRequest logon2 = new LogonRequest(mTerminalID, mRefNo++, RND2, InterchangeMessageSubtype.SMT_LOGON2);
        //return sendReceive(logon2);
        mClient.send(logon2.getMessage());
        return null;
    }

    private byte[] init() throws Exception {
        InitRequest init = new InitRequest(mTerminalID, mRefNo++, mSessionID);
        return sendReceive(init);
    }

    private byte[] authorize(long internalID, byte accountType, byte cardSource, byte[] PAN, long amount, int cardSequenceNumber, String track2, byte[] PIN, byte[] AID, byte[] ICC) throws Exception {
        AuthorizeRequest authorize = new AuthorizeRequest(mTerminalID, mRefNo++, mSessionID, internalID, accountType, cardSource, PAN, amount, cardSequenceNumber, track2, PIN, AID, ICC);
        return sendReceive(authorize);
    }

    private byte[] capture(long internalID, long amount) throws Exception {
        CaptureRequest capture = new CaptureRequest(mTerminalID, mRefNo++, mSessionID, internalID, amount);
        return sendReceive(capture);
    }

    private byte[] reverse(long internalID) throws Exception {
        ReversalRequest capture = new ReversalRequest(mTerminalID, mRefNo++, mSessionID, internalID);
        return sendReceive(capture);
    }

    private void setSessionID(byte[] SessionID)
    {
        mSessionID = SessionID;
    }

    private static class DES {
        // Please DO NOT arbitrarily change the mode and padding of this algorithm, as this will
        // affect the results of the EMV process. In fact, the mode of the algorithm as well as
        // the padding are part of the EMV specification.
        // Some security scanning software may prompt that a more secure algorithm should be used,
        // but you should ignore this part of the prompt. Do not modify the configuration here
        // because of this, unless you are sure that the algorithm mode and padding required by
        // the EMV specification have changed.
        private static final String TRANSFORMATION = "DES" + "/" + "ECB" + "/" + "NoPadding";

        private DES() {

        }

        private static SecretKey genKey(final byte[] password) throws Exception {
            DESKeySpec desKey = new DESKeySpec(password);
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            return keyFactory.generateSecret(desKey);
        }

        static byte[] encrypt(final byte[] input, final byte[] password) throws Exception {
            SecretKey secureKey = genKey(password);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secureKey);
            return cipher.doFinal(input);
        }

        static byte[] decrypt(final byte[] input, final byte[] password) throws Exception {
            SecretKey secureKey = genKey(password);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secureKey);
            return cipher.doFinal(input);
        }
    }
}
