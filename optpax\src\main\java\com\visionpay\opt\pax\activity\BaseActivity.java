package com.visionpay.opt.pax.activity;

import android.annotation.SuppressLint;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.view.WindowManager;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.pax.commonlib.BuildConfig;
import com.pax.dal.ISys;
import com.pax.poslib.neptune.Sdk;
import com.visionpay.opt.pax.app.App;

public abstract class BaseActivity  extends AppCompatActivity {

    protected static final boolean enableDebug = com.pax.emvbase.BuildConfig.DEBUG;

    private static final int UI_ANIMATION_DELAY = 300;
    private static final int HIDE_DELAY = 5000;
    private static final int RESTART_COUNT_DELAY = 1000;
    private static final int COUNT_TOUCH_TOTAL = 10;
    private final Handler mHideHandler = new Handler(Looper.myLooper());

    private boolean mVisible;
    private int mCountTouch = 0;

    private View mContentView;

    private final Runnable mAutoHideRunnable = () -> hide();
    private final Runnable mRestartCountRunnable = () -> mCountTouch = 0;

    private final Runnable mHidePart2Runnable = new Runnable() {
        @SuppressLint("InlinedApi")
        @Override
        public void run() {
            // Delayed removal of status and navigation bar
            /*if (Build.VERSION.SDK_INT >= 30) {
                mContentView.getWindowInsetsController().hide(
                        WindowInsets.Type.statusBars() | WindowInsets.Type.navigationBars());
            } else {
                // Note that some of these constants are new as of API 16 (Jelly Bean)
                // and API 19 (KitKat). It is safe to use them, as they are inlined
                // at compile-time and do nothing on earlier devices.
                mContentView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LOW_PROFILE
                        | View.SYSTEM_UI_FLAG_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION);
            }*/
        }
    };

    private final Runnable mShowPart2Runnable = () -> {
        // Delayed display of UI elements
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.show();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        if(BuildConfig.RELEASE){
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);//禁用截屏
        }

        mVisible = true;
    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);

        mContentView = ((ViewGroup)getWindow().getDecorView()).getChildAt(0);

        // Set up the user interaction to manually show or hide the system UI.
        if(enableDebug)
            mContentView.setOnClickListener(view -> toggle());

        hide();
    }

    @Override
    protected void onDestroy() {
        //show();
        mHideHandler.removeCallbacks(mRestartCountRunnable);
        mHideHandler.removeCallbacks(mShowPart2Runnable);
        mHideHandler.removeCallbacks(mAutoHideRunnable);
        super.onDestroy();
    }

    private void toggle() {
        if (!mVisible) {
            mCountTouch++;
            if(mCountTouch >= COUNT_TOUCH_TOTAL) {
                show();
                mCountTouch = 0;
            }else{
                mHideHandler.removeCallbacks(mRestartCountRunnable);
                mHideHandler.postDelayed(mRestartCountRunnable, RESTART_COUNT_DELAY);
            }
        }
    }

    public void hide() {
        // Hide UI first
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
        mVisible = false;

        ISys SysModule = Sdk.getInstance().getDal(App.getAppContext()).getSys();
        SysModule.showNavigationBar(mVisible);
        SysModule.showStatusBar(mVisible);
        SysModule.enableStatusBar(mVisible);

        // Schedule a runnable to remove the status and navigation bar after a delay
        mHideHandler.removeCallbacks(mShowPart2Runnable);
        mHideHandler.postDelayed(mHidePart2Runnable, UI_ANIMATION_DELAY);
    }

    private void show() {
        // Show the system bar
        if (Build.VERSION.SDK_INT >= 30) {
            mContentView.getWindowInsetsController().show(
                    WindowInsets.Type.statusBars() | WindowInsets.Type.navigationBars());
        } else {
            mContentView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION);
        }
        mVisible = true;

        ISys SysModule = Sdk.getInstance().getDal(App.getAppContext()).getSys();
        SysModule.showNavigationBar(mVisible);
        SysModule.showStatusBar(mVisible);
        SysModule.enableStatusBar(mVisible);

        // Schedule a runnable to display UI elements after a delay
        mHideHandler.removeCallbacks(mHidePart2Runnable);
        mHideHandler.postDelayed(mShowPart2Runnable, UI_ANIMATION_DELAY);

        mHideHandler.removeCallbacks(mAutoHideRunnable);
        mHideHandler.postDelayed(mAutoHideRunnable, HIDE_DELAY);
    }

    protected void fragmentSelect(int containerViewId, Fragment fragment) {
        FragmentManager fManager = getSupportFragmentManager();
        FragmentTransaction transaction = fManager.beginTransaction();
        transaction.replace(containerViewId, fragment);
        transaction.commit();
    }
}

