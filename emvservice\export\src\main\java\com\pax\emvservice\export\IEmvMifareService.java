package com.pax.emvservice.export;


import com.pax.emvbase.param.EmvProcessParam;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.mifare.IMifareCallback;
import com.pax.emvservice.export.mifare.IMifareResultListener;

/**
 * service for emv contactless mifare
 */
public interface IEmvMifareService extends IEmvBase{
    /**
     * emv contactless mifare pretreatment
     * @param emvProcessParam emvProcessParam
     * @return pretreatment result
     */
    int preTransProcess(EmvProcessParam emvProcessParam);

    /**
     * start contactless mifare process,need handle timeout situation
     * @param mifareCallback mifareCallback
     * @return result
     */
    int startTransProcess(long amount, String terminalId, int detectResult, String reference, IMifareCallback mifareCallback, ISendingOnlineCallback sendinfOnlineCallback);

    /**
     * check contactless result
     * @param mifareResultListener mifareResultListener
     */
    void checkMifareResult(IMifareResultListener mifareResultListener);
}
