/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/13                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;
import com.google.android.material.snackbar.Snackbar;
import com.visionpay.opt.pax.config.datastore.PayWaveAidDataStore;
import com.visionpay.opt.pax.config.datastore.PayWaveFloorLimitDataStore;
import com.visionpay.opt.pax.consts.ConfigUIKeyConst;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.bizentity.db.helper.PaywaveAidDbHelper;
import com.pax.bizentity.entity.clss.paywave.PayWaveInterFloorLimitBean;
import com.pax.bizentity.entity.clss.paywave.PaywaveAidBean;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.AmountEditTextPrefBuilder;
import com.pax.preflib.builder.PrefCategoryBuilder;
import com.pax.preflib.builder.StringEditTextPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

import java.util.List;

/**
 * PayWave contactless AID content page
 *
 * Settings -> Param -> PayWave Kernel Param -> AID
 */
@RouterService(interfaces = EmvParamBaseFragment.class, key = EmvRouterConst.CONFIG_PARAM_CLSS_PAYWAVE_AID)
public class EmvPayWaveAidContentFragment extends EmvParamBaseFragment {
    private String aid;

    @Override
    public void setParam(String param) {
        aid = param;
    }

    @NonNull
    @Override
    public String getFragmentTitle() {
        return aid;
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            PaywaveAidBean aidBean = PaywaveAidDbHelper.getInstance().findAID(aid);
            manager.setPreferenceDataStore(new PayWaveAidDataStore(aidBean)
                    .setRefreshCachedParamCallback(() -> Snackbar
                            .make(getListView(), "Need Refresh Cached EMV Param", Snackbar.LENGTH_LONG)
                            .setAction("REFRESH", v -> refreshCachedParam())
                            .show()));
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // APP Name
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PayWaveAidDataStore.APP_NAME, R.string.config_param_app_name)
                    .buildAndApply(source -> source.setSelectable(false)));

            // TTQ
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PayWaveAidDataStore.TTQ, R.string.config_param_ttq)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 8))
                    .build());

            // Security Capability
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PayWaveAidDataStore.SEC_CAP, R.string.config_param_sec_cap)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 2))
                    .build());

            // Limit
            if (aidBean != null) {
                List<PayWaveInterFloorLimitBean> floorLimitBeanList = aidBean.getInterWareFloorLimit();
                if (floorLimitBeanList != null && !floorLimitBeanList.isEmpty()) {
                    for (PayWaveInterFloorLimitBean floorLimitBean : floorLimitBeanList) {
                        PayWaveFloorLimitDataStore floorLimitDataStore = new PayWaveFloorLimitDataStore(floorLimitBean);

                        screen.addPreference(PrefCategoryBuilder
                                .newInstance(context,
                                        ConfigUIKeyConst.EMV_CONTACTLESS_PAYWAVE_LIMIT_CATEGORY
                                        + "_" + floorLimitBean.getTransType(),
                                        getString(R.string.config_param_trans_type, Byte.toString(floorLimitBean.getTransType())))
                                .addPreference(AmountEditTextPrefBuilder
                                        .newInstance(context, PayWaveFloorLimitDataStore.TRANS_LIMIT,
                                                R.string.config_param_trans_limit)
                                        .buildAndApply(source -> source.setPreferenceDataStore(floorLimitDataStore)))
                                .addPreference(AmountEditTextPrefBuilder
                                        .newInstance(context, PayWaveFloorLimitDataStore.FLOOR_LIMIT,
                                                R.string.config_param_floor_limit)
                                        .buildAndApply(source -> source.setPreferenceDataStore(floorLimitDataStore)))
                                .addPreference(AmountEditTextPrefBuilder
                                        .newInstance(context, PayWaveFloorLimitDataStore.CVM_LIMIT,
                                                R.string.config_param_cvm_limit)
                                        .buildAndApply(source -> source.setPreferenceDataStore(floorLimitDataStore)))
                                .setIconSpaceReserved(false)
                                .build());
                    }
                }
            }

            setPreferenceScreen(screen);
        }
    }
}
