<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/03/21                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/main_background"
    tools:context="com.visionpay.opt.pax.activity.TransActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:liftOnScroll="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/trans_toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/main_background"
            app:title="@string/emv_process_title"
            app:titleTextColor="@color/main_toolbar_content"
            app:titleCentered="true"
            app:contentInsetStart="@dimen/toolbar_content_to_screen_edge"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/trans_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <LinearLayout
            android:id="@+id/trans_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/page_content_horizontal_spacing"
            android:layout_marginEnd="@dimen/page_content_horizontal_spacing"
            android:paddingBottom="@dimen/scrollable_page_content_to_screen_bottom"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/trans_amount_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/page_content_to_screen_edge"
                android:layout_marginEnd="@dimen/page_content_to_screen_edge"
                android:layout_marginTop="@dimen/page_content_to_toolbar"
                app:cardElevation="0dp"
                app:cardCornerRadius="@dimen/card_corner_radius"
                app:strokeWidth="@dimen/card_stroke_width"
                app:strokeColor="@color/common_divider"
                app:cardBackgroundColor="@color/main_background">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/card_padding_vertical"
                    android:paddingHorizontal="@dimen/card_padding_horizontal">

                    <TextView
                        android:id="@+id/trans_amount_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/amount"
                        android:textColor="@color/main_text"
                        android:textSize="@dimen/text_medium_size"
                        android:fontFamily="sans-serif-medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/trans_amount_content"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/main_text"
                        android:textSize="@dimen/text_medium_size"
                        android:fontFamily="monospace"
                        android:gravity="end"
                        app:layout_constraintStart_toEndOf="@id/trans_amount_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/trans_amount_title"
                        app:layout_constraintBottom_toBottomOf="@id/trans_amount_title"
                        tools:text="$123.45"/>

                    <TextView
                        android:id="@+id/trans_type_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="@string/trans_type"
                        android:textColor="@color/main_text"
                        android:textSize="@dimen/text_medium_size"
                        android:fontFamily="sans-serif-medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/trans_amount_title"
                        app:layout_constraintBottom_toBottomOf="parent" />

                    <TextView
                        android:id="@+id/trans_type_content"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:textColor="@color/main_text"
                        android:textSize="@dimen/text_medium_size"
                        android:gravity="end"
                        app:layout_constraintStart_toEndOf="@id/trans_type_title"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/trans_type_title"
                        app:layout_constraintBottom_toBottomOf="@id/trans_type_title"
                        tools:text="Sale"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
        android:id="@+id/trans_retry_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_marginBottom="24dp"
        android:layout_marginEnd="@dimen/page_content_to_screen_edge"
        android:visibility="gone"
        android:text="@string/search_card_retry"
        android:textColor="@color/main_text_on_accent"
        app:icon="@drawable/ic_retry"
        app:iconTint="@color/main_text_on_accent" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>