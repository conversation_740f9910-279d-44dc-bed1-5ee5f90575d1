package com.visionpay.opt.pax.emv.online.interchange.messages.requests;

import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageSubtype;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeMessageType;
import com.visionpay.opt.pax.emv.online.interchange.messages.FinancialMessage;

public class InitRequest extends FinancialMessage {

    @Override
    protected byte[] internalGetMessage(){
        return super.internalGetMessage();
    }

    public InitRequest(String terminalID, long refNo, byte[] sessionID){
        super(InterchangeMessageType.IMT_TRX, InterchangeMessageSubtype.SMT_INIT1, refNo, terminalID, sessionID);
    }
}
