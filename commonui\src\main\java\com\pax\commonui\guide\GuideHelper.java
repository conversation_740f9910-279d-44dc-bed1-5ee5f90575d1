/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/08/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.commonui.guide;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import androidx.annotation.DrawableRes;
import com.pax.commonui.R;
import java.util.LinkedList;

/**
 * Copy from GuideHelper Library
 */
public class GuideHelper {

    private final Activity activity;

    private final LinkedList<TipPage> pages = new LinkedList<>();
    private TipPage currentPage;

    private Dialog baseDialog;
    public DialogInterface.OnDismissListener onDismissListener;

    private RelativeLayout layout;

    private boolean autoPlay;

    private static final int MIN = 2000;
    private static final int MAX = 6000;

    private final Handler handler = new Handler(Looper.getMainLooper()) {
        public void handleMessage(android.os.Message msg) {
            if (!pages.isEmpty()) {
                send();
            } else if (currentPage == null || currentPage.clickDoNext) {
                dismiss();
            }
        }
    };

    public GuideHelper(Activity activity) {
        super();
        this.activity = activity;
    }

    /**
     * 添加提示界面
     *
     * @param tipDatas 要显示在提示界面上的内容，可以设置多个
     * @return GuideHelper
     */
    public GuideHelper addPage(TipData... tipDatas) {
        return addPage(true, tipDatas);
    }

    /**
     * 添加提示界面
     *
     * @param clickDoNext 点击提示界面是否进入显示下一个界面，或者dismiss。如果设为 false，就要自己控制逻辑
     * @param tipDatas 要显示在提示界面上的内容，可以设置多个
     * @return GuideHelper
     */
    public GuideHelper addPage(boolean clickDoNext, TipData... tipDatas) {
        return addPage(clickDoNext, null, tipDatas);
    }

    /**
     * 添加提示界面
     *
     * @param clickDoNext 点击提示界面是否进入显示下一个界面，或者dismiss。如果设为 false，就要自己控制逻辑
     * @param onClickPageListener 点击界面的回调
     * @param tipDatas 要显示在提示界面上的内容，可以设置多个
     * @return GuideHelper
     */
    public GuideHelper addPage(boolean clickDoNext, View.OnClickListener onClickPageListener, TipData... tipDatas) {
        pages.add(new TipPage(clickDoNext, onClickPageListener, tipDatas));
        return this;
    }

    /**
     * 获取所有提示界面都被关闭后执行的回调
     *
     * @return 所有提示界面都被关闭后执行的回调
     */
    public DialogInterface.OnDismissListener getOnDismissListener() {
        return onDismissListener;
    }

    /**
     * 设置所有提示界面都被关闭后执行的回调
     *
     * @param onDismissListener 所有提示界面都被关闭后执行的回调
     */
    public void setOnDismissListener(DialogInterface.OnDismissListener onDismissListener) {
        this.onDismissListener = onDismissListener;
    }

    /**
     * 开始在界面上显示提示界面，默认自动播放提示
     *
     * @return GuideHelper
     */
    public GuideHelper show() {
        show(true);
        return this;
    }

    public View inflate(int layoutId) {
        return LayoutInflater.from(activity).inflate(layoutId, layout, false);
    }

    /**
     * 开始在界面上显示提示界面
     *
     * @param autoPlay 是否自动播放提示
     * @return GuideHelper
     */
    public GuideHelper show(boolean autoPlay) {
        this.autoPlay = autoPlay;
        //关闭dialog，移除handler消息
        dismiss();
        handler.removeCallbacksAndMessages(null);

        //创建dialog
        layout = new InnerChildRelativeLayout(activity);
        baseDialog = new Dialog(activity, R.style.commonuiGuideHelperDialog);

        //设置沉浸式状态栏
        WindowManager.LayoutParams localLayoutParams = baseDialog.getWindow().getAttributes();
        localLayoutParams.flags |= WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS;
        localLayoutParams.flags |= WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION;
        localLayoutParams.flags |= WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN;

        baseDialog.setContentView(layout);
        //设置dialog的窗口大小全屏
        baseDialog.getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        //dialog关闭的时候移除所有消息
        baseDialog.setOnDismissListener(dialog -> {
            handler.removeCallbacksAndMessages(null);
            if (onDismissListener != null) {
                onDismissListener.onDismiss(dialog);
            }
        });
        //显示
        baseDialog.show();
        startSend();
        return this;
    }

    /**
     * 显示提示界面，这里用view去post，是为了保证view已经显示，才能获取到view的界面画在提示界面上
     * 如果只有自定义view，那么就用handler post
     *
     * <AUTHOR> by yale
     * create at 2016/7/14 16:12
     */
    private void startSend() {
        View view = null;
        for (TipPage tipPage : pages) {
            for (TipData tp : tipPage.tipDatas) {
                if (tp.targetViews != null && tp.targetViews.length > 0) {
                    view = tp.targetViews[0];
                    break;
                }
            }
            if (view != null) {
                break;
            }
        }
        if (view != null) {
            view.post(() -> send());
        } else {
            handler.post(() -> send());
        }
    }

    private void send() {
        currentPage = pages.poll();
        showIm(layout, currentPage.tipDatas);
        if (autoPlay) {
            int d = currentPage.tipDatas.length * 1500;
            if (d < MIN) {
                d = 2000;
            } else if (d > MAX) {
                d = MAX;
            }
            handler.sendEmptyMessageDelayed(1, d);
        }
    }

    private void showIm(final RelativeLayout layout, TipData... tipDatas) {
        //移除掉之前所有的viwe
        layout.removeAllViews();
        //获取layout在屏幕上的位置
        int[] layoutOffset = new int[2];
        layout.getLocationOnScreen(layoutOffset);
        int imageViewId = 89598;
        //循环提示的数据列表
        for (TipData data : tipDatas) {
            imageViewId++;
            if (data.targetViews == null || data.targetViews.length == 0) {
                RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                switch (data.gravity & Gravity.HORIZONTAL_GRAVITY_MASK) {
                    case Gravity.CENTER_HORIZONTAL:
                        layoutParams.addRule(RelativeLayout.CENTER_HORIZONTAL);
                        break;
                    case Gravity.RIGHT:
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, imageViewId);
                        break;
                    case Gravity.LEFT:
                    default:
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT, imageViewId);
                        break;
                }
                switch (data.gravity & Gravity.VERTICAL_GRAVITY_MASK) {
                    case Gravity.CENTER_VERTICAL:
                        layoutParams.addRule(RelativeLayout.CENTER_VERTICAL, imageViewId);
                        break;
                    case Gravity.TOP:
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_TOP, imageViewId);
                        break;
                    case Gravity.BOTTOM:
                    default:
                        layoutParams.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, imageViewId);
                        break;
                }
                View tipView;
                if (data.tipView != null) {
                    tipView = data.tipView;
                } else {
                    Bitmap bitmap = BitmapFactory.decodeResource(activity.getResources(), data.tipImageResourceId);
                    ImageView imageView = new ImageView(activity);
                    imageView.setImageBitmap(bitmap);
                    tipView = imageView;
                }
                if (data.onClickListener != null) {
                    tipView.setOnClickListener(data.onClickListener);
                }
                layoutParams.leftMargin += data.offsetX;
                layoutParams.rightMargin -= data.offsetX;
                layoutParams.topMargin += data.offsetY;
                layoutParams.bottomMargin -= data.offsetY;
                layout.addView(tipView, layoutParams);
                continue;
            }

            //循环需要提示的view
            View[] views = data.targetViews;
            int[] location = new int[2];
            Rect rect = null;
            for (View view : views) {
                if (view.getVisibility() != View.VISIBLE) {
                    continue;
                }

                //获取view在屏幕的位置，后面会用这个位置绘制到提示的dialog。确保位置完美覆盖在view上
                view.getLocationOnScreen(location);
                //这里避免dialog不是全屏，导致view的绘制位置不对应
                location[1] -= layoutOffset[1];

                //获取view的宽高
                int vWidth = view.getMeasuredWidth();
                int vHeight = view.getMeasuredHeight();

                //如果宽高都小于等于0，再measure试下获取
                if (vWidth <= 0 || vHeight <= 0) {
                    ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
                    view.measure(layoutParams.width, layoutParams.height);
                    vWidth = view.getMeasuredWidth();
                    vHeight = view.getMeasuredHeight();
                }

                if (vWidth <= 0 || vHeight <= 0) {
                    continue;
                }

                if (data.needDrawView) {
                    //通过getDrawingCache的方式获取view的视图缓存
                    view.setDrawingCacheEnabled(true);
                    view.buildDrawingCache();
                    Bitmap bitmap = view.getDrawingCache();
                    if (bitmap != null) {
                        bitmap = Bitmap.createBitmap(bitmap);
                    } else {
                        //如果获取不到，则用创建一个view宽高一样的bitmap用canvas把view绘制上去
                        bitmap = Bitmap.createBitmap(vWidth, vHeight, Bitmap.Config.ARGB_8888);
                        Canvas canvas = new Canvas(bitmap);
                        view.draw(canvas);
                    }
                    //释放试图的缓存
                    view.setDrawingCacheEnabled(false);
                    view.destroyDrawingCache();

                    //把需要提示的view的视图设置到imageView上显示
                    ImageView imageView = new ImageView(activity);
                    imageView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
                    imageView.setImageBitmap(bitmap);
                    imageView.setId(imageViewId);

                    //如果使用者还配置了提示view的背景颜色，那么也设置显示
                    if (data.viewBg != null) {
                        imageView.setBackground(data.viewBg);
                    }

                    if (data.onClickListener != null)
                        imageView.setOnClickListener(data.onClickListener);


                    RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                            ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                    params.leftMargin = location[0];
                    params.topMargin = location[1];
                    layout.addView(imageView, params);
                }

                if (rect == null) {
                    rect = new Rect(location[0], location[1], location[0] + vWidth, location[1] + vHeight);
                } else {
                    if (rect.left > location[0]) {
                        rect.left = location[0];
                    }
                    if (rect.right < location[0] + vWidth) {
                        rect.right = location[0] + vWidth;
                    }
                    if (rect.top > location[1]) {
                        rect.top = location[1];
                    }
                    if (rect.bottom < location[1] + vHeight) {
                        rect.bottom = location[1] + vHeight;
                    }
                }
            }
            if (rect == null) {
                continue;
            }
            int showViewHeight;
            int showViewWidth;
            View tipView;
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            if (data.tipView != null) {
                tipView = data.tipView;
                tipView.measure(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                showViewWidth = tipView.getMeasuredWidth();
                showViewHeight = tipView.getMeasuredHeight();
            } else {
                Bitmap bitmap = BitmapFactory.decodeResource(activity.getResources(), data.tipImageResourceId);
                showViewHeight = bitmap.getHeight();
                showViewWidth = bitmap.getWidth();
                ImageView tip = new ImageView(activity);
                layoutParams.width = showViewWidth;
                layoutParams.height = showViewHeight;
                tip.setImageBitmap(bitmap);
                tipView = tip;
            }
            switch (data.gravity & Gravity.HORIZONTAL_GRAVITY_MASK) {
                case Gravity.CENTER_HORIZONTAL:
                    layoutParams.rightMargin = rect.width() / 2 - showViewWidth / 2;
                    layoutParams.addRule(RelativeLayout.ALIGN_RIGHT, imageViewId);
                    break;
                case Gravity.RIGHT:
                    layoutParams.addRule(RelativeLayout.RIGHT_OF, imageViewId);
                    break;
                case Gravity.LEFT:
                default:
                    layoutParams.rightMargin += rect.width();
                    layoutParams.addRule(RelativeLayout.ALIGN_RIGHT, imageViewId);
                    break;
            }
            switch (data.gravity & Gravity.VERTICAL_GRAVITY_MASK) {
                case Gravity.CENTER_VERTICAL:
                    layoutParams.topMargin = rect.height() / 2 - showViewHeight / 2;
                    layoutParams.addRule(RelativeLayout.ALIGN_TOP, imageViewId);
                    break;
                case Gravity.TOP:
                    layoutParams.bottomMargin = rect.height();
                    layoutParams.addRule(RelativeLayout.ALIGN_BOTTOM, imageViewId);
                    break;
                case Gravity.BOTTOM:
                default:
                    layoutParams.topMargin = rect.height();
                    layoutParams.addRule(RelativeLayout.ALIGN_TOP, imageViewId);
                    break;
            }
            layoutParams.leftMargin += data.offsetX;
            layoutParams.rightMargin -= data.offsetX;
            layoutParams.topMargin += data.offsetY;
            layoutParams.bottomMargin -= data.offsetY;
            layout.addView(tipView, layoutParams);
        }

        layout.setOnClickListener(v -> {
            if (currentPage != null && currentPage.clickDoNext) {
                if (pages.isEmpty()) {
                    dismiss();
                } else {
                    handler.removeCallbacksAndMessages(null);
                    handler.sendEmptyMessage(1);
                }
            }
            if (currentPage != null && currentPage.onClickPageListener != null) {
                currentPage.onClickPageListener.onClick(v);
            }
        });
    }

    /**
     * 显示下一界面
     */
    public void nextPage() {
        if (pages.isEmpty()) {
            dismiss();
        } else {
            handler.removeCallbacksAndMessages(null);
            handler.sendEmptyMessage(1);
        }
    }

    /**
     * 关闭所有提示界面
     */
    public void dismiss() {
        if (baseDialog != null)
            baseDialog.dismiss();
        baseDialog = null;
    }

    public static class TipData {
        View[] targetViews;
        int gravity;
        boolean needDrawView = true;
        private static final int DEFAULT_GRAVITY = Gravity.BOTTOM | Gravity.CENTER;

        View.OnClickListener onClickListener;
        private int offsetX;
        private int offsetY;
        private Drawable viewBg;

        private int tipImageResourceId;
        private View tipView;

        /**
         * 创建提示界面上的内容
         *
         * @param tipView 提示 View
         * @param targetViews 被提示的 View。在提示界面中，除了被提示的 View，其他的 View 都会加上一层蒙版
         */
        public TipData(View tipView, View... targetViews) {
            this(tipView, DEFAULT_GRAVITY, targetViews);
        }

        /**
         * 创建提示界面上的内容
         *
         * @param tipView 提示 View
         * @param gravity 怎样对齐被提示的 View
         * @param targetViews 被提示的 View。在提示界面中，除了被提示的 View，其他的 View 都会加上一层蒙版
         */
        public TipData(View tipView, int gravity, View... targetViews) {
            this.gravity = gravity;
            this.tipView = tipView;
            this.targetViews = targetViews;
        }

        /**
         * 创建提示界面上的内容
         *
         * @param tipImageResourceId 提示图片资源 ID
         * @param targetViews 被提示的 View。在提示界面中，除了被提示的 View，其他的 View 都会加上一层蒙版
         */
        public TipData(@DrawableRes int tipImageResourceId, View... targetViews) {
            this(tipImageResourceId, DEFAULT_GRAVITY, targetViews);
        }

        /**
         * 创建提示界面上的内容
         *
         * @param tipImageResourceId 提示图片资源 ID
         * @param gravity 怎样对齐被提示的 View
         * @param targetViews 被提示的 View。在提示界面中，除了被提示的 View，其他的 View 都会加上一层蒙版
         */
        public TipData(@DrawableRes int tipImageResourceId, int gravity, View... targetViews) {
            super();
            this.targetViews = targetViews;
            this.gravity = gravity;
            this.tipImageResourceId = tipImageResourceId;
        }

        /**
         * 设置提示内容的位置
         *
         * @param gravity 怎样对齐被提示的 View
         * @return TipData
         */
        public TipData setLocation(int gravity) {
            this.gravity = gravity;
            return this;
        }

        /**
         * 设置提示内容的位置
         *
         * @param gravity 怎样对齐被提示的 View
         * @param offsetX X 轴上的偏移
         * @param offsetY Y 轴上的偏移
         * @return TipData
         */
        public TipData setLocation(int gravity, int offsetX, int offsetY) {
            this.gravity = gravity;
            this.offsetX = offsetX;
            this.offsetY = offsetY;
            return this;
        }

        /**
         * 设置提示内容的位置
         *
         * @param offsetX X 轴上的偏移
         * @param offsetY Y 轴上的偏移
         * @return TipData
         */
        public TipData setLocation(int offsetX, int offsetY) {
            this.offsetX = offsetX;
            this.offsetY = offsetY;
            return this;
        }

        /**
         * 设置被提示 View 的背景，一般来说只有当被提示 View 的背景是透明的时候，这个设置才有用
         *
         * @param viewBg 被提示 View 的背景
         * @return TipData
         */
        public TipData setViewBg(Drawable viewBg) {
            this.viewBg = viewBg;
            return this;
        }

        /**
         * 设置是否需要绘制被提示 View
         *
         * @param needDrawView 是否需要绘制被提示 View，如果是 true，就会重新绘制这个被提示 View。
         *      需要注意，只有当传入 true 时，{@code setViewBg()} 才会有效，点击被提示 View 也才会响应
         *      TipData 中通过 {@code setOnClickListener()} 设置的监听器。
         *      默认的值为 true。
         * @return TipData
         */
        public TipData setNeedDrawView(boolean needDrawView) {
            this.needDrawView = needDrawView;
            return this;
        }

        /**
         * 设置点击事件的监听器
         *
         * @param onClickListener 点击事件监听器。
         *      <ul>
         *          <li>点击提示内容会执行该监听器；</li>
         *          <li>如果在添加提示界面（{@code addPage()}）的时候设置 {@code clickDoNext} 为 true，那么点击屏幕上其他位置同样也会执行该监听器；</li>
         *          <li>如果没有通过 {@code setNeedDrawView()} 设置为 false，那么点击被提示 View 时，同样也会执行该监听器。</li>
         *      </ul>
         * @return TipData
         */
        public TipData setOnClickListener(View.OnClickListener onClickListener) {
            this.onClickListener = onClickListener;
            return this;
        }
    }

    private static class TipPage {
        private boolean clickDoNext = true;
        private final TipData[] tipDatas;
        private final View.OnClickListener onClickPageListener;

        public TipPage(boolean clickDoNext, View.OnClickListener onClickPageListener, TipData[] tipDatas) {
            this.clickDoNext = clickDoNext;
            this.tipDatas = tipDatas;
            this.onClickPageListener = onClickPageListener;
        }
    }
}
