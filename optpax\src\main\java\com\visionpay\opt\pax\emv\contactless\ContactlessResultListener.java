/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.contactless;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EmvTag;
import com.visionpay.opt.pax.emv.IEmvService;
import com.pax.bizentity.entity.SearchMode;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvbase.utils.EmvResultUtils;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.utils.EmvTagUtils;
import com.pax.emvservice.export.IEmvContactlessService;
import com.pax.emvservice.export.contactless.IContactlessResultListener;

import java.util.List;

/**
 * Contactless Result Process Listener
 *
 * 这个类实现了 IContactlessResultListener 接口，这个接口是 emvservice:export 模块定义的。emvservice 提供了
 * 检查 EMV 流程结果的代码，只需要设置 IContactlessResultListener 的实现类并执行
 * IEmvContactlessService.checkClsResult 方法即可。emvservice 会根据 EMV 流程的执行结果来执行该 listener 中
 * 的相应方法。
 *
 * 由于 SmartFuel PAX 中的结果需要展示在 Activity 中，所以这里主要是执行相应的回调来更新 UI。
 */
public class ContactlessResultListener implements IContactlessResultListener {
    private static final String TAG = "ContactlessResultListener";

    @NonNull
    private final IEmvContactlessService emv;
    @NonNull
    private final List<CvmType> cvmTypeList;
    @NonNull
    private final List<EmvTag> emvTagList;
    @Nullable
    private final IEmvService.ErrorCallback errorCallback;
    @Nullable
    private final IEmvService.CompleteCallback completeCallback;
    @Nullable
    private final ContactlessService.RetryCallback retryCallback;

    public ContactlessResultListener(@NonNull IEmvContactlessService emv,
            @NonNull List<CvmType> cvmTypeList,
            @NonNull List<EmvTag> emvTagList,
            @Nullable IEmvService.ErrorCallback errorCallback,
            @Nullable IEmvService.CompleteCallback completeCallback,
            @Nullable ContactlessService.RetryCallback retryCallback) {
        this.emv = emv;
        this.cvmTypeList = cvmTypeList;
        this.emvTagList = emvTagList;
        this.errorCallback = errorCallback;
        this.completeCallback = completeCallback;
        this.retryCallback = retryCallback;
    }

    private void saveEmvTag() {
        EmvTagUtils.getCommonTags(emv);
    }

    /**
     * See Phone, 用户需要在手机上操作，然后再次拍卡。此时 EMV 流程不能终止，并且应该先提示用户移开手机（否则直接就
     * 执行后续流程了）。
     */
    @Override
    public void seePhone() {
        LogUtils.d(TAG, "seePhone");
        if (retryCallback != null) {
            retryCallback.onRetry(SearchMode.INTERNAL_WAVE,
                    "Stop",
                    "Please See Phone",
                    true);
        }
    }

    /**
     * 使用插卡交易方式重试。
     */
    @Override
    public void tryAnotherInterface() {
        LogUtils.d(TAG, "tryAnotherInterface");
        if (retryCallback != null) {
            retryCallback.onRetry(SearchMode.INSERT,
                    "Running...",
                    "Stop, try another interface",
                    true);
        }
    }

    /**
     * 重试。有可能是需要重新选择 APP。
     */
    @Override
    public void tryAgain() {
        LogUtils.d(TAG, "tryAgain");
        if (retryCallback != null) {
            retryCallback.onRetry(SearchMode.INTERNAL_WAVE,
                    "Stop",
                    "Try again...",
                    false);
        }
    }

    /**
     * 联机拒绝交易。一般是服务器返回了非 00 的 Response Code 导致的。
     *
     * SmartFuel PAX 中并不具备实际的联机功能，Online Denied 是用户自己选择的，Response Code 也是用户自己输入的，因
     * 此并不会显示错误信息，而是去显示了 CVM Type 和 EMV Tag。在实际的应用中，应该提示错误并终止交易。
     */
    @Override
    public void onlineDenied() {
        LogUtils.d(TAG, "onlineDenied");
        if (completeCallback != null) {
            saveEmvTag();
            completeCallback.onComplete(false, TransResultEnum.RESULT_ONLINE_DENIED,
                    cvmTypeList, emvTagList);
        }
    }

    /**
     * 离线拒绝交易。这很可能是在联机之前就发生了某些错误，导致 EMV 流程被异常终止。因此对于这种情况，SmartFuel PAX 会
     * 显示错误信息。在实际的应用中也应该提示错误信息。
     *
     * @param resultCode 错误原因代码
     */
    @Override
    public void offlineDenied(int resultCode) {
        LogUtils.d(TAG, "offlineDenied");
        emv.setTlv(0x8A, "Z1".getBytes());
        if (errorCallback != null) {
            errorCallback.onError("Offline Denied",
                    "Result Code: " + resultCode + ", " + EmvResultUtils.getMessage(resultCode));
        }
    }

    /**
     * 联机批准，但是卡片拒绝交易。如果二次拍卡时返回失败结果，那么就会执行该回调。SmartFuel PAX 中并没有提示错误，而是显
     * 示了 CVM Type 和 EMV Tag。在实际的应用中，应该要提示错误信息并终止交易。
     *
     * @param resultCode 错误原因代码
     */
    @Override
    public void onlineCardDenied(int resultCode) {
        LogUtils.d(TAG, "onlineCardDenied");
        saveEmvTag();
        if (completeCallback != null) {
            completeCallback.onComplete(false, TransResultEnum.RESULT_ONLINE_CARD_DENIED,
                    cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * 离线批准交易，无需联机。
     *
     * @param needSignature needSignature 是否需要签名。这里仅表示 EMV 内核的 CVM Type 是否要求用户签名。
     */
    @Override
    public void offlineApproved(boolean needSignature) {
        LogUtils.d(TAG, "offlineApproved");
        if (needSignature) {
            cvmTypeList.add(new CvmType(R.drawable.ic_signature, ResourceUtil.getString(R.string.cvm_signature)));
        }
        emv.setTlv(0x8A, "Y1".getBytes());
        saveEmvTag();
        if (completeCallback != null) {
            completeCallback.onComplete(false, TransResultEnum.RESULT_OFFLINE_APPROVED,
                    cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * 联机批准。
     *
     * @param needSignature 是否需要签名。这里仅表示 EMV 内核的 CVM Type 是否要求用户签名。
     */
    @Override
    public void onlineApproved(boolean needSignature) {
        LogUtils.d(TAG, "onlineApproved");
        if (needSignature) {
            cvmTypeList.add(new CvmType(R.drawable.ic_signature, ResourceUtil.getString(R.string.cvm_signature)));
        }
        saveEmvTag();
        if (completeCallback != null) {
            completeCallback.onComplete(false, TransResultEnum.RESULT_ONLINE_APPROVED,
                    cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * 联机失败，交易被拒绝。
     *
     * 和接触式 EMV 流程不太一样的是，非接的 EMV 在联机失败以后就拒绝交易了。不过在 SmartFuel PAX 中联机是否成功是用户
     * 自己选择的，所以这里没有提示错误，而是显示了 CVM Type 和 EMV Tag。在实际的应用中，应该要提示错误信息并终止
     * 交易。
     */
    @Override
    public void onlineFailed() {
        LogUtils.d(TAG, "onlineFailed");
        saveEmvTag();
        if (completeCallback != null) {
            completeCallback.onComplete(false, TransResultEnum.RESULT_ONLINE_FAILED,
                    cvmTypeList,
                    emvTagList);
        }
    }

    /**
     * Simple Flow 结束。
     *
     * Simple Flow 是在流程走到 Confirm 阶段、输入 PIN 和联机之前就终止，不执行完整的 EMV 交易流程。在实际的应用
     * 中可能有些交易类型需要这样处理，不过 SmartFuel PAX 不支持，所以直接显示了错误信息。
     *
     * 在预处理阶段可以设置当前 EMV 交易是否为 Simple Flow。
     */
    @Override
    public void simpleFlowEnd() {
        LogUtils.d(TAG, "simpleFlowEnd");
        if (errorCallback != null) {
            errorCallback.onError("Simple Flow End",
                    "SmartFuel PAX not support this result");
        }
    }
}
