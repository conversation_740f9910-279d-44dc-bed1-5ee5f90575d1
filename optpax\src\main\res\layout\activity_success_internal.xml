<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?attr/fullscreenBackgroundColor"
    android:theme="@style/ThemeOverlay.OPTPAX.FullscreenContainer"
    android:padding="20dp"
    android:gravity="center">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Please wait while we process your order..."
        android:textSize="35dp"
        android:gravity="center"
        android:layout_marginBottom="30dp"
        android:layout_marginTop="30dp"
        android:id="@+id/txtText"/>

</LinearLayout>