package com.visionpay.opt.pax.emv.online.interchange.enums;

public enum InterchangeMessageType {
    IMT_LOGON((byte)0x01),
    IMT_TRX((byte)0x02);

    public final byte value;

    InterchangeMessageType(byte i) {
        this.value = i;
    }

    public static InterchangeMessageType get(int value)
    {
        InterchangeMessageType[] As = InterchangeMessageType.values();
        for(int i = 0; i < As.length; i++)
        {
            if(As[i].value == value)
                return As[i];
        }
        return null;
    }
}
