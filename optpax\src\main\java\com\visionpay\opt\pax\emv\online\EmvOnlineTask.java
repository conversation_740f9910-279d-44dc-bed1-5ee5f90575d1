/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/01                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.online;

import android.app.Activity;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.pax.bizentity.entity.SearchMode;
import com.pax.bizentity.entity.TransData;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.entity.EOnlineResult;
import com.pax.emvbase.process.entity.IssuerRspData;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.transaction.ITransactionService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.emv.online.interchange.Interchange;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeTransactionType;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.AuthorizeResponse;
import com.visionpay.opt.pax.emv.online.interchange.utils.ICCBuilder;
import com.visionpay.opt.pax.utils.DialogUtils;
import com.visionpay.opt.pax.utils.EmvTagUtils;
import com.pax.emvservice.export.IEmvBase;

/**
 * EMV Online Task
 */
public class EmvOnlineTask implements IOnlineTask<IEmvBase> {
    private static final String TAG = "EmvOnlineTask";

    //private final ConditionVariable cv = new ConditionVariable();

    private ISendingOnlineCallback callback;

    private long transId = 0;

    public EmvOnlineTask setSendindOnlineCallback(ISendingOnlineCallback callback){
        this.callback = callback;
        return this;
    }

    @Override
    public OnlineResultWrapper start(@NonNull IEmvBase emv, long amount, String terminalId, int detectResult, String reference) {
        LogUtils.d(TAG, "============ Emv Online Start ============");
        //cv.close();
        OnlineResultWrapper result = new OnlineResultWrapper();
        IssuerRspData rspData = new IssuerRspData();
        result.setIssuerRspData(rspData);

        // Show Dialog
        /*App.getApp().runOnUiThread(() -> {
            new SingleSelectionDialog()
                    .setTitle(R.string.online_result)
                    .addChoice(R.string.online_approved)
                    .addChoice(R.string.online_denied)
                    .addChoice(R.string.online_failed)
                    .setOnItemClickListener((dialog, which) -> {
                        if (which == 0) {
                            // Approved
                            result.setResultCode(EOnlineResult.APPROVE.getResultCode());
                            result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_APPROVED);
                            rspData.setOnlineResult(EOnlineResult.APPROVE.getEmvOnlineResult());
                            rspData.setRespCode("00".getBytes());
                            emv.setTlv(0x8A, "00".getBytes());
                            dialog.dismiss();
                            setRspIccDataDialog(cv, emv, rspData);
                        } else if (which == 1) {
                            // Denied
                            result.setResultCode(EOnlineResult.DENIAL.getResultCode());
                            result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_DENIED);
                            rspData.setOnlineResult(EOnlineResult.DENIAL.getEmvOnlineResult());
                            dialog.dismiss();
                            setRspCodeDialog(cv, emv, rspData);
                        } else if (which == 2) {
                            // Failed
                            result.setResultCode(EOnlineResult.FAILED.getResultCode());
                            result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_FAILED);
                            rspData.setOnlineResult(EOnlineResult.FAILED.getEmvOnlineResult());
                            dialog.dismiss();
                            cv.open();
                        }
                    }).show();
        });
        cv.block();
        cv.close();*/

        Activity activity = ActivityStack.getInstance().top();
        if (activity instanceof FragmentActivity) {
            DialogUtils.createProcessingDialog((FragmentActivity) activity, "EMV Processing",
                    "Process online result, please wait...");
        }

        byte[] PIN = emv.getPin();

        InterchangeTransactionType currentTransactionType;
        if(detectResult == SearchMode.INSERT)
            currentTransactionType = InterchangeTransactionType.INSERT;
        else if(detectResult == SearchMode.SWIPE)
            currentTransactionType = InterchangeTransactionType.SWIPE;
        else
            currentTransactionType = InterchangeTransactionType.TAP;

        callback.onStart();
        AuthorizeResponse response = sendForOnlineAuthorization(emv, reference, amount, PIN, currentTransactionType, terminalId);
        respCodeProcess(result, emv, rspData, response, PIN.length > 0);
        //result.setResultCode(EOnlineResult.APPROVE.getResultCode());
        //result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_APPROVED);
        //rspData.setOnlineResult(EOnlineResult.APPROVE.getEmvOnlineResult());
        //callback.onComplete(true, PIN.length > 0, "MER001", "11111", "Transaction Approved", "00");

        LogUtils.d(TAG, "============ Emv Online End ============");
        return result;
    }

    private AuthorizeResponse sendForOnlineAuthorization(IEmvBase emv, String transReference, long amount,
                                                         byte[] PIN, InterchangeTransactionType currentTransactionType, String terminalId)
    {
        byte[] AID = emv.getTlv(0x4F);
        byte[] PAN = ConvertUtils.strToBcdPaddingRight(emv.getPan());
        int cardSequenceNumber = ConvertUtils.bcd2Int(emv.getTlv(0x5F34));
        String track2 = emv.getTrack2Data();
        byte[] ICC = ICCBuilder.build(emv, AID);
        String aidStr = ConvertUtils.bcd2Str(AID);

        byte currentAccountType = 0x30;
        if(currentTransactionType != InterchangeTransactionType.SWIPE) {
            if (aidStr.contains("A000000384")) {
                if (aidStr.contains("A00000038410"))
                    currentAccountType = 0x10;
                else
                    currentAccountType = 0x20;
            }
        } else {
            if (track2.lastIndexOf("6", 0) == 0)
                currentAccountType = 0x10;
        }

        try {
            //String strPin = ConvertUtils.bcd2Str(PIN);
            transId = transInit(transReference, amount, emv.getPan(), PIN.length > 0, track2, aidStr, new String(emv.getTlv(TagsTable.APP_LABEL)), emv.getExpireDate(), ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)) + "", cardSequenceNumber + "", currentTransactionType == InterchangeTransactionType.INSERT ? TransData.EnterMode.INSERT : (currentTransactionType == InterchangeTransactionType.TAP ? TransData.EnterMode.CLSS : TransData.EnterMode.SWIPE));
            AuthorizeResponse response = Interchange.onlineAuthorizeCard(transId, PAN, amount, cardSequenceNumber, track2, PIN, AID, ICC, currentAccountType, currentTransactionType, terminalId);
            //AuthorizeResponse response = new AuthorizeResponse(new byte[0], "MERCH1");
            //response.HostResponse = "00";
            //response.AuthCode = new byte[0];
            //response.STAN = 1;
            return response;
        }
        catch (Exception e){
            e.printStackTrace();
            Log.e(TAG, e.getMessage());
            callback.onError();
        }
        return null;
    }

    private long transInit(String reference, long amount, String pan, boolean hasPin, String track2, String aid, String appName, String expDate, String atc, String cardSerialNo, TransData.EnterMode enterMode){
        ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
        return transactionService.transInit(reference, Long.toString(amount), pan, hasPin, track2, aid, appName, expDate, atc, cardSerialNo, enterMode).getId();
    }

    private void transUpdate(String authCode, String emvResult, TransData.ReversalStatus reversalStatus, String responseCode, String issuerCode, long stan){
        ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
        transactionService.transUpdate(transId, authCode, emvResult, reversalStatus, responseCode, issuerCode, stan);
    }

    private void respCodeProcess(
            OnlineResultWrapper result,
            @NonNull IEmvBase emv,
            @NonNull IssuerRspData rspData,
            @NonNull AuthorizeResponse response,
            boolean hasPin){
        if(response != null) {
            TransData.ReversalStatus reversalStatus = TransData.ReversalStatus.NORMAL;
            if(response.isError()) {
                result.setResultCode(EOnlineResult.FAILED.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_FAILED);
                rspData.setOnlineResult(EOnlineResult.FAILED.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.PENDING;
                transUpdate(new String(response.AuthCode), result.getResultCode() + "", reversalStatus, response.HostResponse, response.MerchantId, response.STAN);
                callback.onError();
                return;
            }
            if(response.isTimeout()) {
                result.setResultCode(EOnlineResult.DENIAL.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_DENIED);
                rspData.setOnlineResult(EOnlineResult.DENIAL.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.PENDING;
                callback.onTimeOut();
            }
            if(response.isApproved()) {
                result.setResultCode(EOnlineResult.APPROVE.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_APPROVED);
                rspData.setOnlineResult(EOnlineResult.APPROVE.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.NORMAL;
                //rspData.setRespCode("00".getBytes());
                //emv.setTlv(0x8A, "00".getBytes());
                callback.onComplete(response.isApproved(), hasPin, response.MerchantId, response.STAN + "", response.getGatwayResponse(), response.HostResponse, new String(response.AuthCode));
            }
            if(response.isDecline()) {
                result.setResultCode(EOnlineResult.DENIAL.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_DENIED);
                rspData.setOnlineResult(EOnlineResult.DENIAL.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.PENDING;
                callback.onComplete(response.isApproved(), hasPin, response.MerchantId, response.STAN + "", response.getGatwayResponse(), response.HostResponse, new String(response.AuthCode));
            }

            LogUtils.d(TAG, "Response Code: " + response.HostResponse);
            rspData.setRespCode(response.HostResponse.getBytes());
            emv.setTlv(0x8A, response.HostResponse.getBytes());

            // Auth Data
            byte[] value91 = response.IssuerDataForAuth;
            if (value91 != null && value91.length > 0) {
                emv.setTlv(0x91, value91);
                rspData.setAuthData(value91);
            }
            // Script
            byte[] value71 = response.IssuerScriptTemplate1;
            if (value71 != null && value71.length > 0) {
                emv.setTlv(0x71, value71);
            }
            byte[] value72 = response.IssuerScriptTemplate2;
            if (value72 != null && value72.length > 0) {
                emv.setTlv(0x72, value72);
            }
            byte[] script = EmvTagUtils.combine7172(value71, value72);
            if (script != null && script.length > 0) {
                rspData.setScript(script);
            }

            transUpdate(new String(response.AuthCode), result.getResultCode() + "", reversalStatus, response.HostResponse, response.MerchantId, response.STAN);
        }
        else {
            result.setResultCode(EOnlineResult.FAILED.getResultCode());
            result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_FAILED);
            rspData.setOnlineResult(EOnlineResult.FAILED.getEmvOnlineResult());
            TransData.ReversalStatus reversalStatus = TransData.ReversalStatus.PENDING;
            transUpdate("", result.getResultCode() + "", reversalStatus, "", "", 0);
            callback.onError();
        }
    }

    /*@MainThread
    private void setRspCodeDialog(
            @NonNull ConditionVariable cv,
            @NonNull IEmvBase emv,
            @NonNull IssuerRspData rspData) {
        new ContentInputDialog()
                .setTitle(R.string.online_rsp_code)
                .setDialogCancelable(false)
                .setTouchOutsideCancelable(false)
                .setPositiveButton(R.string.dialog_next, null, (dialog, button, editText, input) -> {
                    if (input != null) {
                        String content = input.toString();
                        if (!content.isEmpty()) {
                            LogUtils.d(TAG, "Response Code: " + content);
                            rspData.setRespCode(content.getBytes());
                            emv.setTlv(0x8A, content.getBytes());
                        }
                    }
                    dialog.dismissAllowingStateLoss();
                    setRspIccDataDialog(cv, emv, rspData);
                })
                .show(ActivityStack.getInstance().top(), "Response Code Dialog");
    }

    @MainThread
    private void setRspIccDataDialog(
            @NonNull ConditionVariable cv,
            @NonNull IEmvBase emv,
            @NonNull IssuerRspData rspData) {
        new ContentInputDialog()
                .setTitle(R.string.online_rsp_icc_data)
                .setNegativeButton(R.string.dialog_clear, null, (dialog, button, editText, input) -> {
                    if (editText != null) {
                        editText.setText("");
                    }
                })
                .setPositiveButton(R.string.dialog_done, null, (dialog, button, editText, input) -> {
                    if (input != null) {
                        String content = input.toString();
                        if (!content.isEmpty()) {
                            unpackRspContent(cv, emv, content, rspData);
                        }
                    }
                    dialog.dismissAllowingStateLoss();
                    cv.open();
                })
                .show(ActivityStack.getInstance().top(), "Response ICC Data Dialog");
    }

    private void unpackRspContent(
            @NonNull ConditionVariable cv,
            @NonNull IEmvBase emv,
            @NonNull String content,
            @NonNull IssuerRspData rspData) {
        LogUtils.d(TAG, "Unpack Rsp Content");
        byte[] rsp = ConvertUtils.strToBcdPaddingLeft(content);
        try {
            ITlv tlv = GL.getGL().getPacker().getTlv();
            ITlv.ITlvDataObjList list = tlv.unpack(rsp);
            // Auth Data
            byte[] value91 = list.getValueByTag(0x91);
            if (value91 != null && value91.length > 0) {
                emv.setTlv(0x91, value91);
                rspData.setAuthData(value91);
            }
            // Script
            byte[] value71 = list.getValueByTag(0x71);
            if (value71 != null && value71.length > 0) {
                emv.setTlv(0x71, value71);
            }
            byte[] value72 = list.getValueByTag(0x72);
            if (value72 != null && value72.length > 0) {
                emv.setTlv(0x72, value72);
            }
            byte[] script = EmvTagUtils.combine7172(value71, value72);
            if (script != null && script.length > 0) {
                rspData.setScript(script);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "unpack error", e);
        } finally {
            cv.open();
        }
    }*/
}
