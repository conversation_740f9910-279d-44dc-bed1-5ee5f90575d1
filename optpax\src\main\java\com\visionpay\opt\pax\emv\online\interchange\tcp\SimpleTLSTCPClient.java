package com.visionpay.opt.pax.emv.online.interchange.tcp;

import java.io.IOException;
import java.util.Calendar;

public class SimpleTLSTCPClient extends TLSTCPClient {

    private static SimpleTLSTCPClient simpleTLSTCPClient;

    public static SimpleTLSTCPClient getInstance(String hostName, String hostIP, String port){
        if(simpleTLSTCPClient == null){
            simpleTLSTCPClient = new SimpleTLSTCPClient(hostName, hostIP, port);
        }
        if(!simpleTLSTCPClient.host.equals(hostName) || !simpleTLSTCPClient.ip.equals(hostIP) || !simpleTLSTCPClient.port.equals(port)) {
            simpleTLSTCPClient = new SimpleTLSTCPClient(hostName, hostIP, port);
        }
        return simpleTLSTCPClient;
    }

    private SimpleTLSTCPClient(String hostName, String hostIP, String port) {
        super(hostName, hostIP, port);
    }

    @Override
    protected boolean threadLoopMethod(){
        java.lang.Thread.yield();
        return true;
    }

    public byte[] blockReceive(int timeout) {
        byte[] message = null;
        long start_ms = Calendar.getInstance().getTimeInMillis();

        while (Calendar.getInstance().getTimeInMillis() - start_ms < timeout)
        {
            try {
                message = tryReceive(10);
                if (message.length > 0)
                    break;
            }catch (IOException e)
            {}
            java.lang.Thread.yield();
        }

        return message;
    }
}

