/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/15                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.emvlib.base.consts;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2022/4/15
 */
public class EmvKernelConst {
    public static final String EMV = "EMV";
    public static final String AMEX = "AMEX";
    public static final String DPAS = "DPAS";
    public static final String EFT = "EFT";
    public static final String JCB = "JCB";
    public static final String MC = "MC";
    public static final String MIR = "MIR";
    public static final String PAYWAVE = "PAYWAVE";
    public static final String PBOC = "PBOC";
    public static final String PURE = "PURE";
    public static final String RUPAY = "RUPAY";

    private EmvKernelConst() {
        // do nothing
    }
}
