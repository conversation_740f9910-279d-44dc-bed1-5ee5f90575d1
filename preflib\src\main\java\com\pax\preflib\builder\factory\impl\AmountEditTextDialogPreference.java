/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/09/26                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.factory.impl;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import androidx.annotation.Nullable;
import androidx.preference.EditTextPreference;
import com.pax.preflib.R;
import com.pax.preflib.builder.callback.InputValidateCallback;
import com.pax.preflib.builder.pref.BaseAmountDialogPreference;
import com.pax.preflib.builder.pref.IDialogPreference;

/**
 * Custom amount EditText dialog Preference.
 * <br>
 * Compared with the EditTextPreference of Jetpack Preference, AmountEditTextDialogPreference is
 * specifically designed for entering the amount. It adapts to many different currency types, not
 * just simple currency symbols plus numbers. In addition, it also prohibits the cursor display
 * of EditText, and the input effect is consistent with CustomKeyboardEditText.
 * <br><br>
 * 相较于 Jetpack Preference 的 EditTextPreference 来说，AmountEditTextDialogPreference
 * 专门为了输入金额而设计。它适配了多种不同的货币类型，不仅仅是简单的货币符号加数字。另外，它还禁止了
 * EditText 的光标显示，输入效果和 CustomKeyboardEditText 保持一致。
 */
class AmountEditTextDialogPreference extends BaseAmountDialogPreference
        implements IDialogPreference<AmountEditTextDialog> {
    private long amount = 0;
    private long defValue = 0;

    private EditTextPreference.OnBindEditTextListener onBindEditTextListener;
    private InputValidateCallback<Long> inputValidateCallback;

    public AmountEditTextDialogPreference(Context context,
            AttributeSet attrs,
            int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public AmountEditTextDialogPreference(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public AmountEditTextDialogPreference(Context context, AttributeSet attrs) {
        this(context, attrs, R.attr.dialogPreferenceStyle);
    }

    public AmountEditTextDialogPreference(Context context) {
        this(context, null);
    }

    @Override
    protected Object onGetDefaultValue(TypedArray a, int index) {
        if (a == null) {
            return null;
        }
        defValue = a.getInteger(index, 0);
        return defValue;
    }

    @Override
    protected void onSetInitialValue(@Nullable Object defaultValue) {
        long value;
        if (defaultValue instanceof Number) {
            value = (long) defaultValue;
        } else {
            value = defValue;
        }
        amount = getPersistedLong(value);
    }

    @Override
    public void setAmount(long amount) {
        this.amount = amount;
        persistLong(amount);
        notifyChanged();
    }

    @Override
    public long getAmount() {
        return amount;
    }

    @Override
    public void setOnBindEditTextListener(EditTextPreference.OnBindEditTextListener onBindEditTextListener) {
        this.onBindEditTextListener = onBindEditTextListener;
    }

    @Override
    public void setInputValidateCallback(InputValidateCallback<Long> inputValidateCallback) {
        this.inputValidateCallback = inputValidateCallback;
    }

    @Override
    public AmountEditTextDialog createDialog(String key) {
        return AmountEditTextDialog.newInstance(key)
                .setOnBindEditTextListener(onBindEditTextListener)
                .setValidateCallback(inputValidateCallback);
    }
}
