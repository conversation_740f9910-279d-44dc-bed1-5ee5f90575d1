package com.visionpay.opt.pax.emv.online.interchange;

import com.pax.bizentity.entity.TransData;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.transaction.ITransactionService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.emv.online.IOnlineService;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.CaptureResponse;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.ReversalResponse;

public class InterchangeService implements IOnlineService {

    private ErrorCallback errorCallback = null;
    private RequestCompleteCallback requestCompleteCallback = null;

    @Override
    public InterchangeService setErrorCallback(ErrorCallback errorCallback) {
        this.errorCallback = errorCallback;
        return this;
    }

    @Override
    public InterchangeService setRequestCompleteCallback(RequestCompleteCallback requestCompleteCallback) {
        this.requestCompleteCallback = requestCompleteCallback;
        return this;
    }

    @Override
    public InterchangeService startCapture(String kioskId, String terminalId, long internalID, long stan, long amount){
        App.getApp().runInBackground(() -> {
            try {
                CaptureResponse captureResponse = Interchange.sendForOnlineCapture(terminalId, internalID, amount);
                if(captureResponse != null){
                    ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
                    transactionService.remove(internalID);
                    if(requestCompleteCallback != null)
                        requestCompleteCallback.onComplete(captureResponse.isApproved(),
                                captureResponse.STAN + "", captureResponse.getGatwayResponse(),
                                captureResponse.HostResponse);
                }
                else {
                    if(this.errorCallback != null)
                        errorCallback.onSend("");
                }
                /*ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
                transactionService.remove(internalID);
                requestCompleteCallback.onComplete(true,
                        1 + "", "Transaction Approved",
                        "00");*/

            } catch (Exception e) {
                e.printStackTrace();
                if(this.errorCallback != null)
                    errorCallback.onSend(e.getLocalizedMessage());
            }
        });
        return this;
    }

    @Override
    public InterchangeService startReversal(String kioskId, String terminalId, long internalID, long stan, long amount){
        App.getApp().runInBackground(() -> {

            try {
                ReversalResponse reversalResponse = Interchange.sendForOnlineReversal(terminalId, internalID);
                if(reversalResponse != null){
                    ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
                    transactionService.remove(internalID);
                    if(requestCompleteCallback != null)
                        requestCompleteCallback.onComplete(reversalResponse.isApproved(),
                                reversalResponse.STAN + "", reversalResponse.getGatwayResponse(),
                                ConvertUtils.bcd2Str(new byte[]{reversalResponse.hostDecision}));
                }
                else {
                    if(this.errorCallback != null)
                        errorCallback.onSend("");
                }
                /*ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
                transactionService.remove(internalID);
                    requestCompleteCallback.onComplete(true,
                            1 + "", "Transaction Approved",
                            "00");*/
            } catch (Exception e) {
                if(this.errorCallback != null)
                    errorCallback.onSend(e.getLocalizedMessage());
            }
        });
        return this;
    }
}
