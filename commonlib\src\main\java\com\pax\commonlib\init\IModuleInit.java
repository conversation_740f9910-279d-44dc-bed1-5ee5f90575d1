/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                Author	               Action
 * 20210507 	        xieYb                  Create
 * ===========================================================================================
 *
 */

package com.pax.commonlib.init;
import java.util.LinkedHashMap;

/**
 * base interface for initial
 */
public interface IModuleInit {
    /**
     * method for init for every module
     */
    void init();

    void loadConfig(LinkedHashMap<String, String> configParam);

    void setCallback(Callback callback);

    interface Callback {
        void initDone();
    }
}
