/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2019-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date	                Author	               Action
 * 20210513 	        xieYb                  Create
 * ===========================================================================================
 *
 */
package com.pax.commonui.dialog;

import android.app.Activity;
import android.os.ConditionVariable;
import android.view.KeyEvent;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.application.BaseApplication;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonui.R;
import java.util.Locale;

/**
 * The type Trans process listener.
 */
public class TransProcessListenerImpl implements TransProcessListener {
    private static final String TAG = "TransProcessListener";

    private CustomAlertDialog dialog;

    private ConditionVariable cv;
    private String title;
    private int preAlertType = -1;

    /**
     * Instantiates a new Trans process listener.
     *
     */
    public TransProcessListenerImpl() {
        // do nothing
    }


    @Override
    public void onShowProgress(final String message, final int timeout) {
        showDialog(message,timeout,CustomAlertDialog.PROGRESS_TYPE,false,false);
    }

    @Override
    public void onShowWarning(String message, int timeout) {
        showDialog(message, timeout, CustomAlertDialog.WARN_TYPE,false,false);
    }


    @Override
    public void onShowNormalMessage(final String message, final int timeout, boolean confirmable) {
        cv = new ConditionVariable();
        showDialog(message,timeout,CustomAlertDialog.NORMAL_TYPE,confirmable,false);
        cv.block(timeout * 1000L);
    }

    @Override
    public void onShowErrMessage(final String message, final int timeout, boolean confirmable) {
        cv = new ConditionVariable();
        showDialog(message,timeout,CustomAlertDialog.ERROR_TYPE,confirmable,false);
        cv.block(timeout * 1000L);
    }

    @Override
    public void onShowErrMessage(final String title, final String message, final int timeout, final boolean confirmable) {
        cv = new ConditionVariable();
        showDialog(message,timeout,CustomAlertDialog.ERROR_TYPE,confirmable,false);
        cv.block(timeout * 1000L);
    }

    @Override
    public void onHideProgress() {
        BaseApplication.getAppContext().runOnUiThread(() -> {
            if (dialog != null && dialog.isShowing()) {
                LogUtils.d(TAG,"dismiss");
                dialog.dismiss();
                dialog = null;
            }
        });
    }

    @Override
    public void onUpdateProgressTitle(String title) {
        this.title = title;
    }


    private void showDialog(final String message, final int timeout, final int alertType, final boolean confirmable, final boolean cancelable) {
        BaseApplication.getAppContext().runOnUiThread(() -> {
            LogUtils.d(TAG,String.format(Locale.US, "showDialog: alertType = %s,preAlertType = %s",alertType,preAlertType));
            if (alertType != preAlertType){
                LogUtils.d(TAG,"showDialog:  alertType != preAlertType");
                if (dialog != null) {
                    if (dialog.isShowing()) {
                        LogUtils.d(TAG,"showDialog:  dialog.isShowing,will be dismiss");
                        dialog.dismiss();
                    }
                }
                dialog = createDialog(message,timeout,alertType,confirmable,cancelable);
//                    dialog.show();
            }else {
                LogUtils.d(TAG,"showDialog: alertType == preAlertType");
                if (dialog == null){
                    LogUtils.d(TAG,"showDialog: alertType == preAlertType && dialog == null");
                    dialog = createDialog(message,timeout,alertType,confirmable,cancelable);
//                        dialog.show();
                }else {
                    LogUtils.d(TAG,"showDialog: alertType == preAlertType && dialog != null");
                    updateDialog(message,timeout);
                    if (!dialog.isShowing()){
                        dialog.show();
                    }
                }
            }
            preAlertType = alertType;
        });
    }

    private void updateDialog(String message, int timeout) {
        LogUtils.d(TAG,"updateDialog:"+message);
        dialog.setTimeout(timeout);
        dialog.setContentText(message);
        dialog.setTitleText(title);
    }

    private CustomAlertDialog createDialog(String message, int timeout, int alertType, boolean confirmable, boolean cancelable) {
        Activity top = ActivityStack.getInstance().top();
        LogUtils.d(TAG,"createDialog:topActivity:"+top);
        dialog = new CustomAlertDialog(top, alertType);
        dialog.show();
        if (alertType == CustomAlertDialog.WARN_TYPE) {
            dialog.setImage(R.drawable.commonui_ic16);
        }
        dialog.setTimeout(timeout);
        dialog.setTitleText(title);
        LogUtils.d(TAG,"createDialog:dialog.setTitleText:title:"+title);
        dialog.setContentText(message);
        dialog.setCancelable(false);
        dialog.setCanceledOnTouchOutside(false);
        // AET-77
        dialog.setOnKeyListener((dialog, keyCode, event) ->
                keyCode == KeyEvent.KEYCODE_BACK || keyCode == KeyEvent.KEYCODE_DEL);
        dialog.setOnDismissListener(dialogInterface ->
                LogUtils.d(TAG,"createDialog: setOnDismissListener"));
        if (cancelable){
            dialog.showCancelButton(true);
            dialog.setCancelClickListener(CustomAlertDialog::dismiss);
        }
        if (confirmable){
            dialog.showConfirmButton(true);
            dialog.setConfirmClickListener(CustomAlertDialog::dismiss);
        }
        return dialog;
    }

}
