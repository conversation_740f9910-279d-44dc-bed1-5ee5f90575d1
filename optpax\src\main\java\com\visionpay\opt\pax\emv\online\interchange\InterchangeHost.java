package com.visionpay.opt.pax.emv.online.interchange;

import androidx.annotation.Keep;

@Keep
public class InterchangeHost {
    private String hostName;
    private String iP;
    private String port;

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getiP() {
        return iP;
    }

    public void setiP(String iP) {
        this.iP = iP;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }
}
