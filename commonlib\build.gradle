apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        stagingRelease {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        autoTest{
            buildConfigField('boolean','RELEASE','false')
        }
        debug{
            buildConfigField('boolean','RELEASE','false')
        }
    }

}
repositories{
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    api fileTree(dir: 'libs', include: ['*.jar'])

    api "com.alibaba:fastjson:$rootProject.fastjson"
    api "com.android.support:multidex:$rootProject.multidex"
    api "androidx.appcompat:appcompat:$rootProject.androidAppcompat"
    api 'net.sourceforge.javacsv:javacsv:2.0'
    api "org.greenrobot:greendao:$rootProject.greendao"
    api "net.zetetic:android-database-sqlcipher:$rootProject.sqlcipher"
    testImplementation "junit:junit:$rootProject.junit"
    testImplementation "org.mockito:mockito-all:$rootProject.mockito"
    testImplementation "org.hamcrest:hamcrest-all:$rootProject.hamcrest"
    testImplementation "androidx.arch.core:core-testing:$rootProject.coreTesting"
    androidTestImplementation "androidx.test:runner:$rootProject.runner"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    androidTestImplementation "junit:junit:$rootProject.junit"
    annotationProcessor "io.github.meituan-dianping:compiler:$rootProject.routerCompiler"
    api "io.github.meituan-dianping:router:$rootProject.router"
    //RxJava RxAndroid
    api "io.reactivex.rxjava3:rxjava:$rootProject.rxjava3"
    api "io.reactivex.rxjava3:rxandroid:$rootProject.rxandroid3"
    api "com.trello.rxlifecycle4:rxlifecycle-android-lifecycle:$rootProject.rxlifecycle4"

    api "androidx.constraintlayout:constraintlayout:$rootProject.constraintlayout"
    api "com.squareup.okhttp3:okhttp:$rootProject.okhttp"
    api "com.squareup.okhttp3:logging-interceptor:$rootProject.loggingInterceptor"
}
