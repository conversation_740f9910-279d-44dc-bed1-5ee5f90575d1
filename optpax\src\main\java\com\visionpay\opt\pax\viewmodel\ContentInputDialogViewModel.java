/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/24                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.viewmodel;

import androidx.lifecycle.ViewModel;
import com.visionpay.opt.pax.callback.OnBindEditTextCallback;
import com.visionpay.opt.pax.dialog.ContentInputDialog;

/**
 * Content Input Dialog ViewModel.
 *
 * 其实 DialogFragment 用 ViewModel 有点奇怪，但是的确是没有更好的办法。像是 OnClickCallback 这类接口很难存储
 * 到 Bundle 中去，而且浪费时间。还不如用一个 ViewModel 存起来，哪怕是旋转屏幕或者切换深色模式，都不会影响其中存储
 * 的数据。
 */
public class ContentInputDialogViewModel extends ViewModel {
    private boolean isRestore = false;
    private String title;
    private OnBindEditTextCallback onBindEditTextCallback;
    private String positiveButtonText;
    private Integer positiveButtonIconRes;
    private ContentInputDialog.OnClickCallback positiveButtonClickListener;
    private String negativeButtonText;
    private Integer negativeButtonIconRes;
    private ContentInputDialog.OnClickCallback negativeButtonClickListener;

    public boolean isRestore() {
        return isRestore;
    }

    public void setRestore(boolean restore) {
        isRestore = restore;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public OnBindEditTextCallback getOnBindEditTextCallback() {
        return onBindEditTextCallback;
    }

    public void setOnBindEditTextCallback(OnBindEditTextCallback onBindEditTextCallback) {
        this.onBindEditTextCallback = onBindEditTextCallback;
    }

    public String getPositiveButtonText() {
        return positiveButtonText;
    }

    public void setPositiveButtonText(String positiveButtonText) {
        this.positiveButtonText = positiveButtonText;
    }

    public Integer getPositiveButtonIconRes() {
        return positiveButtonIconRes;
    }

    public void setPositiveButtonIconRes(Integer positiveButtonIconRes) {
        this.positiveButtonIconRes = positiveButtonIconRes;
    }

    public ContentInputDialog.OnClickCallback getPositiveButtonClickListener() {
        return positiveButtonClickListener;
    }

    public void setPositiveButtonClickListener(
            ContentInputDialog.OnClickCallback positiveButtonClickListener) {
        this.positiveButtonClickListener = positiveButtonClickListener;
    }

    public String getNegativeButtonText() {
        return negativeButtonText;
    }

    public void setNegativeButtonText(String negativeButtonText) {
        this.negativeButtonText = negativeButtonText;
    }

    public Integer getNegativeButtonIconRes() {
        return negativeButtonIconRes;
    }

    public void setNegativeButtonIconRes(Integer negativeButtonIconRes) {
        this.negativeButtonIconRes = negativeButtonIconRes;
    }

    public ContentInputDialog.OnClickCallback getNegativeButtonClickListener() {
        return negativeButtonClickListener;
    }

    public void setNegativeButtonClickListener(
            ContentInputDialog.OnClickCallback negativeButtonClickListener) {
        this.negativeButtonClickListener = negativeButtonClickListener;
    }
}
