package com.visionpay.opt.pax.emv;
import com.pax.emvservice.export.IEmvBase;
public interface ISimpleEmvService extends IEmvBase {
    /**
     * user cancel during contact process
     * @param userCancel userCancel
     */
    void setUserCancel(boolean userCancel);
    /**
     * application timeout,and finish the emv process
     * @param isTimeOut isTimeOut
     */
    void timeOut(boolean isTimeOut);
    /**
     * Gets kernel type
     * @return kernel type
     */
    int getKernelType();
    /**
     * check whether application is timeout
     */
    int startTransProcess(int detectResult, SimpleEmvService.ISimpleContactCallback contactCallback);

    /**
     * setTrackData
     * @param trackData1 trackData1
     * @param trackData2 trackData2
     * @param trackData3 trackData3
     */
    void setTrackData(String trackData1, String trackData2, String trackData3);
}
