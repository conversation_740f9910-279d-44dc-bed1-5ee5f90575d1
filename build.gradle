// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()

        // In version 1.06 of EDC Template, we have removed the JCenter declaration. This is
        // because JCenter has stated that they plan to shut down JCenter in February 2022.
        // Although JCenter subsequently revised their announcements, many Android developers
        // have migrated their open source libraries to Maven Central or other platforms just to
        // be on the safe side.
        // You should not re-enable JCenter. JCenter no longer allows uploading new libraries,
        // which means that if you use a library in JCenter, you will not be able to get any
        // updates in the future. And if JCenter again says they need to shut down the service,
        // you still need to migrate, otherwise your code will unable to build.
        // 在 1.06 版本的 EDC Template中，我们已经移除了 JCenter 的声明。这是因为 JCenter 曾经表示它们计划于
        // 2022 年 2 月关闭 JCenter。尽管后续 JCenter 修改了它们的公告，但为了保险起见，众多 Android 开发者都
        // 已经将它们的开源库迁移到了 Maven Central 或者其他平台（事实上，如果一个开源库没有从 JCenter 迁移走，
        // 那基本上可以认为这个库已经不再维护了）。
        // 请不要重新启用 JCenter。JCenter 已经不允许上传新的库，这也就意味着您如果使用 JCenter 中的库，将来会
        // 无法获得任何更新。并且如果 JCenter 再次表示它们需要关闭服务的话，您仍然需要迁移，否则将导致您的代码无法
        // build。
        // 如果你维护的是老项目，并且也希望移除 JCenter 的使用的话，你可以参照 PPN 上发布的 EDC Template 的文档
        // 来移除 JCenter 的使用。

//        jcenter()

        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.1'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0' // add plugin
        classpath 'org.apache.commons:commons-configuration2:2.4'
        classpath "io.github.meituan-dianping:plugin:1.2.1"
    }
    ext {
        compileSdkVersion = 31
        buildToolsVersion = '28.0.3'
        minSdkVersion = 22
        targetSdkVersion = 28
        applicationId = "com.pax.edc"
        versionCode = 52
        versionName = "1.06.02"

        glide = '4.11.0'
        rxjava3 = '3.1.0'
        rxandroid3 = '3.0.0'
        rxlifecycle4 = '4.0.2'
        banner = '3.0.1'
        constraintlayout = '2.1.0'
        greendao = '3.3.0'
        sqlcipher = '3.5.6'
        material = '1.4.0'
        preference = '1.1.1'
        androidxSupportV4 = '1.0.0'
        androidAppcompat = '1.3.1'
        slf4jAndroid = '1.6.1-RC1'
        multidex = '1.0.3'
        paxstore = '8.1.0'
        fastjson = '1.1.72.android'
        //fastjson = '2.0.21.android'
        routerCompiler = '1.2.1'
        router = '1.2.1'
        okhttp = '3.14.9'
        loggingInterceptor = '3.14.0'
        androidxPaging = '3.0.1'

        // for test
        junit = '4.13.2'
        mockito = '1.10.19'
        hamcrest = '1.3'
        coreTesting = '2.1.0'
        runner = '1.4.0'
        espresso = '3.4.0'
        androidxExtJunit = '1.1.3'

        // for debug
        leakcanary = '2.4'
    }
}

allprojects {
    repositories {
        google()
        maven { url 'https://jitpack.io' }
//        jcenter()
        mavenCentral()
    }
}
