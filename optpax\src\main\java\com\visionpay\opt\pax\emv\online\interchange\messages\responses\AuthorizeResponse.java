package com.visionpay.opt.pax.emv.online.interchange.messages.responses;

import com.pax.commonlib.utils.ConvertUtils;
import com.visionpay.opt.pax.emv.online.interchange.messages.Message;

import java.util.Arrays;

public class AuthorizeResponse extends Message {

    public long InternalID;
    public int STAN;
    public String RRN;
    public byte HostDecision;
    public String HostResponse;
    public byte[] AuthCode, IssuerDataForAuth, IssuerScriptTemplate1, IssuerScriptTemplate2;

    public String MerchantId;

    public AuthorizeResponse(byte[] buffer, String merchantId) {
        super(buffer);
        MerchantId = merchantId;
    }

    @Override
    protected byte[] internalGetMessage() {
        return this.mMessage;
    }

    @Override
    protected int internalParse(int i) {
        i = super.internalParse(i);

        //String strMessage = ConvertUtils.bcd2Str(this.mMessage);

        //this.InternalID = (long)(this.mMessage[i++] << 24);
        //this.InternalID += (long)(this.mMessage[i++] << 16);
        //this.InternalID += (long)(this.mMessage[i++] << 8);
        //this.InternalID += (long)this.mMessage[i++];
        this.InternalID = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 4));
        i += 4;
        //this.STAN = (int)(this.mMessage[i++] << 8);
        //this.STAN += (int)(this.mMessage[i++]);
        this.STAN = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 2));
        i += 2;
        //this.RRN = strMessage.substring(i, i + 12);
        this.RRN = new String(Arrays.copyOfRange(this.mMessage, i, i + 12));
        i += 12;
        this.HostDecision = this.mMessage[i++];
        //this.HostResponse = strMessage.substring(i, i + 2);
        this.HostResponse = new String(Arrays.copyOfRange(this.mMessage, i, i + 2));
        i += 2;
        this.AuthCode = new byte[6];
        System.arraycopy(this.mMessage,i, this.AuthCode, 0, 6);
        i += 6;
        int len = this.mMessage[i++];
        this.IssuerDataForAuth = new byte[len];
        System.arraycopy(this.mMessage,i, this.IssuerDataForAuth, 0, len);
        i += len;
        //len = this.mMessage[i++] << 8;
        //len += this.mMessage[i++];
        len = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 2));
        i += 2;
        this.IssuerScriptTemplate1 = new byte[len];
        System.arraycopy(this.mMessage, i, this.IssuerScriptTemplate1, 0, len);
        i += len;
        //len = this.mMessage[i++] << 8;
        //len += this.mMessage[i++];
        len = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 2));
        i += 2;
        this.IssuerScriptTemplate2 = new byte[len];
        System.arraycopy(this.mMessage, i, this.IssuerScriptTemplate2, 0, len);
        i += len;
        return i;
    }

    public boolean isApproved(){
        return this.HostResponse.equals("00") || this.HostResponse.equals("08")
                || this.HostResponse.equals("10") || this.HostResponse.equals("11")
                || this.HostResponse.equals("16");
    }

    public boolean isTimeout(){
        return this.HostResponse.equals("V0");
    }

    public boolean isError(){
        return this.HostResponse.equals("96") || this.HostResponse.equals("06");
    }

    public boolean isDecline(){
        return !isApproved() && !isTimeout() && !isError();
    }

    public String getGatwayResponse(){
        return ResponseCodeHelper.getGatwayResponse(HostResponse);
    }
}
