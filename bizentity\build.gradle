apply plugin: 'com.android.library'
apply plugin: 'org.greenrobot.greendao'
android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        buildConfigField('String', 'DATABASE_PWD', DATABASE_PWD)
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        stagingRelease {
            buildConfigField('boolean','RELEASE','true')
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        autoTest{
            buildConfigField('boolean','RELEASE','false')
        }
        debug{
            buildConfigField('boolean','RELEASE','false')
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api "androidx.appcompat:appcompat:$rootProject.androidAppcompat"
    testImplementation "junit:junit:$rootProject.junit"
    androidTestImplementation "androidx.test.ext:junit:$rootProject.androidxExtJunit"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    api project(':commonlib')
    annotationProcessor "io.github.meituan-dianping:compiler:$rootProject.routerCompiler"
}
greendao {
    schemaVersion 8
    daoPackage 'com.pax.bizentity.db.dao'
    targetGenDir 'src/main/java'
}