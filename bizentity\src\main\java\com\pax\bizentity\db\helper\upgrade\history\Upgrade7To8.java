package com.pax.bizentity.db.helper.upgrade.history;

import com.alibaba.fastjson.TypeReference;
import com.pax.bizentity.db.dao.DpasAidBeanDao;
import com.pax.bizentity.db.dao.MirAidBeanDao;
import com.pax.bizentity.db.dao.PBOCAidBeanDao;
import com.pax.bizentity.db.dao.PayPassAidBeanDao;
import com.pax.bizentity.db.dao.RupayAidBeanDao;
import com.pax.bizentity.db.dao.TransDataDao;
import com.pax.bizentity.db.helper.DpasAidDbHelper;
import com.pax.bizentity.db.helper.MirAidDbHelper;
import com.pax.bizentity.db.helper.PBOCAidDbHelper;
import com.pax.bizentity.db.helper.PaypassAidDbHelper;
import com.pax.bizentity.db.helper.RupayAidDbHelper;
import com.pax.bizentity.db.helper.upgrade.DbUpgrade;
import com.pax.bizentity.db.helper.upgrade.UpgradeConst;
import com.pax.bizentity.entity.clss.dpas.DpasAidBean;
import com.pax.bizentity.entity.clss.dpas.DpasParamBean;
import com.pax.bizentity.entity.clss.mir.MirAidBean;
import com.pax.bizentity.entity.clss.mir.MirParamBean;
import com.pax.bizentity.entity.clss.paypass.PayPassAidBean;
import com.pax.bizentity.entity.clss.paypass.PayPassParamBean;
import com.pax.bizentity.entity.clss.pboc.PBOCAidBean;
import com.pax.bizentity.entity.clss.pboc.PBOCParamBean;
import com.pax.bizentity.entity.clss.rupay.RupayAidBean;
import com.pax.bizentity.entity.clss.rupay.RupayParamBean;
import com.pax.commonlib.json.JsonProxy;
import com.pax.commonlib.utils.LogUtils;
import com.sankuai.waimai.router.annotation.RouterService;

import org.greenrobot.greendao.database.Database;

import java.util.List;

/**
 * File description
 *
 * <AUTHOR>
 * @date 2021/11/25
 */
@RouterService(interfaces = DbUpgrade.class, key = UpgradeConst.UPGRADE_7_8)
public class Upgrade7To8 extends DbUpgrade {
    private static final String TAG = "Upgrade7To8";

    @Override
    protected void upgrade(Database db) {
        try {
            DbUpgrade.upgradeTable(db, TransDataDao.class);
        } catch (Exception e) {
            LogUtils.e(TAG, "", e);
        }
    }
}
