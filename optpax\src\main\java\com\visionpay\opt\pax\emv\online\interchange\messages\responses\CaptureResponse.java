package com.visionpay.opt.pax.emv.online.interchange.messages.responses;

import com.pax.commonlib.utils.ConvertUtils;
import com.visionpay.opt.pax.emv.online.interchange.messages.Message;

import java.util.Arrays;

public class CaptureResponse extends Message {

    public long internalID;
    public int STAN;
    public String RRN;
    public String HostResponse;

    public CaptureResponse(byte[] buffer) {
        super(buffer);
    }

    @Override
    protected byte[] internalGetMessage(){
        return this.mMessage;
    }

    @Override
    protected int internalParse(int i){
        i = super.internalParse(i);
        //this.InternalID = (long)(this.mMessage[i++] << 24);
        //this.InternalID += (long)(this.mMessage[i++] << 16);
        //this.InternalID += (long)(this.mMessage[i++] << 8);
        //this.InternalID += (long)this.mMessage[i++];
        this.internalID = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 4));
        i += 4;
        //this.STAN = (int)(this.mMessage[i++] << 8);
        //this.STAN += (int)(this.mMessage[i++]);
        this.STAN = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 2));
        i += 2;
        this.RRN = new String(Arrays.copyOfRange(this.mMessage, i, i + 12));
        i += 12;
        this.HostResponse = new String(Arrays.copyOfRange(this.mMessage, i, i + 2));
        i += 2;
        //this->HostDecision = string(this->Message.begin() + *i, this->Message.begin() + *i + 1);
        //this.hostDecision = new String(Arrays.copyOfRange(this.mMessage,i, i+1));
        //i += 1;
        return i;
    }

    public boolean isApproved(){
        return this.HostResponse.equals("00") || this.HostResponse.equals("08")
                || this.HostResponse.equals("10") || this.HostResponse.equals("11")
                || this.HostResponse.equals("16");
    }

    public boolean isTimeout(){
        return this.HostResponse.equals("V0");
    }

    public boolean isError(){
        return this.HostResponse.equals("96") || this.HostResponse.equals("06");
    }

    public boolean isDecline(){
        return !isApproved() && !isTimeout() && !isError();
    }

    public String getGatwayResponse(){
        return ResponseCodeHelper.getGatwayResponse(HostResponse);
    }
}
