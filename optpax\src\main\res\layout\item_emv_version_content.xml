<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ ===========================================================================================
  ~ = COPYRIGHT
  ~          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
  ~   This software is supplied under the terms of a license agreement or nondisclosure
  ~   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
  ~   disclosed except in accordance with the terms in that agreement.
  ~     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
  ~ Description: // Detail description about the function of this module,
  ~             // interfaces with the other modules, and dependencies.
  ~ Revision History:
  ~ Date                           Author                      Action
  ~ 2022/04/11                     YeHongbo                    Create
  ~ ===========================================================================================
  -->

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="@dimen/list_padding_vertical">

    <TextView
        android:id="@+id/emv_version_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/main_text"
        android:textSize="@dimen/text_normal_size"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/emv_version_item_content"
        app:layout_constraintBottom_toBottomOf="@id/emv_version_item_content"
        tools:text="AMEX Version" />

    <TextView
        android:id="@+id/emv_version_item_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/main_text"
        android:textSize="@dimen/text_normal_size"
        android:fontFamily="monospace"
        android:letterSpacing="0.02"
        android:gravity="end"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/emv_version_item_title"
        app:layout_constraintEnd_toEndOf="parent"
        tools:text="v252 2021.05.10" />

</androidx.constraintlayout.widget.ConstraintLayout>