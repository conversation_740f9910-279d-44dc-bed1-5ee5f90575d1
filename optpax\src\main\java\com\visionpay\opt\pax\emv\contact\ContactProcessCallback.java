/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.contact;

import android.app.Activity;
import android.os.ConditionVariable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.pax.bizentity.entity.Issuer;
import com.pax.bizlib.trans.Device;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.pax.emvbase.constant.EmvConstant;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.contact.CandidateAID;
import com.pax.emvbase.process.contact.IContactCallback;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.pax.emvservice.export.IEmvContactService;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.dialog.RemoveCardDialog;
import com.visionpay.opt.pax.dialog.SingleSelectionDialog;
import com.visionpay.opt.pax.emv.IEmvProcessCallback;
import com.visionpay.opt.pax.emv.IEmvService;
import com.visionpay.opt.pax.emv.contactless.ContactlessService;
import com.visionpay.opt.pax.emv.online.OnlineTaskFactory;
import com.visionpay.opt.pax.emv.pin.IPinTask;
import com.visionpay.opt.pax.emv.pin.OfflinePinTask;
import com.visionpay.opt.pax.emv.pin.OnlinePinTask;
import com.visionpay.opt.pax.entity.CvmType;
import com.visionpay.opt.pax.entity.EIssuer;
import com.visionpay.opt.pax.entity.EmvAidInfo;
import com.visionpay.opt.pax.utils.DialogUtils;
import com.visionpay.opt.pax.utils.EmvAidUtils;

import java.util.List;

/**
 * Contact EMV Process Callback
 *
 * 这个类实现了 IContactCallback 接口，这个接口是 emvbase 模块定义的。之所以要提供这样一个 Callback，是因为
 * emvlib 或者 p2pelib 模块并不包含 UI 以及联机部分的逻辑，这一部分的逻辑需要由上层的 application 模块去实现。
 *
 * 不过在这里，这个类的作用更多的是把处理逻辑交付给其他类来处理。比如输入 PIN 的逻辑交给了 OnlinePinTask 或者
 * OfflinePinTask，将联机的逻辑交给了 EmvOnlineTask，或者是调用更新 Activity 的 ConfirmCallback 以及
 * ErrorCallback。
 */
@WorkerThread
public class ContactProcessCallback implements IContactCallback, IEmvProcessCallback {
    private static final String TAG = "ContactProcessCallback";

    private final IEmvContactService emv;
    private final ConditionVariable cv;
    private IEmvService.ConfirmCallback confirmCallback;
    private IEmvService.ErrorCallback errorCallback;
    private ContactlessService.RemoveCardCallback removeCardCallback;
    private List<CvmType> cvmTypeList;

    public ContactProcessCallback(@NonNull IEmvContactService emv, @NonNull ConditionVariable cv) {
        this.emv = emv;
        this.cv = cv;
    }

    @Override
    public void onRemoveCard() {
        try {
            DialogUtils.dismiss();
            final boolean[] isShown = { false };
            RemoveCardDialog dialog = new RemoveCardDialog();
            Device.removeCard(result -> {
                if (!isShown[0]) {
                    Activity activity = ActivityStack.getInstance().top();
                    if (activity instanceof FragmentActivity) {
                        FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
                        dialog.show(manager, "Remove Card");
                        isShown[0] = true;
                    }
                    if (removeCardCallback != null) {
                        removeCardCallback.onRemove();
                    }
                }
            });
            dialog.dismissAllowingStateLoss();
        } catch (Exception e) {
            LogUtils.e(TAG, "Remove card error", e);
        }
    }

    @Override
    public int onWaitAppSelect(boolean isFirstSelect, List<CandidateAID> candList) {
        LogUtils.d(TAG, "============ Application Select Start ============");
        DialogUtils.dismiss();

        final int[] appSelectRet = {0};
        App.getApp().runOnUiThread(() -> {
            new SingleSelectionDialog()
                    .setTitle("APP Choose")
                    .addChoices(candList,
                            input -> new String(input.getAppName()), -1,
                            (dialog, which) -> {
                        appSelectRet[0] = which;
                        dialog.dismiss();
                        cv.open();
                    })
                    .setPositiveButton("Cancel", null, dialog -> {
                        appSelectRet[0] = EmvConstant.ContactCallbackStatus.USER_CANCEL;
                        dialog.dismiss();
                        cv.open();
                    }).show();
        });
        cv.block();
        cv.close();
        LogUtils.d(TAG, "============ Application Select End ============");
        return appSelectRet[0];
    }

    @Override
    public int onCardHolderPwd(boolean isOnlinePin, boolean supportPINByPass, int leftTimes,
            byte[] pinData) {
        LogUtils.d(TAG, "============ PIN Input Start ============");
        DialogUtils.dismiss();

        IPinTask pinTask;
        if (isOnlinePin) {
            pinTask = new OnlinePinTask((title, reason) -> errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_online_pin)));
            }
        } else {
            pinTask = new OfflinePinTask(leftTimes, (title, reason) ->
                    errorCallback.onError(title, reason));
            if (cvmTypeList != null) {
                cvmTypeList.add(new CvmType(R.drawable.ic_pin, ResourceUtil.getString(R.string.cvm_offline_pin)));
            }
        }

        int ret = pinTask.start(emv, supportPINByPass, false);
        LogUtils.d(TAG, "Pin Task ret: " + ret);
        LogUtils.d(TAG, "============ PIN Input End ============");
        return ret;
    }

    @Override
    public int showConfirmCard() {
        LogUtils.d(TAG, "============ Confirm Card Start ============");

        String aid = ConvertUtils.bcd2Str(emv.getTlv(0x84));
        int seqNumber = ConvertUtils.bcd2Int(emv.getTlv(0x5F34));
        List<EmvAidInfo> infoList = EmvAidUtils.getContactAidInfo(aid);
        String path = EmvAidUtils.getEditContactAidPath(aid);

        Issuer matchedIssuer = emv.getMatchedIssuerByPan();

        if (confirmCallback != null) {
            if (matchedIssuer != null) {
                String name = matchedIssuer.getName();
                EIssuer info = EIssuer.parse(name);
                if (info != null) {
                    confirmCallback.onConfirm(emv.getPan(),
                            info.getIssuerIconRes(),
                            info.getDisplayName(),
                            getCardHolderName(), infoList, path, aid, seqNumber + "",
                            emv.getExpireDate(),
                            new String(emv.getTlv(TagsTable.APP_LABEL)),
                            ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                } else {
                    confirmCallback.onConfirm(emv.getPan(),
                            R.drawable.ic_issuer_other, name,
                            getCardHolderName(), infoList, path, aid, seqNumber + "",
                            emv.getExpireDate(),
                            new String(emv.getTlv(TagsTable.APP_LABEL)),
                            ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                            matchedIssuer != null ? matchedIssuer.isBankCard() : false);
                }
            } else {
                confirmCallback.onConfirm(emv.getPan(), R.drawable.ic_issuer_other,
                        "Unknown",
                        getCardHolderName(), infoList, path, aid, seqNumber + "",
                        emv.getExpireDate(),
                        new String(emv.getTlv(TagsTable.APP_LABEL)),
                        ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)),
                        matchedIssuer != null ? matchedIssuer.isBankCard() : false);
            }
        }
        LogUtils.d(TAG, "============ Confirm Card End ============");
        return 0;
    }

    @NonNull
    private String getCardHolderName() {
        String cardHolderName = emv.getCardholderName();
        if (cardHolderName == null || cardHolderName.isEmpty() || cardHolderName.trim().isEmpty()) {
            return "";
        }
        return new String(ConvertUtils.strToBcdPaddingLeft(cardHolderName));
    }

    @Override
    public int showEnterTip() {
        // do nothing
        return 0;
    }

    @Override
    public OnlineResultWrapper startOnlineProcess(long amount, String terminalId, int detectResult, String reference, ISendingOnlineCallback callback) {
        DialogUtils.dismiss();
        return OnlineTaskFactory.GetTask(emv, callback).start(emv, amount, terminalId, detectResult, reference);
    }

    @NonNull
    @Override
    public ContactProcessCallback setConfirmCallback(@Nullable IEmvService.ConfirmCallback callback) {
        this.confirmCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public ContactProcessCallback setErrorCallback(@Nullable IEmvService.ErrorCallback callback) {
        this.errorCallback = callback;
        return this;
    }

    @NonNull
    public ContactProcessCallback setCvmTypeList(List<CvmType> cvmTypeList) {
        this.cvmTypeList = cvmTypeList;
        return this;
    }

    public ContactProcessCallback setRemoveCardCallback(ContactlessService.RemoveCardCallback removeCardCallback) {
        this.removeCardCallback = removeCardCallback;
        return this;
    }
}
