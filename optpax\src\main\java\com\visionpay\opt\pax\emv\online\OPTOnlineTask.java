/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/01                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv.online;

import android.app.Activity;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.pax.bizentity.entity.SearchMode;
import com.pax.bizentity.entity.TransData;
import com.pax.commonlib.application.ActivityStack;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.emvbase.constant.TagsTable;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.process.entity.EOnlineResult;
import com.pax.emvbase.process.entity.IssuerRspData;
import com.pax.emvbase.process.entity.OnlineResultWrapper;
import com.pax.emvbase.process.enums.TransResultEnum;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.IEmvBase;
import com.pax.emvservice.export.transaction.ITransactionService;
import com.sankuai.waimai.router.Router;
import com.visionpay.opt.pax.emv.online.interchange.enums.InterchangeTransactionType;
import com.visionpay.opt.pax.emv.online.interchange.messages.responses.AuthorizeResponse;
import com.visionpay.opt.pax.emv.online.interchange.utils.ICCBuilder;
import com.visionpay.opt.pax.emv.online.opt.OPTService;
import com.visionpay.opt.pax.emv.online.opt.messages.responses.AccountCardTransactionResponse;
import com.visionpay.opt.pax.utils.DialogUtils;
import com.visionpay.opt.pax.utils.EmvTagUtils;

/**
 * OPT Online Task
 */
public class OPTOnlineTask implements IOnlineTask<IEmvBase> {
    private static final String TAG = "OPTOnlineTask";

    private ISendingOnlineCallback callback;

    private long transId = 0;

    public OPTOnlineTask setSendindOnlineCallback(ISendingOnlineCallback callback){
        this.callback = callback;
        return this;
    }

    @Override
    public OnlineResultWrapper start(@NonNull IEmvBase emv, long amount, String terminalId, int detectResult, String reference) {
        LogUtils.d(TAG, "============ OPT Online Start ============");
        OnlineResultWrapper result = new OnlineResultWrapper();
        IssuerRspData rspData = new IssuerRspData();
        result.setIssuerRspData(rspData);

        Activity activity = ActivityStack.getInstance().top();
        if (activity instanceof FragmentActivity) {
            DialogUtils.createProcessingDialog((FragmentActivity) activity, "OPT Processing",
                    "Process online result, please wait...");
        }

        byte[] PIN = emv.getPin();

        String kioskId = new String(emv.getTlv(0x100001));

        callback.onStart();
        AccountCardTransactionResponse response = sendForOnlineAuthorization(emv, reference, amount, PIN, terminalId,
                                                                            kioskId);
        respCodeProcess(result, emv, rspData, response, PIN.length > 0);

        LogUtils.d(TAG, "============ OPT Online End ============");
        return result;
    }

    private AccountCardTransactionResponse sendForOnlineAuthorization(IEmvBase emv, String transReference, long amount,
                                                         byte[] PIN, String terminalId, String kioskId)
    {
        String track2 = emv.getTrack2Data();

        try {
            transId = transInit(transReference, amount, emv.getPan(), PIN.length > 0, track2, "", new String(emv.getTlv(TagsTable.APP_LABEL)), emv.getExpireDate(), ConvertUtils.bcd2Int(emv.getTlv(TagsTable.ATC)) + "", "0");
            AccountCardTransactionResponse response = OPTService.authorizeCard(kioskId, transId, emv.getPan(), amount, track2, ConvertUtils.bcd2Str(PIN), emv.getExpireDate(), terminalId);
            return response;
        }
        catch (Exception e){
            e.printStackTrace();
            Log.e(TAG, e.getMessage());
            callback.onError();
        }
        return null;
    }

    private long transInit(String reference, long amount, String pan, boolean hasPin, String track2, String aid, String appName, String expDate, String atc, String cardSerialNo){
        ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
        return transactionService.transInit(reference, Long.toString(amount), pan, hasPin, track2, aid, appName, expDate, atc, cardSerialNo, TransData.EnterMode.WHITECARD).getId();
    }

    private void transUpdate(String authCode, String emvResult, TransData.ReversalStatus reversalStatus, String responseCode, String issuerCode, long stan){
        ITransactionService transactionService = Router.getService(ITransactionService.class, EmvServiceConstant.EMVSERVICE_TRANSACTION);
        transactionService.transUpdate(transId, authCode, emvResult, reversalStatus, responseCode, issuerCode, stan);
    }

    private void respCodeProcess(
            OnlineResultWrapper result,
            @NonNull IEmvBase emv,
            @NonNull IssuerRspData rspData,
            @NonNull AccountCardTransactionResponse response,
            boolean hasPin){
        if(response != null) {
            TransData.ReversalStatus reversalStatus = TransData.ReversalStatus.NORMAL;
            if(response.isError()) {
                result.setResultCode(EOnlineResult.FAILED.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_FAILED);
                rspData.setOnlineResult(EOnlineResult.FAILED.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.PENDING;
                transUpdate(response.getAuthCode(), result.getResultCode() + "", reversalStatus, response.getHostResponse(), response.getMerchantId(), response.getSTAN());
                callback.onError();
                return;
            }
            if(response.isTimeout()) {
                result.setResultCode(EOnlineResult.DENIAL.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_DENIED);
                rspData.setOnlineResult(EOnlineResult.DENIAL.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.PENDING;
                callback.onTimeOut();
            }
            if(response.isApproved()) {
                result.setResultCode(EOnlineResult.APPROVE.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_APPROVED);
                rspData.setOnlineResult(EOnlineResult.APPROVE.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.NORMAL;
                //rspData.setRespCode("00".getBytes());
                //emv.setTlv(0x8A, "00".getBytes());
                callback.onComplete(response.isApproved(), hasPin, response.getMerchantId(), response.getSTAN() + "", response.getGatwayResponse(), response.getHostResponse(), response.getAuthCode());
            }
            if(response.isDecline()) {
                result.setResultCode(EOnlineResult.DENIAL.getResultCode());
                result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_DENIED);
                rspData.setOnlineResult(EOnlineResult.DENIAL.getEmvOnlineResult());
                reversalStatus = TransData.ReversalStatus.PENDING;
                callback.onComplete(response.isApproved(), hasPin, response.getMerchantId(), response.getSTAN() + "", response.getGatwayResponse(), response.getHostResponse(), response.getAuthCode());
            }

            LogUtils.d(TAG, "Response Code: " + response.getHostResponse());
            rspData.setRespCode(response.getHostResponse().getBytes());
            emv.setTlv(0x8A, response.getHostResponse().getBytes());

            transUpdate(response.getAuthCode(), result.getResultCode() + "", reversalStatus, response.getHostResponse(), response.getMerchantId(), response.getSTAN());
        }
        else {
            result.setResultCode(EOnlineResult.FAILED.getResultCode());
            result.setTransResultEnum(TransResultEnum.RESULT_ONLINE_FAILED);
            rspData.setOnlineResult(EOnlineResult.FAILED.getEmvOnlineResult());
            TransData.ReversalStatus reversalStatus = TransData.ReversalStatus.PENDING;
            transUpdate("", result.getResultCode() + "", reversalStatus, "", "", 0);
            callback.onError();
        }
    }
}
