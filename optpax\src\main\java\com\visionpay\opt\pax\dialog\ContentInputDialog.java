/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.dialog;

import android.app.Dialog;
import android.os.Bundle;
import android.text.Editable;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.fragment.app.DialogFragment;
import androidx.lifecycle.ViewModelProvider;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.pax.commonlib.utils.LogUtils;
import com.pax.commonlib.utils.ResourceUtil;
import com.visionpay.opt.pax.R;
import com.visionpay.opt.pax.callback.OnBindEditTextCallback;
import com.visionpay.opt.pax.viewmodel.ContentInputDialogViewModel;

/**
 * Content Input Dialog.
 */
public class ContentInputDialog extends BaseDialogFragment<ContentInputDialog> {
    private static final String TAG = "ContentInputDialog";
    private ContentInputDialogViewModel viewModel;

    private String title;
    private OnBindEditTextCallback onBindEditTextCallback;
    private String positiveButtonText;
    private Integer positiveButtonIconRes;
    private OnClickCallback positiveButtonClickListener;
    private String negativeButtonText;
    private Integer negativeButtonIconRes;
    private OnClickCallback negativeButtonClickListener;

    @Override
    protected int getContentLayoutRes() {
        return R.layout.dialog_input;
    }

    @Override
    protected void onCreateDialog(@NonNull Dialog dialog, @NonNull View view,
            @Nullable Window window, @Nullable Bundle savedInstanceState) {
        LogUtils.d(TAG, "onCreateDialog");

        if (window != null) {
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE);
        }

        viewModel = new ViewModelProvider(this).get(ContentInputDialogViewModel.class);
        if (!viewModel.isRestore()) {
            setViewModel();
        } else {
            viewModel.setRestore(false);
        }

        // Title
        TextView titleTextView = view.findViewById(R.id.input_title);
        if (viewModel.getTitle() != null && !viewModel.getTitle().isEmpty()) {
            titleTextView.setVisibility(View.VISIBLE);
            titleTextView.setText(viewModel.getTitle());
        } else {
            titleTextView.setVisibility(View.GONE);
        }

        // EditText
        TextInputEditText inputEditText = view.findViewById(R.id.input_text_field);
        inputEditText.requestFocus();
        if (viewModel.getOnBindEditTextCallback() != null) {
            viewModel.getOnBindEditTextCallback().onBind(inputEditText);
        }

        // Positive Button
        MaterialButton positiveButton = view.findViewById(R.id.input_positive_button);
        if (viewModel.getPositiveButtonText() != null && !viewModel.getPositiveButtonText().isEmpty()) {
            positiveButton.setVisibility(View.VISIBLE);
            positiveButton.setText(viewModel.getPositiveButtonText());
            positiveButton.setOnClickListener(v -> {
                if (viewModel.getPositiveButtonClickListener() != null) {
                    viewModel.getPositiveButtonClickListener()
                            .onClick(this, positiveButton, inputEditText, inputEditText.getText());
                }
            });
            if (viewModel.getPositiveButtonIconRes() != null) {
                positiveButton.setIconResource(viewModel.getPositiveButtonIconRes());
            }
        } else {
            positiveButton.setVisibility(View.GONE);
        }

        // Negative Button
        MaterialButton negativeButton = view.findViewById(R.id.input_negative_button);
        if (viewModel.getNegativeButtonText() != null && !viewModel.getNegativeButtonText().isEmpty()) {
            negativeButton.setVisibility(View.VISIBLE);
            negativeButton.setText(viewModel.getNegativeButtonText());
            negativeButton.setOnClickListener(v -> {
                if (viewModel.getNegativeButtonClickListener() != null) {
                    viewModel.getNegativeButtonClickListener()
                            .onClick(this, negativeButton, inputEditText, inputEditText.getText());
                }
            });
            if (viewModel.getNegativeButtonIconRes() != null) {
                negativeButton.setIconResource(viewModel.getNegativeButtonIconRes());
            }
        } else {
            negativeButton.setVisibility(View.GONE);
        }
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        viewModel.setRestore(true);
    }

    private void setViewModel() {
        viewModel.setTitle(title);
        viewModel.setOnBindEditTextCallback(onBindEditTextCallback);
        viewModel.setPositiveButtonText(positiveButtonText);
        viewModel.setPositiveButtonIconRes(positiveButtonIconRes);
        viewModel.setPositiveButtonClickListener(positiveButtonClickListener);
        viewModel.setNegativeButtonText(negativeButtonText);
        viewModel.setNegativeButtonIconRes(negativeButtonIconRes);
        viewModel.setNegativeButtonClickListener(negativeButtonClickListener);
    }

    /**
     * Set dialog title.
     *
     * Please execute set dialog title before {@code show()} this dialog.
     * @param titleId Dialog title string resource ID.
     * @return This dialog.
     */
    public ContentInputDialog setTitle(@StringRes int titleId) {
        this.title = ResourceUtil.getString(titleId);
        return this;
    }

    /**
     * Set dialog title.
     *
     * Please execute set dialog title before {@code show()} this dialog.
     * @param title Dialog title.
     * @return This dialog.
     */
    public ContentInputDialog setTitle(String title) {
        this.title = title;
        return this;
    }

    /**
     * Set OnBindEditTextCallback.
     *
     * Please execute set OnBindEditTextCallback before {@code show()} this dialog.
     * @param callback OnBindEditTextCallback instance.
     * @return This dialog.
     */
    public ContentInputDialog setOnBindEditTextCallback(OnBindEditTextCallback callback) {
        this.onBindEditTextCallback = callback;
        return this;
    }

    public ContentInputDialog setPositiveButton(
            @StringRes int textRes,
            @Nullable @DrawableRes Integer iconRes,
            @Nullable OnClickCallback listener) {
        return setPositiveButton(ResourceUtil.getString(textRes), iconRes, listener);
    }

    /**
     * Set positive button.
     *
     * Please execute set positive button before {@code show()} this dialog.
     * @param text Positive button text. If this param is {@code null} or is empty, this button
     * will not display on the dialog.
     * @param iconRes Positive button icon drawable resource. If this param is {@code null}, this
     * button will not display icon.
     * @param listener Positive button OnClickCallback. When user click positive button, this
     * callback will be executed.
     * @return This dialog.
     */
    public ContentInputDialog setPositiveButton(
            @Nullable String text,
            @Nullable @DrawableRes Integer iconRes,
            @Nullable OnClickCallback listener) {
        this.positiveButtonText = text;
        this.positiveButtonIconRes = iconRes;
        this.positiveButtonClickListener = listener;
        return this;
    }

    public ContentInputDialog setNegativeButton(
            @StringRes int textRes,
            @Nullable @DrawableRes Integer iconRes,
            @Nullable OnClickCallback listener) {
        return setNegativeButton(ResourceUtil.getString(textRes), iconRes, listener);
    }

    /**
     * Set negative button.
     *
     * Please execute set negative button before {@code show()} this dialog.
     * @param text Negative button text. If this param is {@code null} or is empty, this button
     * will not display on the dialog.
     * @param iconRes Negative button icon drawable resource. If this param is {@code null}, this
     * button will not display icon.
     * @param listener Negative button OnClickCallback. When user click negative button, this
     * callback will be executed.
     * @return This dialog.
     */
    public ContentInputDialog setNegativeButton(
            @Nullable String text,
            @Nullable @DrawableRes Integer iconRes,
            @Nullable OnClickCallback listener) {
        this.negativeButtonText = text;
        this.negativeButtonIconRes = iconRes;
        this.negativeButtonClickListener = listener;
        return this;
    }

    public interface OnClickCallback {
        void onClick(DialogFragment dialog, Button button, EditText editText, Editable input);
    }
}
