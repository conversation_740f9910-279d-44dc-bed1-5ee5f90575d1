package com.visionpay.opt.pax.emv.online.interchange.messages.responses;

import com.pax.commonlib.utils.ConvertUtils;
import com.visionpay.opt.pax.emv.online.interchange.messages.Message;

import java.util.Arrays;

public class LogonResponse extends Message {
    public byte[] RND1;
    public byte[] RND2;

    public LogonResponse(byte[] buffer) {
        super(buffer);
    }

    @Override
    protected byte[] internalGetMessage() {
        return mMessage;
    }

    @Override
    protected int internalParse(int i) {
        i = super.internalParse(i);

        this.RND1 = Arrays.copyOfRange(this.mMessage, i,i + 8);
        i += 8;
        this.RND2 = Arrays.copyOfRange(this.mMessage, i,i + 8);
        i += 8;

        return i;
    }
}
