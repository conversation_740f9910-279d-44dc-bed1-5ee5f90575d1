package com.visionpay.opt.pax.utils;

import androidx.annotation.NonNull;

import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.pax.commonlib.apppara.AppParaLoader;
import com.pax.commonlib.init.IModuleInit;
import com.pax.commonlib.utils.LogUtils;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IEmvParamService;
import com.pax.market.android.app.sdk.StoreSdk;
import com.pax.market.api.sdk.java.base.exception.NotInitException;
import com.pax.market.api.sdk.java.base.exception.ParseXMLException;
import com.sankuai.waimai.router.Router;

import org.apache.commons.io.FileUtils;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;

public class ParamUtils {

    public static LinkedHashMap<String, String> loadConfigParam(){
        String paraFiles = AppParaLoader.getPath();
        File f = new File(paraFiles + "config.p");
        if(f.exists()){
            return getParameters(f);
        }
        else{
            throw new RuntimeException("Parameter files do not exist.");
        }
    }

    @NonNull
    private static LinkedHashMap<String, String> getParameters(File parameterFile) {
        try {
            //parse the download parameter xml file for display.
            //todo call API to parse xml
            if (isJsonFile(parameterFile)) {
                return StoreSdk.getInstance().paramApi().parseDownloadParamJsonWithOrder(parameterFile);
            } else {
                return StoreSdk.getInstance().paramApi().parseDownloadParamXmlWithOrder(parameterFile);
            }
        } catch (JsonParseException e) {
            e.printStackTrace();
        } catch (NotInitException e) {
            e.printStackTrace();
            try {
                return getParametersXml(new FileInputStream(parameterFile));
            } catch (XmlPullParserException ex) {
                ex.printStackTrace();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        } catch (ParseXMLException e) {
            e.printStackTrace();
        }

        return null;
    }

    private static boolean isJsonFile(File parameterFile) {
        if (parameterFile == null) {
            return false;
        }
        try {
            String jsonStr = FileUtils.readFileToString(parameterFile, StandardCharsets.UTF_8);
            JsonElement jsonElement = (new JsonParser()).parse(jsonStr);
            return true;
        } catch (JsonSyntaxException e) {
            return false;
        } catch (IOException e1) {
            return false;
        }
    }

    public static void emvModuleInit() {
        IModuleInit emv = Router.getService(IModuleInit.class, ConfigServiceConstant.INIT_EMV);
        emv.init();
    }

    public static void emvParamInit() {
        IEmvParamService service = Router.getService(IEmvParamService.class, ConfigServiceConstant.CONFIGSERVICE_EMVPARAM);
        try {
            service.getCachedEmvParam();
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }

    public static LinkedHashMap<String, String> getParametersXml(InputStream stream) throws XmlPullParserException, IOException {
        XmlPullParserFactory factory = XmlPullParserFactory.newInstance();
        factory.setNamespaceAware(true);
        XmlPullParser xpp = factory.newPullParser();

        xpp.setInput(stream, null);
        int eventType = xpp.getEventType();
        String key = "";
        String value = "";
        LinkedHashMap<String, String> ret = new LinkedHashMap<>();
        while (eventType != XmlPullParser.END_DOCUMENT) {
            if (eventType == XmlPullParser.START_DOCUMENT) {
                //System.out.println("Start document");
            } else if (eventType == XmlPullParser.START_TAG) {
                //System.out.println("Start tag " + xpp.getName());
                key = xpp.getName();
            } else if (eventType == XmlPullParser.TEXT) {
                //System.out.println("Text " + xpp.getText());
                value = xpp.getText();
            } else if (eventType == XmlPullParser.END_TAG) {
                //System.out.println("End tag " + xpp.getName());
                if(!key.equals("") && !xpp.getName().equals("parameter")) {
                    ret.put(key, value);
                    key = "";
                    value = "";
                }
            }

            eventType = xpp.next();
        }
        return ret;
        //System.out.println("End document");
    }
}
