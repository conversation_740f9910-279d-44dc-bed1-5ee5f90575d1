/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/06                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.dialog;

import android.app.Dialog;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.bumptech.glide.Glide;
import com.visionpay.opt.pax.R;

/**
 * Remove Card Dialog.
 */
public class RemoveCardDialog extends BaseDialogFragment<RemoveCardDialog> {
    @Override
    protected int getContentLayoutRes() {
        return R.layout.dialog_prompt;
    }

    @Override
    protected void onCreateDialog(@NonNull Dialog dialog, @NonNull View view,
            @Nullable Window window, @Nullable Bundle savedInstanceState) {
        // Icon
        ImageView iconImageView = view.findViewById(R.id.dialog_prompt_icon);
        Glide.with(iconImageView).asGif()
                .load(R.drawable.icon_prompt)
                .into(iconImageView);

        // Title
        TextView titleTextView = view.findViewById(R.id.dialog_prompt_title);
        titleTextView.setText(R.string.remove_card);

        // Info
        TextView infoTextView = view.findViewById(R.id.dialog_prompt_info);
        infoTextView.setVisibility(View.GONE);
    }
}
