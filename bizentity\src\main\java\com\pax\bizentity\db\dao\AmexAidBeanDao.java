package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.clss.amex.AmexAidBean;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "amex_aid".
*/
public class AmexAidBeanDao extends AbstractDao<AmexAidBean, Long> {

    public static final String TABLENAME = "amex_aid";

    /**
     * Properties of entity AmexAidBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "amex_id");
        public final static Property AppName = new Property(1, String.class, "appName", false, "APP_NAME");
        public final static Property Aid = new Property(2, String.class, "aid", false, "AID");
        public final static Property SelFlag = new Property(3, byte.class, "selFlag", false, "SEL_FLAG");
        public final static Property TransLimit = new Property(4, long.class, "transLimit", false, "TRANS_LIMIT");
        public final static Property TransLimitFlag = new Property(5, byte.class, "transLimitFlag", false, "TRANS_LIMIT_FLAG");
        public final static Property FloorLimit = new Property(6, long.class, "floorLimit", false, "FLOOR_LIMIT");
        public final static Property FloorLimitFlag = new Property(7, byte.class, "floorLimitFlag", false, "FLOOR_LIMIT_FLAG");
        public final static Property CvmLimit = new Property(8, long.class, "cvmLimit", false, "CVM_LIMIT");
        public final static Property CvmLimitFlag = new Property(9, byte.class, "cvmLimitFlag", false, "CVM_LIMIT_FLAG");
        public final static Property TacDenial = new Property(10, String.class, "tacDenial", false, "TAC_DENIAL");
        public final static Property TacOnline = new Property(11, String.class, "tacOnline", false, "TAC_ONLINE");
        public final static Property TacDefault = new Property(12, String.class, "tacDefault", false, "TAC_DEFAULT");
        public final static Property AcquirerId = new Property(13, String.class, "acquirerId", false, "ACQUIRER_ID");
        public final static Property DDOL = new Property(14, String.class, "dDOL", false, "D_DOL");
        public final static Property TDOL = new Property(15, String.class, "tDOL", false, "T_DOL");
        public final static Property Version = new Property(16, String.class, "version", false, "VERSION");
        public final static Property TerminalType = new Property(17, String.class, "terminalType", false, "TERMINAL_TYPE");
        public final static Property TerminalCapability = new Property(18, String.class, "terminalCapability", false, "TERMINAL_CAPABILITY");
        public final static Property TerminalAdditionalCapability = new Property(19, String.class, "terminalAdditionalCapability", false, "TERMINAL_ADDITIONAL_CAPABILITY");
        public final static Property TermTransCap = new Property(20, String.class, "termTransCap", false, "TERM_TRANS_CAP");
        public final static Property UnRange = new Property(21, String.class, "unRange", false, "UN_RANGE");
        public final static Property SupportOptTrans = new Property(22, byte.class, "supportOptTrans", false, "SUPPORT_OPT_TRANS");
        public final static Property DelayAuthSupport = new Property(23, byte.class, "delayAuthSupport", false, "DELAY_AUTH_SUPPORT");
        public final static Property ExPayRdCap = new Property(24, String.class, "exPayRdCap", false, "EX_PAY_RD_CAP");
        public final static Property ExFunction = new Property(25, String.class, "exFunction", false, "EX_FUNCTION");
        public final static Property SupportFullOnline = new Property(26, byte.class, "supportFullOnline", false, "SUPPORT_FULL_ONLINE");
        public final static Property AucRFU = new Property(27, String.class, "aucRFU", false, "AUC_RFU");
    }


    public AmexAidBeanDao(DaoConfig config) {
        super(config);
    }
    
    public AmexAidBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"amex_aid\" (" + //
                "\"amex_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"APP_NAME\" TEXT," + // 1: appName
                "\"AID\" TEXT," + // 2: aid
                "\"SEL_FLAG\" INTEGER NOT NULL ," + // 3: selFlag
                "\"TRANS_LIMIT\" INTEGER NOT NULL ," + // 4: transLimit
                "\"TRANS_LIMIT_FLAG\" INTEGER NOT NULL ," + // 5: transLimitFlag
                "\"FLOOR_LIMIT\" INTEGER NOT NULL ," + // 6: floorLimit
                "\"FLOOR_LIMIT_FLAG\" INTEGER NOT NULL ," + // 7: floorLimitFlag
                "\"CVM_LIMIT\" INTEGER NOT NULL ," + // 8: cvmLimit
                "\"CVM_LIMIT_FLAG\" INTEGER NOT NULL ," + // 9: cvmLimitFlag
                "\"TAC_DENIAL\" TEXT," + // 10: tacDenial
                "\"TAC_ONLINE\" TEXT," + // 11: tacOnline
                "\"TAC_DEFAULT\" TEXT," + // 12: tacDefault
                "\"ACQUIRER_ID\" TEXT," + // 13: acquirerId
                "\"D_DOL\" TEXT," + // 14: dDOL
                "\"T_DOL\" TEXT," + // 15: tDOL
                "\"VERSION\" TEXT," + // 16: version
                "\"TERMINAL_TYPE\" TEXT," + // 17: terminalType
                "\"TERMINAL_CAPABILITY\" TEXT," + // 18: terminalCapability
                "\"TERMINAL_ADDITIONAL_CAPABILITY\" TEXT," + // 19: terminalAdditionalCapability
                "\"TERM_TRANS_CAP\" TEXT," + // 20: termTransCap
                "\"UN_RANGE\" TEXT," + // 21: unRange
                "\"SUPPORT_OPT_TRANS\" INTEGER NOT NULL ," + // 22: supportOptTrans
                "\"DELAY_AUTH_SUPPORT\" INTEGER NOT NULL ," + // 23: delayAuthSupport
                "\"EX_PAY_RD_CAP\" TEXT," + // 24: exPayRdCap
                "\"EX_FUNCTION\" TEXT," + // 25: exFunction
                "\"SUPPORT_FULL_ONLINE\" INTEGER NOT NULL ," + // 26: supportFullOnline
                "\"AUC_RFU\" TEXT);"); // 27: aucRFU
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"amex_aid\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, AmexAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
 
        String tacDenial = entity.getTacDenial();
        if (tacDenial != null) {
            stmt.bindString(11, tacDenial);
        }
 
        String tacOnline = entity.getTacOnline();
        if (tacOnline != null) {
            stmt.bindString(12, tacOnline);
        }
 
        String tacDefault = entity.getTacDefault();
        if (tacDefault != null) {
            stmt.bindString(13, tacDefault);
        }
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(14, acquirerId);
        }
 
        String dDOL = entity.getDDOL();
        if (dDOL != null) {
            stmt.bindString(15, dDOL);
        }
 
        String tDOL = entity.getTDOL();
        if (tDOL != null) {
            stmt.bindString(16, tDOL);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(17, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(18, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(19, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(20, terminalAdditionalCapability);
        }
 
        String termTransCap = entity.getTermTransCap();
        if (termTransCap != null) {
            stmt.bindString(21, termTransCap);
        }
 
        String unRange = entity.getUnRange();
        if (unRange != null) {
            stmt.bindString(22, unRange);
        }
        stmt.bindLong(23, entity.getSupportOptTrans());
        stmt.bindLong(24, entity.getDelayAuthSupport());
 
        String exPayRdCap = entity.getExPayRdCap();
        if (exPayRdCap != null) {
            stmt.bindString(25, exPayRdCap);
        }
 
        String exFunction = entity.getExFunction();
        if (exFunction != null) {
            stmt.bindString(26, exFunction);
        }
        stmt.bindLong(27, entity.getSupportFullOnline());
 
        String aucRFU = entity.getAucRFU();
        if (aucRFU != null) {
            stmt.bindString(28, aucRFU);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, AmexAidBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String appName = entity.getAppName();
        if (appName != null) {
            stmt.bindString(2, appName);
        }
 
        String aid = entity.getAid();
        if (aid != null) {
            stmt.bindString(3, aid);
        }
        stmt.bindLong(4, entity.getSelFlag());
        stmt.bindLong(5, entity.getTransLimit());
        stmt.bindLong(6, entity.getTransLimitFlag());
        stmt.bindLong(7, entity.getFloorLimit());
        stmt.bindLong(8, entity.getFloorLimitFlag());
        stmt.bindLong(9, entity.getCvmLimit());
        stmt.bindLong(10, entity.getCvmLimitFlag());
 
        String tacDenial = entity.getTacDenial();
        if (tacDenial != null) {
            stmt.bindString(11, tacDenial);
        }
 
        String tacOnline = entity.getTacOnline();
        if (tacOnline != null) {
            stmt.bindString(12, tacOnline);
        }
 
        String tacDefault = entity.getTacDefault();
        if (tacDefault != null) {
            stmt.bindString(13, tacDefault);
        }
 
        String acquirerId = entity.getAcquirerId();
        if (acquirerId != null) {
            stmt.bindString(14, acquirerId);
        }
 
        String dDOL = entity.getDDOL();
        if (dDOL != null) {
            stmt.bindString(15, dDOL);
        }
 
        String tDOL = entity.getTDOL();
        if (tDOL != null) {
            stmt.bindString(16, tDOL);
        }
 
        String version = entity.getVersion();
        if (version != null) {
            stmt.bindString(17, version);
        }
 
        String terminalType = entity.getTerminalType();
        if (terminalType != null) {
            stmt.bindString(18, terminalType);
        }
 
        String terminalCapability = entity.getTerminalCapability();
        if (terminalCapability != null) {
            stmt.bindString(19, terminalCapability);
        }
 
        String terminalAdditionalCapability = entity.getTerminalAdditionalCapability();
        if (terminalAdditionalCapability != null) {
            stmt.bindString(20, terminalAdditionalCapability);
        }
 
        String termTransCap = entity.getTermTransCap();
        if (termTransCap != null) {
            stmt.bindString(21, termTransCap);
        }
 
        String unRange = entity.getUnRange();
        if (unRange != null) {
            stmt.bindString(22, unRange);
        }
        stmt.bindLong(23, entity.getSupportOptTrans());
        stmt.bindLong(24, entity.getDelayAuthSupport());
 
        String exPayRdCap = entity.getExPayRdCap();
        if (exPayRdCap != null) {
            stmt.bindString(25, exPayRdCap);
        }
 
        String exFunction = entity.getExFunction();
        if (exFunction != null) {
            stmt.bindString(26, exFunction);
        }
        stmt.bindLong(27, entity.getSupportFullOnline());
 
        String aucRFU = entity.getAucRFU();
        if (aucRFU != null) {
            stmt.bindString(28, aucRFU);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public AmexAidBean readEntity(Cursor cursor, int offset) {
        AmexAidBean entity = new AmexAidBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // appName
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // aid
            (byte) cursor.getShort(offset + 3), // selFlag
            cursor.getLong(offset + 4), // transLimit
            (byte) cursor.getShort(offset + 5), // transLimitFlag
            cursor.getLong(offset + 6), // floorLimit
            (byte) cursor.getShort(offset + 7), // floorLimitFlag
            cursor.getLong(offset + 8), // cvmLimit
            (byte) cursor.getShort(offset + 9), // cvmLimitFlag
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // tacDenial
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // tacOnline
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // tacDefault
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // acquirerId
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // dDOL
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // tDOL
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // version
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // terminalType
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // terminalCapability
            cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19), // terminalAdditionalCapability
            cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20), // termTransCap
            cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21), // unRange
            (byte) cursor.getShort(offset + 22), // supportOptTrans
            (byte) cursor.getShort(offset + 23), // delayAuthSupport
            cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24), // exPayRdCap
            cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25), // exFunction
            (byte) cursor.getShort(offset + 26), // supportFullOnline
            cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27) // aucRFU
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, AmexAidBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setAppName(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAid(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSelFlag((byte) cursor.getShort(offset + 3));
        entity.setTransLimit(cursor.getLong(offset + 4));
        entity.setTransLimitFlag((byte) cursor.getShort(offset + 5));
        entity.setFloorLimit(cursor.getLong(offset + 6));
        entity.setFloorLimitFlag((byte) cursor.getShort(offset + 7));
        entity.setCvmLimit(cursor.getLong(offset + 8));
        entity.setCvmLimitFlag((byte) cursor.getShort(offset + 9));
        entity.setTacDenial(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setTacOnline(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setTacDefault(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setAcquirerId(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setDDOL(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setTDOL(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setVersion(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setTerminalType(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setTerminalCapability(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setTerminalAdditionalCapability(cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19));
        entity.setTermTransCap(cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20));
        entity.setUnRange(cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21));
        entity.setSupportOptTrans((byte) cursor.getShort(offset + 22));
        entity.setDelayAuthSupport((byte) cursor.getShort(offset + 23));
        entity.setExPayRdCap(cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24));
        entity.setExFunction(cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25));
        entity.setSupportFullOnline((byte) cursor.getShort(offset + 26));
        entity.setAucRFU(cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(AmexAidBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(AmexAidBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(AmexAidBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
