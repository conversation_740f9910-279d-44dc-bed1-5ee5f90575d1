package com.visionpay.opt.pax.emv.online.interchange.messages.responses;

import com.pax.commonlib.utils.ConvertUtils;
import com.visionpay.opt.pax.emv.online.interchange.messages.Message;

import java.util.Arrays;

public class ReversalResponse extends Message {

    public long internalID;
    public int STAN;
    public String RRN;
    public byte hostDecision;

    public ReversalResponse(byte[] buffer) {
        super(buffer);
    }

    @Override
    protected byte[] internalGetMessage(){
        return this.mMessage;
    }

    @Override
    protected int internalParse(int i){
        i = super.internalParse(i);
        //this.InternalID = (long)(this.mMessage[i++] << 24);
        //this.InternalID += (long)(this.mMessage[i++] << 16);
        //this.InternalID += (long)(this.mMessage[i++] << 8);
        //this.InternalID += (long)this.mMessage[i++];
        this.internalID = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 4));
        i += 4;
        //this.STAN = (int)(this.mMessage[i++] << 8);
        //this.STAN += (int)(this.mMessage[i++]);
        this.STAN = ConvertUtils.bcd2Int(Arrays.copyOfRange(this.mMessage, i, i + 2));
        i += 2;
        this.RRN = new String(Arrays.copyOfRange(this.mMessage, i, i + 12));
        i += 12;
        //this.HostResponse = new String(Arrays.copyOfRange(this.mMessage, i, i + 2));
        //i += 2;
        this.hostDecision = this.mMessage[i++];
        return i;
    }

    // --> 0x30=approved, 0x31=denied, 0x32=stolen card, 0x39=timeout
    public boolean isApproved(){
        return this.hostDecision == 0x30;
    }

    public boolean isTimeout(){
        return this.hostDecision == 0x39;
    }

    public boolean isStolenCard(){
        return this.hostDecision == 0x32;
    }

    public boolean isDecline(){
        return this.hostDecision == 0x31;
    }

    public String getGatwayResponse(){
        switch (this.hostDecision){
            case 0x030:
                return ResponseCodeHelper.getGatwayResponse("00");
            case 0x31:
                return ResponseCodeHelper.getGatwayResponse("12");
            case 0x32:
                return "Stolen Card";
            case 0x39:
                return "Timeout";
            default:
                return "";
        }
    }
}
