<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@android:color/holo_green_light">
    <item>
        <selector>
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/holo_green_light"/>
                    <corners android:radius="4dp"/>
                </shape>
            </item>

            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@android:color/holo_green_dark"/>
                    <corners android:radius="4dp"/>
                </shape>
            </item>
        </selector>
    </item>
</ripple>
