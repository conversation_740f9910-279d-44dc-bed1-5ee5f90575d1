/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/02/23                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.recyclerview.widget.RecyclerView;
import com.pax.commonlib.utils.ResourceUtil;
import com.visionpay.opt.pax.R;
import com.pax.preflib.fragment.ConfigBaseFragment;

/**
 * Base Config Fragment
 */
public abstract class EmvConfigBaseFragment extends ConfigBaseFragment {

    @Override
    public RecyclerView onCreateRecyclerView(LayoutInflater inflater, ViewGroup parent,
            Bundle savedInstanceState) {
        RecyclerView recyclerView = super.onCreateRecyclerView(inflater, parent, savedInstanceState);
        recyclerView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        recyclerView.setBackgroundResource(R.drawable.bg_card);
        ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) recyclerView.getLayoutParams();
        layoutParams.setMargins(
                ResourceUtil.getDimens(R.dimen.card_margin_horizontal),
                ResourceUtil.getDimens(R.dimen.card_spacing),
                ResourceUtil.getDimens(R.dimen.card_margin_horizontal),
                ResourceUtil.getDimens(R.dimen.scrollable_page_content_to_screen_bottom));
        recyclerView.setLayoutParams(layoutParams);
        return recyclerView;
    }
}
