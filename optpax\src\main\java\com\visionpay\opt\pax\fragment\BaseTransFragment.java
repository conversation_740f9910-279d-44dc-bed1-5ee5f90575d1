/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/25                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.fragment;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import com.visionpay.opt.pax.entity.TransStatus;
import com.visionpay.opt.pax.viewmodel.BaseTransCardViewModel;

/**
 * Base Transaction Fragment.
 */
public abstract class BaseTransFragment extends Fragment {
    private static final String TAG = "BaseTransFragment";
    protected TransStatus status;
    private BaseTransCardViewModel viewModel;

    /**
     * Get Fragment tag.
     *
     * @return Fragment tag
     */
    @NonNull
    public abstract String getFragmentTag();

    /**
     * Update trans status
     *
     * @param status TransStatus instance
     */
    public void updateStatus(TransStatus status) {
        this.status = status;
        if (viewModel != null) {
            viewModel.setStatus(status);
        }
    }

    protected void viewModelPrepared(BaseTransCardViewModel viewModel) {
        this.viewModel = viewModel;
        if (status != null) {
            viewModel.setStatus(status);
        }
    }
}
