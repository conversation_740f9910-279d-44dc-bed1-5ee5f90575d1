/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2021/11/16                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.activity;

import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.annotation.CallSuper;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.google.android.material.appbar.MaterialToolbar;
import com.pax.commonlib.application.BaseActivity;
import com.pax.commonlib.utils.LogUtils;

/**
 * This activity provide fragment back stack and Tool Bar refresh function.
 *
 * If you need more than one config activity, please extend this activity.
 */
public abstract class ConfigBaseActivity extends BaseActivity {
    protected final String TAG = this.getClass().getSimpleName();
    private static final String PATH_KEY = "PATH";
    private static final String FIRST_PATH_KEY = "FIRST_PATH";
    private static final String FIRST_TITLE_KEY = "FIRST_TITLE";

    protected MaterialToolbar toolbar;

    private String path;
    private String firstPath;
    private String firstTitle;

    @NonNull
    protected abstract MaterialToolbar getToolbar();

    protected abstract int getContainerId();

    protected abstract void setFragment(@Nullable String path, @Nullable Intent intent);

    protected Drawable getCustomHomeAsUpDrawable() {
        return null;
    }

    @CallSuper
    @Override
    protected void initViews() {
        toolbar = getToolbar();
        setSupportActionBar(toolbar);

        FragmentManager manager = getSupportFragmentManager();
        // Reset
        while (manager.getBackStackEntryCount() != 0) {
            LogUtils.d(TAG, "pop back stack");
            manager.popBackStackImmediate();
        }

        Intent intent = getIntent();
        newIntent(intent);

        firstPath = path;

        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            Drawable homeAsUpDrawable = getCustomHomeAsUpDrawable();
            if (homeAsUpDrawable != null) {
                actionBar.setHomeAsUpIndicator(homeAsUpDrawable);
            }
        }

        // Set action bar title
        manager.addOnBackStackChangedListener(() -> {
            int stackSize = manager.getBackStackEntryCount();
            String title;
            StringBuilder subTitle = new StringBuilder();
            if (stackSize <= 0) {
                title = firstTitle;
            } else {
                title = manager.getBackStackEntryAt(stackSize - 1).getName();
                if (stackSize > 1) {
                    for (int i = 0; i < stackSize - 1; i++) {
                        subTitle.append(manager.getBackStackEntryAt(i).getName());
                        if (i < stackSize - 2) {
                            subTitle.append(" > ");
                        }
                    }
                }
            }
            if (actionBar != null && title != null) {
                LogUtils.d(TAG, "action bar title: " + title);
                actionBar.setTitle(title);
                if (!subTitle.toString().isEmpty()) {
                    actionBar.setSubtitle(subTitle.toString());
                } else {
                    actionBar.setSubtitle(null);
                }
            }
        });
    }

    @Override
    protected void setListeners() {
    }

    @CallSuper
    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        newIntent(intent);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onKeyBackDown();
        }
        return true;
    }

    @Override
    protected boolean onKeyBackDown() {
        super.onBackPressed();
        return true;
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);

        outState.putString(PATH_KEY, path);
        outState.putString(FIRST_PATH_KEY, firstPath);
        outState.putString(FIRST_TITLE_KEY, firstTitle);
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        path = savedInstanceState.getString(PATH_KEY);
        firstPath = savedInstanceState.getString(FIRST_PATH_KEY);
        firstTitle = savedInstanceState.getString(FIRST_TITLE_KEY);
    }

    /**
     * Get Router path from intent and display fragment.
     *
     * @param intent Include path message
     */
    protected void newIntent(Intent intent) {
        // Get path
        if (intent != null && intent.getData() != null && intent.getData().getPath() != null) {
            path = intent.getData().getPath();
            LogUtils.d(TAG, "path: " + path);
        }

        // Set fragment
        setFragment(path, intent);
        getSupportFragmentManager().executePendingTransactions();

        // Set Toolbar
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setTitle(toolbar.getTitle());
        }
    }

    /**
     * Place Fragment on Activity.
     *
     * @param fragment Fragment
     * @param title Toolbar title
     */
    protected void placeFragment(@NonNull Fragment fragment, @NonNull String title) {
        placeFragment(getContainerId(), fragment, title, true);
    }

    /**
     * Place Fragment on Activity.
     *
     * @param containerId Container View Id
     * @param fragment Fragment
     * @param title Toolbar title
     * @param addToBackStack Need add fragment to back stack
     */
    protected void placeFragment(@IdRes int containerId, @NonNull Fragment fragment,
            @NonNull String title, boolean addToBackStack) {
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.replace(containerId, fragment);
        /*
         * The fragment started at the beginning should not be added to the return stack, otherwise
         * the screen will be blank when exiting the Activity
         */
        if (firstPath == null || path.equals(firstPath)) {
            firstTitle = title;
            ActionBar actionBar = getSupportActionBar();
            if (actionBar != null) {
                actionBar.setTitle(firstTitle);
            }
            transaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN);
        } else if (addToBackStack) {
            transaction.addToBackStack(title);
        }
        transaction.commit();
    }
}
