/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/03/28                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.emv;

import com.pax.emvservice.export.IEmvMifareService;
import com.visionpay.opt.pax.emv.device.EmvDeviceImpl;
import com.pax.bizentity.entity.SearchMode;
import com.pax.commonlib.currency.CurrencyConverter;
import com.pax.commonlib.utils.ConvertUtils;
import com.pax.commonlib.utils.LogUtils;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IEmvParamService;
import com.pax.emvbase.param.EmvProcessParam;
import com.pax.emvbase.param.EmvTransParam;
import com.pax.emvservice.export.EmvServiceConstant;
import com.pax.emvservice.export.IEmvContactService;
import com.pax.emvservice.export.IEmvContactlessService;
import com.pax.jemv.clcommon.RetCode;
import com.pax.jemv.device.DeviceManager;
import com.pax.poslib.gl.convert.ConvertHelper;
import com.pax.poslib.model.ModelInfo;
import com.sankuai.waimai.router.Router;

/**
 * EMV Pre Process
 */
public class EmvPreProcessService {
    private static final String TAG = "EmvPreProcessService";

    private final IEmvContactService emv = Router.getService(IEmvContactService.class, EmvServiceConstant.EMVSERVICE_CONTACT);
    private final IEmvContactlessService clss = Router.getService(IEmvContactlessService.class, EmvServiceConstant.EMVSERVICE_CONTACTLESS);
    private final IEmvMifareService mifare = Router.getService(IEmvMifareService.class, EmvServiceConstant.EMVSERVICE_MIFARE);
    private final String procCode;
    private final long amount;
    private final String dateTime;
    private final long traceNo;
    private byte searchCardMode;
    private final byte[] currencyCode;
    private final byte[] terminalId;

    public EmvPreProcessService(String procCode, long amount, String dateTime, long traceNo,
            byte searchCardMode, byte[] currencyCode, String terminalId) {
        this.procCode = procCode;
        this.amount = amount;
        this.dateTime = dateTime;
        this.traceNo = traceNo;
        this.searchCardMode = searchCardMode;
        this.currencyCode = currencyCode;
        this.terminalId = terminalId.getBytes();
    }

    /**
     * Start EMV Pre Process.
     *
     * @return Search card mode.
     */
    public byte start() {
        DeviceManager.getInstance().setIDevice(EmvDeviceImpl.getInstance());
        byte[] procCode = ConvertHelper.getConvert().strToBcdPaddingRight(this.procCode);
        if (!SearchMode.isSupportIcc(searchCardMode) && !SearchMode.isWave(searchCardMode) && !SearchMode.isSupportMag(searchCardMode)){
            return 0;
        }
        IEmvParamService service = Router.getService(IEmvParamService.class, ConfigServiceConstant.CONFIGSERVICE_EMVPARAM);
        EmvProcessParam cachedEmvParam = service.getCachedEmvParam();
        EmvTransParam.Builder builder = new EmvTransParam.Builder();
        builder.setTransType(procCode[0])
                .setAmount(amount)
                .setAmountOther(Long.parseLong("0"))//暂时不支持，写死为0
                .setTerminalID(this.terminalId)
                .setTransCurrencyCode(currencyCode)
                .setTransCurrencyExponent((byte) CurrencyConverter.getDigitsNum())
                //the format of transDate saved in transData is yyyyMMddHHmmss，but emv need YYMMDD format
                .setTransDate(ConvertUtils.strToBcdPaddingLeft(dateTime.substring(2, 8)))
                //emv need HHMMSS format
                .setTransTime(ConvertUtils.strToBcdPaddingLeft(dateTime.substring(8)))
                .setTransTraceNo(Long.parseLong(ConvertUtils.getPaddedNumber(traceNo, 6)))
                .setFlowType(EmvTransParam.FLOWTYPE_COMPLETE)
                .setMaskPattern("")
                .setPinLenSet( "0,4,5,6,7,8,9,10,11,12\0".getBytes())
                .setPciTimeout(60 * 1000);
        EmvProcessParam.Builder processParamBuilder = new EmvProcessParam.Builder()
                .setTermConfig(cachedEmvParam.getTermConfig())
                .setCapkParam(cachedEmvParam.getCapkParam());
        // ICC
        if (SearchMode.isSupportIcc(searchCardMode)){
            builder.setPciMode((byte) 1);
            processParamBuilder.setEmvTransParam(builder.create())
                    .setEmvAidList(cachedEmvParam.getEmvAidList());
            int contactRet = emv.preTransProcess(processParamBuilder.create());
            if (contactRet != RetCode.EMV_OK){
                LogUtils.e(TAG, "contact pre process failed!!!");
                searchCardMode = (byte) (searchCardMode & (~SearchMode.INSERT));
            }
        }
        // PICC
        if (SearchMode.isSupportInternalPicc(searchCardMode) ||
                SearchMode.isSupportExternalPicc(searchCardMode)){
            // Only PICC
            if (!SearchMode.isSupportIcc(searchCardMode)) {
                processParamBuilder.setEmvTransParam(builder.create());
            }
            processParamBuilder.setAmexParam(cachedEmvParam.getAmexParam())
                    .setPassParam(cachedEmvParam.getPayPassParam())
                    .setPayWaveParam(cachedEmvParam.getPayWaveParam())
                    .setDpasParam(cachedEmvParam.getDpasParam())
                    .setEFTParam(cachedEmvParam.getEftParam())
                    .setJcbParam(cachedEmvParam.getJcbParam())
                    .setMirParam(cachedEmvParam.getMirParam())
                    .setPbocParam(cachedEmvParam.getPbocParam())
                    .setPureParam(cachedEmvParam.getPureParam())
                    .setRuPayParam(cachedEmvParam.getRuPayParam());
            int contactlessRet = clss.preTransProcess(processParamBuilder.create());
            if (contactlessRet != RetCode.EMV_OK){
                LogUtils.e(TAG, "contactless pre process failed!!!");
                searchCardMode = (byte) (searchCardMode & (~SearchMode.WAVE));
            }
        }
        // MIFARE
        if (SearchMode.isSupportInternalMifare(searchCardMode)){
            builder.setBlockNum(4);
            builder.setMifarePwd(ConvertHelper.getConvert().strToBcdPaddingRight("436f6d706163"));
            processParamBuilder.setEmvTransParam(builder.create());
            int mifareRet = mifare.preTransProcess(processParamBuilder.create());
            if (mifareRet != RetCode.EMV_OK){
                LogUtils.e(TAG, "mifare pre process failed!!!");
                searchCardMode = (byte) (searchCardMode & (~SearchMode.INTERNAL_MIFARE));
            }
        }
        return searchCardMode;
    }
}
