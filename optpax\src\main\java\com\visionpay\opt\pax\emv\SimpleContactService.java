package com.visionpay.opt.pax.emv;
import android.os.ConditionVariable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.pax.commonlib.utils.LogUtils;
import com.pax.dal.entity.EPiccType;
import com.pax.emvbase.process.ISendingOnlineCallback;
import com.pax.emvbase.utils.EmvDebugger;
import com.pax.jemv.device.DeviceManager;
import com.visionpay.opt.pax.app.App;
import com.visionpay.opt.pax.emv.contactless.ContactlessService;
import com.visionpay.opt.pax.emv.device.EmvDeviceImpl;
import com.visionpay.opt.pax.entity.CvmType;

import java.util.LinkedList;
import java.util.List;
/**
 * Contact EMV Service.
 */
public class SimpleContactService implements IEmvService<ISimpleEmvService> {
    private static final String TAG = "SimpleContactService";

    private ConditionVariable cv = new ConditionVariable();
    private ConfirmCallback confirmCallback;
    private ErrorCallback errorCallback;
    private CompleteCallback completeCallback;
    private ContactlessService.RemoveCardCallback removeCardCallback;

    @NonNull
    @Override
    public SimpleContactService start(@NonNull ISimpleEmvService emv, long amount, String terminalId, int detectResult, String reference) {
        LogUtils.d(TAG, "============ Start Contact EMV Service ============");
        App.getApp().runInBackground(() -> {
            List<CvmType> cvmTypeList = new LinkedList<>();
            try {
                DeviceManager.getInstance().setIDevice(EmvDeviceImpl.getInstance());
                int ret = emv.startTransProcess(detectResult, new SimpleContactProcessCallback(emv, cv, detectResult)
                        .setRemoveCardCallback(removeCardCallback)
                        .setConfirmCallback(confirmCallback)
                        .setErrorCallback(errorCallback)
                        .setCvmTypeList(cvmTypeList));
                LogUtils.d(TAG, "Emv ret: " + ret);
            } catch (Exception e) {
                LogUtils.e(TAG, "Emv service error", e);
                if (errorCallback != null) {
                    errorCallback.onError("Unexpected error",
                            "EMV service throw a "
                                    + e.getClass().getSimpleName()
                                    + ". Please check log.");
                }
            } finally {
                LogUtils.d(TAG, "============ Stop Search Card ============");
                closeMag();
                closeIcc();
                closeInternalPicc();
                closeExternalPicc();

                EmvDebugger.setCustomDebugger(null);

                completeCallback.onComplete(false, null, null, null);
            }
        });
        return this;
    }

    @NonNull
    @Override
    public SimpleContactService setConfirmCallback(@Nullable ConfirmCallback callback) {
        this.confirmCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public SimpleContactService setErrorCallback(@Nullable ErrorCallback callback) {
        this.errorCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public SimpleContactService setCompleteCallback(@Nullable CompleteCallback callback) {
        this.completeCallback = callback;
        return this;
    }

    @NonNull
    @Override
    public IEmvService<ISimpleEmvService> setSendinfOnlineCallback(@Nullable ISendingOnlineCallback callback) {
        return this;
    }
    @NonNull
    public SimpleContactService setRemoveCardCallback(@Nullable ContactlessService.RemoveCardCallback callback) {
        this.removeCardCallback = callback;
        return this;
    }

    private void closeMag() {
        try {
            App.getDal().getMag().close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close mag failed", e);
        }
    }

    private void closeIcc() {
        try {
            App.getDal().getIcc().close((byte) 0);
        } catch (Exception e) {
            LogUtils.e(TAG, "close icc failed", e);
        }
    }

    private void closeInternalPicc() {
        try {
            App.getDal().getPicc(EPiccType.INTERNAL).close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close internal picc failed", e);
        }
    }

    private void closeExternalPicc() {
        try {
            App.getDal().getPicc(EPiccType.EXTERNAL).close();
        } catch (Exception e) {
            LogUtils.e(TAG, "close external picc failed", e);
        }
    }
}
