/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/13                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import android.content.Context;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;
import androidx.preference.PreferenceScreen;
import com.google.android.material.snackbar.Snackbar;
import com.visionpay.opt.pax.config.datastore.PbocAidDataStore;
import com.visionpay.opt.pax.consts.EmvRouterConst;
import com.pax.bizentity.db.helper.PBOCAidDbHelper;
import com.visionpay.opt.pax.R;
import com.pax.preflib.builder.AmountEditTextPrefBuilder;
import com.pax.preflib.builder.StringEditTextPrefBuilder;
import com.sankuai.waimai.router.annotation.RouterService;

/**
 * PBOC contactless AID content page
 *
 * Settings -> Param -> PBOC Kernel Param -> AID
 */
@RouterService(interfaces = EmvParamBaseFragment.class, key = EmvRouterConst.CONFIG_PARAM_CLSS_PBOC_AID)
public class EmvPbocAidContentFragment extends EmvParamBaseFragment {
    private String aid;

    @Override
    public void setParam(String param) {
        aid = param;
    }

    @NonNull
    @Override
    public String getFragmentTitle() {
        return aid;
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        PreferenceManager manager = getPreferenceManager();
        if (manager != null) {
            manager.setPreferenceDataStore(new PbocAidDataStore(PBOCAidDbHelper.getInstance().findAID(aid))
                    .setRefreshCachedParamCallback(() -> Snackbar
                            .make(getListView(), "Need Refresh Cached EMV Param", Snackbar.LENGTH_LONG)
                            .setAction("REFRESH", v -> refreshCachedParam())
                            .show()));
            Context context = manager.getContext();
            PreferenceScreen screen = manager.createPreferenceScreen(context);

            // APP Name
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.APP_NAME, R.string.config_param_app_name)
                    .buildAndApply(source -> source.setSelectable(false)));

            // Trans limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.TRANS_LIMIT, R.string.config_param_trans_limit)
                    .build());

            // Floor limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.FLOOR_LIMIT, R.string.config_param_floor_limit)
                    .build());

            // CVM limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.CVM_LIMIT, R.string.config_param_cvm_limit)
                    .build());

            // QPS limit
            screen.addPreference(AmountEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.QPS_LIMIT, R.string.config_param_qps_limit)
                    .build());

            // Terminal Capability
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.TERM_CAP, R.string.config_param_term_cap)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 6))
                    .build());

            // Terminal Additional Capability
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.ADD_TERM_CAP, R.string.config_param_term_add_cap)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 10))
                    .build());

            // TTQ
            screen.addPreference(StringEditTextPrefBuilder
                    .newInstance(context, PbocAidDataStore.TTQ, R.string.config_param_ttq)
                    .addPrefChangedCallback((preference, newValue) ->
                            verifyHexString(newValue, 8))
                    .build());

            setPreferenceScreen(screen);
        }
    }
}
