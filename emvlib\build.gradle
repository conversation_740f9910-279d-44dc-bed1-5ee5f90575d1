apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.minSdkVersion
        targetSdkVersion rootProject.targetSdkVersion
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        release {
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        stagingRelease {
            minifyEnabled false
            //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        autoTest{

        }
        debug{

        }
    }
}

repositories {
    flatDir {
        dirs '../poslib/libs', 'libs'
    }
    mavenCentral()
}

dependencies {
    api fileTree(include: ['*.jar'], dir: 'libs')
    api project(":emvbase")

    // Whether to use DPAS connect 2.0 or not.
    // 是否使用 DPAS connect 2.0
    api project(":emvlib:dpas")
//    api project(":emvlib:dpas2")
    api project(':poslib')
    
    annotationProcessor "io.github.meituan-dianping:compiler:$rootProject.routerCompiler"
    testImplementation "junit:junit:$rootProject.junit"
    testImplementation "org.mockito:mockito-all:$rootProject.mockito"
    testImplementation "org.hamcrest:hamcrest-all:$rootProject.hamcrest"
    testImplementation "androidx.arch.core:core-testing:$rootProject.coreTesting"
    androidTestImplementation "androidx.test:runner:$rootProject.runner"
    androidTestImplementation "androidx.test.espresso:espresso-core:$rootProject.espresso"
    androidTestImplementation "junit:junit:$rootProject.junit"
}
