package com.pax.bizentity.db.dao;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import com.pax.bizentity.entity.CapkRevokeBean;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "capk_revoke".
*/
public class CapkRevokeBeanDao extends AbstractDao<CapkRevokeBean, Long> {

    public static final String TABLENAME = "capk_revoke";

    /**
     * Properties of entity CapkRevokeBean.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property RID = new Property(1, String.class, "rID", false, "R_ID");
        public final static Property KeyID = new Property(2, String.class, "keyID", false, "KEY_ID");
        public final static Property CertificateSN = new Property(3, String.class, "certificateSN", false, "CERTIFICATE_SN");
    }


    public CapkRevokeBeanDao(DaoConfig config) {
        super(config);
    }
    
    public CapkRevokeBeanDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"capk_revoke\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"R_ID\" TEXT," + // 1: rID
                "\"KEY_ID\" TEXT," + // 2: keyID
                "\"CERTIFICATE_SN\" TEXT);"); // 3: certificateSN
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"capk_revoke\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, CapkRevokeBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String rID = entity.getRID();
        if (rID != null) {
            stmt.bindString(2, rID);
        }
 
        String keyID = entity.getKeyID();
        if (keyID != null) {
            stmt.bindString(3, keyID);
        }
 
        String certificateSN = entity.getCertificateSN();
        if (certificateSN != null) {
            stmt.bindString(4, certificateSN);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, CapkRevokeBean entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String rID = entity.getRID();
        if (rID != null) {
            stmt.bindString(2, rID);
        }
 
        String keyID = entity.getKeyID();
        if (keyID != null) {
            stmt.bindString(3, keyID);
        }
 
        String certificateSN = entity.getCertificateSN();
        if (certificateSN != null) {
            stmt.bindString(4, certificateSN);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public CapkRevokeBean readEntity(Cursor cursor, int offset) {
        CapkRevokeBean entity = new CapkRevokeBean( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // rID
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // keyID
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3) // certificateSN
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, CapkRevokeBean entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setRID(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setKeyID(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setCertificateSN(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
     }
    
    @Override
    protected final Long updateKeyAfterInsert(CapkRevokeBean entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(CapkRevokeBean entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(CapkRevokeBean entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
