/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/07                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.config.fragment;

import androidx.annotation.IntRange;
import androidx.annotation.Nullable;
import com.google.android.material.snackbar.Snackbar;
import com.pax.configservice.export.ConfigServiceConstant;
import com.pax.configservice.export.IEmvParamService;
import com.sankuai.waimai.router.Router;

/**
 * Base EMV param content page
 */
public abstract class EmvParamBaseFragment extends EmvConfigBaseFragment {
    public abstract void setParam(String param);

    /**
     * Verify Hex String
     *
     * @param value String value. If value is null or empty, return false.
     * @return Pass or not.
     */
    protected boolean verifyHexString(@Nullable String value) {
        return verifyHexString(false, value);
    }

    /**
     * Verify Hex String
     *
     * @param value String value. If value is null or empty, return false.
     * @param length HEX String length.
     * @return Pass or not.
     */
    protected boolean verifyHexString(@Nullable String value, @IntRange(from = 0) int length) {
        return verifyHexString(false, value, length);
    }

    /**
     * Verify Hex String
     *
     * @param allowEmpty Whether allow empty value or not.
     * @param value String value.
     * @return Pass or not.
     */
    protected boolean verifyHexString(boolean allowEmpty, @Nullable String value) {
        return verifyHexString(allowEmpty, value, 0);
    }

    /**
     * Verify Hex String
     *
     * @param allowEmpty Whether allow empty value or not.
     * @param value String value.
     * @param length HEX String length.
     * @return Pass or not.
     */
    protected boolean verifyHexString(
            boolean allowEmpty,
            @Nullable String value,
            @IntRange(from = 0) int length) {
        if (value == null || value.isEmpty()) {
            return allowEmpty;
        }
        StringBuilder regex = new StringBuilder("^[\\dABCDEFabcdef]");
        if (length > 0) {
            regex.append("{")
                    .append(length)
                    .append("}");
        } else {
            regex.append("+");
        }
        regex.append("$");
        return value.matches(regex.toString());
    }

    protected void refreshCachedParam() {
        IEmvParamService service = Router.getService(IEmvParamService.class, ConfigServiceConstant.CONFIGSERVICE_EMVPARAM);
        service.invalidCachedEmvParam();
        Snackbar.make(getListView(), "Refresh Done.", Snackbar.LENGTH_LONG).show();
    }
}
