package com.visionpay.opt.pax.message;

import android.os.Bundle;
import android.os.Message;
import android.os.Messenger;
import android.os.RemoteException;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pax.poslib.neptune.UartComm;
import com.pax.poslib.neptune.UartCommListener;
import com.visionpay.opt.pax.utils.EnumTypeAdapterFactory;

import java.util.HashSet;
import java.util.Set;

public class BroadcastMesseger implements UartCommListener {

    private static final String KEY_MESSAGE = "key_message";

    private static final Set<Class<?>> WRAPPER_TYPES = getWrapperTypes();

    private static BroadcastMesseger instance;

    private BroadcastMessegerListener listener;
    private Gson gson;

    private Message msg = null;
    private Messenger replyTo;
    private boolean isRunning = false;

    private BroadcastMesseger(){
        gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd HH:mm:ss.SSS")
                .registerTypeAdapterFactory(new EnumTypeAdapterFactory()).create();
    }

    public static BroadcastMesseger getInstance(){
        if(instance == null)
            instance = new BroadcastMesseger();

        return instance;
    }

    public String setMessage(Message msg){
        this.msg = msg;
        this.replyTo = msg.replyTo;

        Bundle bundle = (Bundle) msg.obj;
        return bundle.getString(KEY_MESSAGE);
    }

    public boolean isInternalMessage(){
        return this.msg != null;
    }

    public <T> void send(T message){
        String strMsg;
        if(!isWrapperType(message.getClass()))
            strMsg = gson.toJson(message);
        else
            strMsg = message.toString();

        if(this.msg == null) {
            UartComm.getInstance().send(strMsg.getBytes());
        }
        else {
            //Setup the reply message
            Bundle bundle = new Bundle();
            bundle.putString(KEY_MESSAGE, strMsg);
            Message bcMessage = Message.obtain(null, 2, 0, 0, bundle);
            try
            {
                //make the RPC invocation
                this.replyTo.send(bcMessage);
            }
            catch(RemoteException rme)
            {
                throw new RuntimeException(rme);
            }
        }
    }

    public void start(BroadcastMessegerListener listener){
        this.listener = listener;
        this.isRunning = true;
        if(this.msg == null) {
            UartComm.getInstance().start(this);
        }
    }

    public void stop(){
        this.isRunning = false;
        this.listener = null;
        if(this.msg == null) {
            UartComm.getInstance().stop();
        }
    }

    public boolean receive(String message){
        if(listener != null && isRunning) {
            listener.OnMessageReceive(message);
            return true;
        }
        return false;
    }

    @Override
    public void OnCommMessageReceive(String message) {
        receive(message);
    }

    private static boolean isWrapperType(Class<?> clazz)
    {
        return WRAPPER_TYPES.contains(clazz);
    }

    private static Set<Class<?>> getWrapperTypes()
    {
        Set<Class<?>> ret = new HashSet<Class<?>>();
        ret.add(Boolean.class);
        ret.add(Character.class);
        ret.add(Byte.class);
        ret.add(Short.class);
        ret.add(Integer.class);
        ret.add(Long.class);
        ret.add(Float.class);
        ret.add(Double.class);
        ret.add(Void.class);
        ret.add(String.class);
        return ret;
    }
}
