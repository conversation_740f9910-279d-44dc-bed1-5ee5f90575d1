/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/01/19                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.pax.preflib.builder.factory;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.preference.EditTextPreference;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceCategory;
import androidx.preference.SwitchPreference;
import com.pax.preflib.builder.pref.BaseAmountDialogPreference;
import com.pax.preflib.builder.pref.BaseSeekBarDialogPreference;
import com.sankuai.waimai.router.Router;

/**
 * Preference Factory.
 */
public class PrefFactory implements IPreferenceFactory {
    private IPreferenceFactory preferenceFactory = Router.getService(IPreferenceFactory.class,
            PrefFactoryConst.DEFAULT);

    private PrefFactory() {}

    private static class Holder {
        private static final PrefFactory INSTANCE = new PrefFactory();
    }

    public static PrefFactory getInstance() {
        return Holder.INSTANCE;
    }

    public void setFactory(String factoryConst) {
        preferenceFactory = Router.getService(IPreferenceFactory.class, factoryConst);
    }

    public void setFactory(IPreferenceFactory factory) {
        preferenceFactory = factory;
    }

    @NonNull
    @Override
    public BaseAmountDialogPreference createAmountEditTextDialogPreference(@NonNull Context context) {
        return preferenceFactory.createAmountEditTextDialogPreference(context);
    }

    @NonNull
    @Override
    public EditTextPreference createEditTextPreference(@NonNull Context context) {
        return preferenceFactory.createEditTextPreference(context);
    }

    @NonNull
    @Override
    public ListPreference createListPreference(@NonNull Context context) {
        return preferenceFactory.createListPreference(context);
    }

    @NonNull
    @Override
    public Preference createPreference(@NonNull Context context) {
        return preferenceFactory.createPreference(context);
    }

    @NonNull
    @Override
    public BaseSeekBarDialogPreference createSeekBarDialogPreference(@NonNull Context context) {
        return preferenceFactory.createSeekBarDialogPreference(context);
    }

    @NonNull
    @Override
    public SwitchPreference createSwitchPreference(@NonNull Context context) {
        return preferenceFactory.createSwitchPreference(context);
    }

    @NonNull
    @Override
    public PreferenceCategory createPreferenceCategory(@NonNull Context context) {
        return preferenceFactory.createPreferenceCategory(context);
    }
}
