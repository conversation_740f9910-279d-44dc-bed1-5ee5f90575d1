package com.visionpay.opt.pax.emv.online.interchange.messages;

import androidx.annotation.NonNull;

import com.visionpay.opt.pax.emv.online.interchange.enums.MessageDirection;
import com.visionpay.opt.pax.emv.online.interchange.enums.MessageSource;

public abstract class BaseMessage {
    public MessageDirection mDirection;
    public MessageSource mSource;

    @NonNull
    @Override
    public String toString(){
        String direction;
        switch (mSource)
        {
            case POS:
                direction = "[POS]";
                break;
            case M7:
                direction = "[M7]";
                break;
            case PSS5000:
                direction = "[PSS5000]";
                break;
            case PEPS:
                direction = "[PEPS]";
                break;
            case INTERCHANGE:
                direction = "[SWITCH]";
                break;
            default:
                direction = "[UNKNOWN]";
                break;
        }
        direction += (mDirection == MessageDirection.IN ? " <-- " : " --> ");
        return direction;
    }

    public boolean isSensitive()
    {
        return true;
    }
}