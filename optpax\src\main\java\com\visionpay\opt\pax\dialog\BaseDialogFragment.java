/*
 * ===========================================================================================
 * = COPYRIGHT
 *          PAX Computer Technology(Shenzhen) CO., LTD PROPRIETARY INFORMATION
 *   This software is supplied under the terms of a license agreement or nondisclosure
 *   agreement with PAX Computer Technology(Shenzhen) CO., LTD and may not be copied or
 *   disclosed except in accordance with the terms in that agreement.
 *     Copyright (C) 2021-? PAX Computer Technology(Shenzhen) CO., LTD All rights reserved.
 * Description: // Detail description about the function of this module,
 *             // interfaces with the other modules, and dependencies.
 * Revision History:
 * Date                           Author                      Action
 * 2022/04/02                     YeHongbo                    Create
 * ===========================================================================================
 */

package com.visionpay.opt.pax.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import com.pax.commonlib.application.ActivityStack;
import com.visionpay.opt.pax.R;

/**
 * Base DialogFragment
 */
public abstract class BaseDialogFragment<T extends BaseDialogFragment<T>> extends DialogFragment {
    private static final String TAG = "BaseDialogFragment";

    private DialogInterface.OnDismissListener onDismissListener;
    private boolean isDialogCancelable = false;
    private boolean isTouchOutsideCancelable = false;

    @LayoutRes
    protected abstract int getContentLayoutRes();

    protected abstract void onCreateDialog(@NonNull Dialog dialog, @NonNull View view,
            @Nullable Window window, @Nullable Bundle savedInstanceState);

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        View view = View.inflate(getContext(), getContentLayoutRes(), null);

        // Use AlertDialog.Builder to create dialog instance.
        // The advantage of this is that AlertDialog already provides a dialog optimized for large
        // or landscape screens, so using AlertDialog directly does not require us to make special
        // adaptations.
        // 使用 AlertDialog.Builder 来创建 Dialog 实例
        // 这样做的好处是，AlertDialog 已经提供了一个针对大屏幕或者横屏优化的对话框，因此直接使用 AlertDialog
        // 就不需要我们再去做专门适配了。
        Dialog dialog = new AlertDialog.Builder(ActivityStack.getInstance().top())
                .setCancelable(isDialogCancelable)
                .setView(view)
                .create();

        dialog.setCanceledOnTouchOutside(isTouchOutsideCancelable);

        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(R.drawable.bg_dialog);
        }

        onCreateDialog(dialog, view, window, savedInstanceState);

        return dialog;
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (onDismissListener != null) {
            onDismissListener.onDismiss(dialog);
        }
    }

    /**
     * Set OnDismissListener.
     *
     * Please execute set dialog dismiss listener before dismiss this dialog.
     * @param listener OnDismissListener
     * @return This dialog.
     */
    public T setOnDismissListener(DialogInterface.OnDismissListener listener) {
        this.onDismissListener = listener;
        return (T) this;
    }

    /**
     * Set dialog cancelable.
     *
     * @param dialogCancelable Whether this dialog can be canceled.
     * @return
     */
    public T setDialogCancelable(boolean dialogCancelable) {
        isDialogCancelable = dialogCancelable;
        return (T) this;
    }

    public T setTouchOutsideCancelable(boolean touchOutsideCancelable) {
        isTouchOutsideCancelable = touchOutsideCancelable;
        return (T) this;
    }

    public void show(@NonNull Activity activity, @Nullable String tag) {
        if (activity instanceof FragmentActivity) {
            FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
            show(manager, tag);
        }
    }
}
